import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import { processStripeWebhookForHistory } from '@/lib/payments/payment-history'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 Webhook Stripe Unificado recebido')
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { message: 'Missing stripe signature' },
        { status: 400 }
      )
    }

    // Buscar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const webhookSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeWebhookSecret' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
    const webhookSecret = webhookSecretSetting?.value || process.env.STRIPE_WEBHOOK_SECRET

    if (!stripeSecretKey || !webhookSecret) {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretKey)

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Erro na verificação do webhook:', err)
      return NextResponse.json(
        { message: 'Webhook inválido' },
        { status: 400 }
      )
    }

    console.log('📨 Evento recebido:', event.type)

    // Processar diferentes tipos de eventos
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break

      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break

      default:
        console.log(`Evento não processado: ${event.type}`)
    }

    // Processar para histórico de pagamentos
    await processStripeWebhookForHistory(event)

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Erro no webhook unificado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Processar PaymentIntent bem-sucedido (principalmente Multibanco)
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('🎉 Processando payment_intent.succeeded:', paymentIntent.id)

    const metadata = paymentIntent.metadata
    const type = metadata.type

    console.log('Metadata do PaymentIntent:', metadata)

    // Determinar tipo de pagamento baseado nos metadados
    if (type?.includes('subscription')) {
      await handleSubscriptionPayment(paymentIntent, metadata)
    } else if (type?.includes('marketplace')) {
      await handleMarketplacePayment(paymentIntent, metadata)
    } else if (type?.includes('repair')) {
      await handleRepairPayment(paymentIntent, metadata)
    } else {
      console.log('Tipo de pagamento não identificado, tentando buscar por PaymentIntent ID')
      await handleGenericPayment(paymentIntent)
    }

  } catch (error) {
    console.error('Erro ao processar payment_intent.succeeded:', error)
  }
}

// Processar Checkout Session completada (principalmente cartão)
async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('🎉 Processando checkout.session.completed:', session.id)

    const metadata = session.metadata
    const type = metadata?.type

    console.log('Metadata da Session:', metadata)

    // Determinar tipo de pagamento baseado nos metadados
    if (type?.includes('subscription')) {
      await handleSubscriptionCheckout(session, metadata)
    } else if (type?.includes('marketplace')) {
      await handleMarketplaceCheckout(session, metadata)
    } else if (type?.includes('repair')) {
      await handleRepairCheckout(session, metadata)
    } else {
      console.log('Tipo de checkout não identificado')
    }

  } catch (error) {
    console.error('Erro ao processar checkout.session.completed:', error)
  }
}

// Processar pagamento de subscrição
async function handleSubscriptionPayment(paymentIntent: Stripe.PaymentIntent, metadata: any) {
  const subscriptionId = metadata.subscriptionId || metadata.relatedId
  const paymentId = metadata.paymentId

  if (!subscriptionId) {
    console.log('SubscriptionId não encontrado nos metadados')
    return
  }

  // Buscar pagamento pendente
  const pendingPayment = await prisma.subscriptionPayment.findFirst({
    where: {
      OR: [
        { id: paymentId },
        { stripePaymentIntentId: paymentIntent.id },
        { subscriptionId, status: 'PENDING' }
      ]
    },
    include: { subscription: true }
  })

  if (pendingPayment) {
    // Atualizar pagamento
    await prisma.subscriptionPayment.update({
      where: { id: pendingPayment.id },
      data: {
        status: 'COMPLETED',
        paidAt: new Date()
      }
    })

    // Ativar subscrição
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: { status: 'ACTIVE' }
    })

    // Atualizar referência Multibanco
    await prisma.multibancoReference.updateMany({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: { status: 'PAID', paidAt: new Date() }
    })

    console.log(`✅ Subscrição ${subscriptionId} ativada via PaymentIntent`)
  }
}

// Processar pagamento de marketplace
async function handleMarketplacePayment(paymentIntent: Stripe.PaymentIntent, metadata: any) {
  const orderIds = metadata.orderIds?.split(',') || []
  const paymentId = metadata.paymentId

  if (orderIds.length === 0) {
    console.log('OrderIds não encontrados nos metadados')
    return
  }

  // Buscar pagamento pendente
  const pendingPayment = await prisma.payment.findFirst({
    where: {
      OR: [
        { id: paymentId },
        { stripeSessionId: paymentIntent.id },
        { orderId: { in: orderIds }, status: 'PENDING' }
      ]
    }
  })

  if (pendingPayment) {
    // Atualizar pagamento
    await prisma.payment.update({
      where: { id: pendingPayment.id },
      data: {
        status: 'COMPLETED',
        method: 'multibanco'
      }
    })

    // Atualizar encomendas
    await prisma.order.updateMany({
      where: { id: { in: orderIds } },
      data: { status: 'CONFIRMED' }
    })

    // Atualizar referência Multibanco
    await prisma.multibancoReference.updateMany({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: { status: 'PAID', paidAt: new Date() }
    })

    console.log(`✅ Encomendas ${orderIds.join(', ')} confirmadas via PaymentIntent`)
  }
}

// Processar pagamento de reparação
async function handleRepairPayment(paymentIntent: Stripe.PaymentIntent, metadata: any) {
  const repairId = metadata.repairId
  const paymentId = metadata.paymentId

  if (!repairId) {
    console.log('RepairId não encontrado nos metadados')
    return
  }

  // Buscar pagamento pendente
  const pendingPayment = await prisma.payment.findFirst({
    where: {
      OR: [
        { id: paymentId },
        { stripeSessionId: paymentIntent.id },
        { repairId, status: 'PENDING' }
      ]
    }
  })

  if (pendingPayment) {
    // Atualizar pagamento
    await prisma.payment.update({
      where: { id: pendingPayment.id },
      data: {
        status: 'COMPLETED',
        method: 'multibanco'
      }
    })

    // Atualizar referência Multibanco
    await prisma.multibancoReference.updateMany({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: { status: 'PAID', paidAt: new Date() }
    })

    console.log(`✅ Pagamento de reparação ${repairId} confirmado via PaymentIntent`)
  }
}

// Processar pagamento genérico (fallback)
async function handleGenericPayment(paymentIntent: Stripe.PaymentIntent) {
  // Tentar encontrar qualquer pagamento pendente com este PaymentIntent ID
  const subscriptionPayment = await prisma.subscriptionPayment.findFirst({
    where: { stripePaymentIntentId: paymentIntent.id, status: 'PENDING' }
  })

  if (subscriptionPayment) {
    await handleSubscriptionPayment(paymentIntent, { subscriptionId: subscriptionPayment.subscriptionId })
    return
  }

  const marketplacePayment = await prisma.payment.findFirst({
    where: { stripeSessionId: paymentIntent.id, status: 'PENDING' }
  })

  if (marketplacePayment) {
    const orderIds = marketplacePayment.orderId ? [marketplacePayment.orderId] : []
    await handleMarketplacePayment(paymentIntent, { orderIds: orderIds.join(',') })
    return
  }

  console.log('Nenhum pagamento pendente encontrado para PaymentIntent:', paymentIntent.id)
}

// Handlers para Checkout Sessions (similar aos PaymentIntents)
async function handleSubscriptionCheckout(session: Stripe.Checkout.Session, metadata: any) {
  try {
    console.log('🎉 Processando checkout de subscrição:', session.id)
    console.log('Metadata:', metadata)

    const subscriptionId = metadata?.subscriptionId
    const userId = metadata?.userId
    const planId = metadata?.planId
    const billingCycle = metadata?.billingCycle

    if (!subscriptionId && !userId) {
      console.log('❌ SubscriptionId ou UserId não encontrado nos metadados')
      return
    }

    // Se temos subscriptionId, atualizar subscrição existente
    if (subscriptionId) {
      const subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId }
      })

      if (subscription) {
        await prisma.subscription.update({
          where: { id: subscriptionId },
          data: {
            status: 'ACTIVE',
            stripeSubscriptionId: session.subscription as string,
            stripeCustomerId: session.customer as string,
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + (billingCycle === 'MONTHLY' ? 30 : 365) * 24 * 60 * 60 * 1000)
          }
        })

        // Atualizar pagamento se existir
        if (metadata?.paymentId) {
          await prisma.subscriptionPayment.update({
            where: { id: metadata.paymentId },
            data: {
              status: 'COMPLETED',
              stripePaymentIntentId: session.payment_intent as string,
              paidAt: new Date()
            }
          })
        }

        console.log('✅ Subscrição atualizada:', subscriptionId)
        return
      }
    }

    // Se não temos subscriptionId, criar nova subscrição
    if (userId && planId) {
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: planId }
      })

      if (plan) {
        const price = billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice
        const currentPeriodStart = new Date()
        const currentPeriodEnd = new Date(Date.now() + (billingCycle === 'MONTHLY' ? 30 : 365) * 24 * 60 * 60 * 1000)

        const newSubscription = await prisma.subscription.create({
          data: {
            userId,
            planId,
            status: 'ACTIVE',
            billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
            stripeSubscriptionId: session.subscription as string,
            stripeCustomerId: session.customer as string,
            currentPeriodStart,
            currentPeriodEnd
          }
        })

        // Criar pagamento
        await prisma.subscriptionPayment.create({
          data: {
            subscriptionId: newSubscription.id,
            amount: price,
            currency: 'EUR',
            status: 'COMPLETED',
            stripePaymentIntentId: session.payment_intent as string,
            paidAt: new Date(),
            periodStart: currentPeriodStart,
            periodEnd: currentPeriodEnd
          }
        })

        console.log('✅ Nova subscrição criada:', newSubscription.id)
      }
    }

  } catch (error) {
    console.error('❌ Erro ao processar checkout de subscrição:', error)
  }
}

async function handleMarketplaceCheckout(session: Stripe.Checkout.Session, metadata: any) {
  // Implementar lógica similar ao PaymentIntent mas para checkout sessions
  console.log('Processando checkout de marketplace:', session.id)
}

async function handleRepairCheckout(session: Stripe.Checkout.Session, metadata: any) {
  // Implementar lógica similar ao PaymentIntent mas para checkout sessions
  console.log('Processando checkout de reparação:', session.id)
}
