import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user.isSuperAdmin) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'csv'
    const status = searchParams.get('status')
    const paymentType = searchParams.get('paymentType')
    const paymentMethod = searchParams.get('paymentMethod')
    const lojistId = searchParams.get('lojistId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Construir filtros (mesmo que na API principal)
    const where: any = {}

    if (status) where.status = status
    if (paymentType) where.paymentType = paymentType
    if (paymentMethod) where.paymentMethodType = paymentMethod
    if (lojistId) where.lojistId = lojistId

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    // Buscar todos os pagamentos (sem paginação para export)
    const payments = await prisma.paymentHistory.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    })

    if (format === 'csv') {
      // Gerar CSV
      const csvHeaders = [
        'ID',
        'Data',
        'Valor',
        'Moeda',
        'Status',
        'Tipo',
        'Método',
        'Cliente Email',
        'Cliente Nome',
        'Lojista',
        'Comissão',
        'Valor Líquido',
        'Stripe Payment Intent',
        'Stripe Invoice',
        'Descrição',
        'Razão Falha'
      ].join(',')

      const csvRows = payments.map(payment => [
        payment.id,
        payment.createdAt.toISOString(),
        payment.amount.toString(),
        payment.currency,
        payment.status,
        payment.paymentType,
        payment.paymentMethodType || '',
        payment.customerEmail || '',
        payment.customerName || '',
        payment.lojistName || '',
        payment.platformFee?.toString() || '',
        payment.netAmount?.toString() || '',
        payment.stripePaymentIntentId || '',
        payment.stripeInvoiceId || '',
        payment.description || '',
        payment.failureReason || ''
      ].map(field => `"${field}"`).join(','))

      const csvContent = [csvHeaders, ...csvRows].join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="pagamentos_${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    if (format === 'json') {
      return NextResponse.json(payments, {
        headers: {
          'Content-Disposition': `attachment; filename="pagamentos_${new Date().toISOString().split('T')[0]}.json"`
        }
      })
    }

    return NextResponse.json(
      { message: 'Formato não suportado' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Erro ao exportar pagamentos:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
