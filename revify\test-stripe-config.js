// Teste para verificar configuração do Stripe
require('dotenv').config({ path: '.env.local' })

console.log('🔍 Verificando configuração do Stripe...')

// Verificar variáveis de ambiente
const stripeSecretKey = process.env.STRIPE_SECRET_KEY
const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

console.log('STRIPE_SECRET_KEY:', stripeSecretKey ? `${stripeSecretKey.substring(0, 20)}...` : '❌ NÃO CONFIGURADA')
console.log('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:', stripePublishableKey ? `${stripePublishableKey.substring(0, 20)}...` : '❌ NÃO CONFIGURADA')

// Verificar se as chaves são válidas
if (stripeSecretKey && stripeSecretKey.startsWith('sk_')) {
  console.log('✅ Chave secreta do Stripe parece válida')
} else {
  console.log('❌ Chave secreta do Stripe inválida ou não configurada')
}

if (stripePublishableKey && stripePublishableKey.startsWith('pk_')) {
  console.log('✅ Chave pública do Stripe parece válida')
} else {
  console.log('❌ Chave pública do Stripe inválida ou não configurada')
}

// Testar conexão com Stripe
if (stripeSecretKey) {
  const stripe = require('stripe')(stripeSecretKey)
  
  stripe.accounts.retrieve()
    .then(account => {
      console.log('✅ Conexão com Stripe bem-sucedida')
      console.log('Conta:', account.display_name || account.id)
    })
    .catch(error => {
      console.log('❌ Erro na conexão com Stripe:', error.message)
    })
} else {
  console.log('❌ Não é possível testar conexão - chave secreta não configurada')
}