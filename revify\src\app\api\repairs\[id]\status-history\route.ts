import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET - Buscar histórico de status da reparação
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso à reparação
    const repair = await prisma.repair.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        customerId: true,
        repairShopId: true,
        assignedEmployeeId: true
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar permissões
    const hasAccess = 
      repair.customerId === session.user.id || // Cliente
      repair.repairShopId === session.user.id || // Lojista
      session.user.role === 'ADMIN' || // Admin
      (session.user.role === 'EMPLOYEE' && repair.assignedEmployeeId === session.user.employeeId) // Empregado atribuído

    if (!hasAccess) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar histórico de status
    const statusHistory = await prisma.repairStatusHistory.findMany({
      where: {
        repairId: params.id
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Buscar informações dos usuários que fizeram alterações
    const userIds = statusHistory
      .filter(h => h.updatedBy && h.updatedByType === 'REPAIR_SHOP')
      .map(h => h.updatedBy!)

    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds }
      },
      select: {
        id: true,
        name: true
      }
    })

    const usersMap = users.reduce((acc, user) => {
      acc[user.id] = user
      return acc
    }, {} as Record<string, any>)

    // Formatar histórico
    const formattedHistory = statusHistory.map(entry => ({
      id: entry.id,
      status: entry.status,
      notes: entry.notes,
      createdAt: entry.createdAt,
      updatedBy: entry.updatedBy,
      updatedByType: entry.updatedByType,
      updatedByName: entry.updatedByType === 'EMPLOYEE' && entry.employee
        ? entry.employee.name
        : entry.updatedByType === 'REPAIR_SHOP' && entry.updatedBy
        ? usersMap[entry.updatedBy]?.name
        : 'Sistema',
      updatedByPosition: entry.employee?.position || null
    }))

    return NextResponse.json({
      statusHistory: formattedHistory
    })

  } catch (error) {
    console.error('Erro ao buscar histórico de status:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// POST - Adicionar nova entrada no histórico
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { status, notes } = await request.json()

    if (!status) {
      return NextResponse.json(
        { message: 'Status é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o usuário tem acesso à reparação
    const repair = await prisma.repair.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        customerId: true,
        repairShopId: true,
        assignedEmployeeId: true,
        status: true
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar permissões para alterar status
    let canUpdateStatus = false
    let updatedByType = ''
    let employeeId = null

    if (session.user.role === 'REPAIR_SHOP' && repair.repairShopId === session.user.id) {
      canUpdateStatus = true
      updatedByType = 'REPAIR_SHOP'
    } else if (session.user.role === 'EMPLOYEE') {
      // Verificar se é empregado da loja e tem permissão
      const employee = await prisma.employee.findFirst({
        where: {
          userId: session.user.id,
          repairShopId: repair.repairShopId,
          isActive: true
        }
      })

      if (employee && employee.permissions && 
          (employee.permissions as any).canUpdateRepairStatus) {
        canUpdateStatus = true
        updatedByType = 'EMPLOYEE'
        employeeId = employee.id
      }
    } else if (session.user.role === 'ADMIN') {
      canUpdateStatus = true
      updatedByType = 'ADMIN'
    }

    if (!canUpdateStatus) {
      return NextResponse.json(
        { message: 'Sem permissão para alterar status' },
        { status: 403 }
      )
    }

    // Atualizar reparação e criar entrada no histórico em transação
    const result = await prisma.$transaction(async (tx) => {
      // Atualizar status da reparação
      const updatedRepair = await tx.repair.update({
        where: { id: params.id },
        data: { status }
      })

      // Criar entrada no histórico
      const historyEntry = await tx.repairStatusHistory.create({
        data: {
          repairId: params.id,
          status,
          notes,
          updatedBy: session.user.id,
          updatedByType,
          employeeId
        },
        include: {
          employee: {
            select: {
              id: true,
              name: true,
              position: true
            }
          }
        }
      })

      return { updatedRepair, historyEntry }
    })

    // Buscar nome do usuário se necessário
    let updatedByName = 'Sistema'
    if (updatedByType === 'EMPLOYEE' && result.historyEntry.employee) {
      updatedByName = result.historyEntry.employee.name
    } else if (updatedByType === 'REPAIR_SHOP' || updatedByType === 'ADMIN') {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { name: true }
      })
      updatedByName = user?.name || 'Sistema'
    }

    return NextResponse.json({
      message: 'Status atualizado com sucesso',
      repair: result.updatedRepair,
      historyEntry: {
        id: result.historyEntry.id,
        status: result.historyEntry.status,
        notes: result.historyEntry.notes,
        createdAt: result.historyEntry.createdAt,
        updatedBy: result.historyEntry.updatedBy,
        updatedByType: result.historyEntry.updatedByType,
        updatedByName,
        updatedByPosition: result.historyEntry.employee?.position || null
      }
    })

  } catch (error) {
    console.error('Erro ao atualizar status:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
