const fs = require('fs')
const path = require('path')

console.log('🔧 CORREÇÃO FINAL DE PROBLEMAS JAVASCRIPT...\n')

// 1. Limpar completamente cache e builds
console.log('1️⃣ Limpando cache completo...')

const dirsToClean = [
  '.next',
  'node_modules/.cache',
  '.vercel'
]

dirsToClean.forEach(dir => {
  const fullPath = path.join(__dirname, '..', dir)
  if (fs.existsSync(fullPath)) {
    try {
      fs.rmSync(fullPath, { recursive: true, force: true })
      console.log(`✅ ${dir} removido`)
    } catch (error) {
      console.log(`⚠️ Erro ao remover ${dir}:`, error.message)
    }
  } else {
    console.log(`ℹ️ ${dir} não existe`)
  }
})

// 2. Verificar problemas específicos na página nova-v2
console.log('\n2️⃣ Verificando página nova-v2...')
const novaReparacaoPath = path.join(__dirname, '..', 'src/app/cliente/reparacoes/nova-v2/page.tsx')

if (fs.existsSync(novaReparacaoPath)) {
  const content = fs.readFileSync(novaReparacaoPath, 'utf8')
  
  // Verificar problemas específicos
  const issues = []
  
  // Verificar se há variáveis com nomes problemáticos
  const problematicVars = ['ey', 'ev', 'key', 'index']
  problematicVars.forEach(varName => {
    const regex = new RegExp(`\\b${varName}\\s*=`, 'g')
    const matches = content.match(regex)
    if (matches) {
      issues.push(`Variável '${varName}' encontrada ${matches.length} vezes`)
    }
  })
  
  // Verificar se há problemas com .map() sem key
  const mapRegex = /\.map\s*\(\s*\([^)]*\)\s*=>/g
  const mapMatches = content.match(mapRegex) || []
  
  mapMatches.forEach((match, index) => {
    // Verificar se há key prop na linha seguinte
    const lines = content.split('\n')
    let foundMap = false
    
    lines.forEach((line, lineIndex) => {
      if (line.includes(match.substring(0, 20)) && !foundMap) {
        foundMap = true
        // Verificar próximas 5 linhas por key prop
        const nextLines = lines.slice(lineIndex, lineIndex + 5).join(' ')
        if (!nextLines.includes('key=')) {
          issues.push(`Map sem key prop na linha ${lineIndex + 1}`)
        }
      }
    })
  })
  
  if (issues.length > 0) {
    console.log('⚠️ Problemas encontrados:')
    issues.forEach(issue => console.log(`   - ${issue}`))
  } else {
    console.log('✅ Nenhum problema específico encontrado')
  }
  
  // Verificar se há imports problemáticos
  const imports = content.match(/import.*from.*/g) || []
  const problematicImports = imports.filter(imp => 
    imp.includes('../../') || 
    imp.includes('../../../') ||
    imp.includes('../../../../')
  )
  
  if (problematicImports.length > 0) {
    console.log('⚠️ Imports com paths longos:')
    problematicImports.forEach(imp => console.log(`   ${imp}`))
  }
  
} else {
  console.log('❌ Arquivo nova-v2/page.tsx não encontrado')
}

// 3. Verificar se há problemas de TypeScript
console.log('\n3️⃣ Verificando configuração TypeScript...')
const tsconfigPath = path.join(__dirname, '..', 'tsconfig.json')

if (fs.existsSync(tsconfigPath)) {
  try {
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'))
    
    // Verificar configurações que podem causar problemas
    const compilerOptions = tsconfig.compilerOptions || {}
    
    console.log('📋 Configurações TypeScript:')
    console.log(`   strict: ${compilerOptions.strict}`)
    console.log(`   noUnusedLocals: ${compilerOptions.noUnusedLocals}`)
    console.log(`   noUnusedParameters: ${compilerOptions.noUnusedParameters}`)
    
    if (compilerOptions.strict === false) {
      console.log('⚠️ Modo strict desativado pode causar problemas')
    }
    
  } catch (error) {
    console.log('❌ Erro ao ler tsconfig.json:', error.message)
  }
} else {
  console.log('❌ tsconfig.json não encontrado')
}

// 4. Verificar Next.js config
console.log('\n4️⃣ Verificando configuração Next.js...')
const nextConfigPath = path.join(__dirname, '..', 'next.config.js')

if (fs.existsSync(nextConfigPath)) {
  console.log('✅ next.config.js encontrado')
  
  const nextConfig = fs.readFileSync(nextConfigPath, 'utf8')
  
  // Verificar se há configurações que podem causar problemas
  if (nextConfig.includes('swcMinify: false')) {
    console.log('⚠️ SWC minify desativado')
  }
  
  if (nextConfig.includes('experimental')) {
    console.log('⚠️ Configurações experimentais encontradas')
  }
  
} else {
  console.log('ℹ️ next.config.js não encontrado (usando padrões)')
}

console.log('\n🎯 SOLUÇÕES RECOMENDADAS:')
console.log('1. Cache limpo - fazer build completo')
console.log('2. Verificar se não há conflitos de nomes de variáveis')
console.log('3. Garantir que todos os .map() têm key props')
console.log('4. Verificar imports com paths relativos longos')
console.log('5. Fazer deploy forçado: vercel --prod --force')

console.log('\n📋 COMANDOS PARA EXECUTAR:')
console.log('npm install --force')
console.log('npm run build')
console.log('vercel --prod --force')

console.log('\n✅ Análise completa concluída!')
