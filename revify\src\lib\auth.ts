import { NextAuthOptions } from 'next-auth'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import { prisma } from './prisma'
import bcrypt from 'bcryptjs'

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          },
          include: {
            profile: true,
            employeeOf: true
          }
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        // Verificar se empregado está ativo
        if (user.role === 'EMPLOYEE' && user.employeeOf && !user.employeeOf.isActive) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          isSuperAdmin: user.isSuperAdmin,
          employeeId: user.employeeOf?.id || null,
          repairShopId: user.employeeOf?.repairShopId || null
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          // Verificar se o usuário já existe
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! }
          })

          if (!existingUser) {
            // Criar novo usuário
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name!,
                role: 'CUSTOMER',
                isVerified: true, // Email já verificado pelo Google
                profile: {
                  create: {
                    phone: '',
                    address: '',
                    city: '',
                    postalCode: '',
                    country: 'PT'
                  }
                }
              }
            })
          }
          return true
        } catch (error) {
          console.error('Erro ao criar usuário do Google:', error)
          return false
        }
      }
      return true
    },
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.isSuperAdmin = (user as any).isSuperAdmin
        token.employeeId = (user as any).employeeId
        token.repairShopId = (user as any).repairShopId
      } else if (token.email) {
        // Buscar dados do usuário se não estiver no token
        const dbUser = await prisma.user.findUnique({
          where: { email: token.email },
          select: {
            role: true,
            isSuperAdmin: true,
            employeeOf: {
              select: {
                id: true,
                repairShopId: true,
                isActive: true
              }
            }
          }
        })
        if (dbUser) {
          token.role = dbUser.role
          token.isSuperAdmin = dbUser.isSuperAdmin
          token.employeeId = dbUser.employeeOf?.id || null
          token.repairShopId = dbUser.employeeOf?.repairShopId || null
        }
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
        session.user.isSuperAdmin = token.isSuperAdmin as boolean
        session.user.employeeId = token.employeeId as string
        session.user.repairShopId = token.repairShopId as string
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
  }
}
