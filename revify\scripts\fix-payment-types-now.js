const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixPaymentTypesNow() {
  console.log('🔧 CORRIGINDO TIPOS DE PAGAMENTO AGORA...\n')

  try {
    // 1. Buscar pagamentos com valor baixo que são REPAIR
    const wrongPayments = await prisma.paymentHistory.findMany({
      where: {
        paymentType: 'REPAIR',
        amount: { lte: 10 }
      }
    })

    console.log(`❌ Encontrados ${wrongPayments.length} pagamentos com tipo incorreto`)

    if (wrongPayments.length === 0) {
      console.log('✅ Nenhum pagamento para corrigir')
      return
    }

    // 2. Corrigir cada pagamento
    let correctedCount = 0
    
    for (const payment of wrongPayments) {
      try {
        console.log(`🔄 Corrigindo pagamento ${payment.id}: €${payment.amount}`)
        
        // Atualizar tipo para SUBSCRIPTION
        await prisma.paymentHistory.update({
          where: { id: payment.id },
          data: { 
            paymentType: 'SUBSCRIPTION',
            updatedAt: new Date()
          }
        })
        
        correctedCount++
        console.log(`✅ Pagamento ${payment.id} corrigido: REPAIR → SUBSCRIPTION`)
        
      } catch (error) {
        console.error(`❌ Erro ao corrigir pagamento ${payment.id}:`, error.message)
      }
    }

    console.log(`\n📊 RESULTADO:`)
    console.log(`✅ ${correctedCount} pagamentos corrigidos`)
    console.log(`❌ ${wrongPayments.length - correctedCount} erros`)

    // 3. Verificar se a correção funcionou
    console.log('\n🔍 VERIFICANDO CORREÇÃO...')
    
    const stillWrong = await prisma.paymentHistory.count({
      where: {
        paymentType: 'REPAIR',
        amount: { lte: 10 }
      }
    })

    if (stillWrong === 0) {
      console.log('✅ TODOS OS TIPOS CORRIGIDOS!')
    } else {
      console.log(`❌ Ainda há ${stillWrong} pagamentos com tipo incorreto`)
    }

    // 4. Mostrar resultado final
    console.log('\n📋 TIPOS DE PAGAMENTO ATUAIS:')
    
    const paymentsByType = await prisma.paymentHistory.groupBy({
      by: ['paymentType'],
      _count: {
        id: true
      },
      where: {
        amount: { lte: 10 }
      }
    })

    paymentsByType.forEach(group => {
      console.log(`   ${group.paymentType}: ${group._count.id} pagamentos`)
    })

  } catch (error) {
    console.error('❌ Erro geral:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixPaymentTypesNow()
