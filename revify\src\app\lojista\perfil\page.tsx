'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  Building,
  MapPin,
  Clock,
  Star,
  Users,
  Save,
  Upload,
  FileText,
  Settings,
  Briefcase
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'


interface ProfileData {
  companyName: string
  companyNif?: string
  nif: string
  phone: string
  email: string
  description?: string
  companyType?: string
  legalDocument?: string
  logo?: string
  address: string
  city: string
  postalCode: string
  district?: string
  workingCategories: string[]
  workingBrands: string[]
  workingProblems: string[]
  serviceRadius?: number
  averageRepairTime?: number
  monthlyRepairs?: number
  expectedGrowth?: number
  businessHours: {
    monday: { open: string; close: string; closed: boolean }
    tuesday: { open: string; close: string; closed: boolean }
    wednesday: { open: string; close: string; closed: boolean }
    thursday: { open: string; close: string; closed: boolean }
    friday: { open: string; close: string; closed: boolean }
    saturday: { open: string; close: string; closed: boolean }
    sunday: { open: string; close: string; closed: boolean }
  }
}

interface Category {
  id: string
  name: string
}

interface Brand {
  id: string
  name: string
}

interface ProblemType {
  id: string
  name: string
  description?: string
}

export default function PerfilPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('geral')
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [categories, setCategories] = useState<Category[]>([])
  const [brands, setBrands] = useState<Brand[]>([])
  const [problemTypes, setProblemTypes] = useState<ProblemType[]>([])
  const [profileData, setProfileData] = useState<ProfileData>({
    companyName: '',
    companyNif: '',
    nif: '',
    phone: '',
    email: '',
    description: '',
    companyType: '',
    legalDocument: '',
    logo: '',
    address: '',
    city: '',
    postalCode: '',
    district: '',
    workingCategories: [],
    workingBrands: [],
    workingProblems: [],
    serviceRadius: 10,
    averageRepairTime: 3,
    monthlyRepairs: 50,
    expectedGrowth: 20,
    businessHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '13:00', closed: false },
      sunday: { open: '09:00', close: '18:00', closed: true }
    }
  })

  const tabs = [
    { id: 'geral', name: 'Geral', icon: Building },
    { id: 'especialidades', name: 'Especialidades', icon: Star },
    { id: 'horarios', name: 'Horários', icon: Clock }
  ]

  useEffect(() => {
    if (session?.user) {
      loadProfileData()
      loadCategories()
      loadBrands()
      loadProblemTypes()
    }
  }, [session])

  const loadProfileData = async () => {
    try {
      const response = await fetch('/api/lojista/profile')
      if (response.ok) {
        const data = await response.json()
        console.log('Dados do perfil recebidos:', data)

        // A API pode retornar dados diretos ou dentro de um objeto 'profile'
        const profileData = data.profile || data

        setProfileData(prev => ({
          ...prev,
          companyName: profileData.companyName || '',
          companyNif: profileData.companyNif || '',
          nif: profileData.nif || profileData.companyNif || '',
          phone: profileData.phone || '',
          email: data.user?.email || profileData.email || session?.user?.email || '',
          description: profileData.description || '',
          companyType: profileData.companyType || '',
          legalDocument: profileData.legalDocument || '',
          logo: profileData.logo || '',
          address: profileData.address || profileData.street || '',
          city: profileData.city || '',
          postalCode: profileData.postalCode || '',
          district: profileData.district || '',
          workingCategories: profileData.workingCategories || [],
          workingBrands: profileData.workingBrands || [],
          workingProblems: profileData.workingProblems || [],
          serviceRadius: profileData.serviceRadius || 10,
          averageRepairTime: profileData.averageRepairTime || 120,
          monthlyRepairs: profileData.monthlyRepairs || 0,
          expectedGrowth: profileData.expectedGrowth || 0,
          businessHours: profileData.businessHours || prev.businessHours
        }))
      }
    } catch (error) {
      console.error('Erro ao carregar dados do perfil:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || data || [])
      }
    } catch (error) {
      console.error('Erro ao carregar categorias:', error)
    }
  }

  const loadBrands = async () => {
    try {
      const response = await fetch('/api/brands')
      if (response.ok) {
        const data = await response.json()
        setBrands(data.brands || data || [])
      }
    } catch (error) {
      console.error('Erro ao carregar marcas:', error)
    }
  }

  const loadProblemTypes = async () => {
    try {
      const response = await fetch('/api/problem-types')
      if (response.ok) {
        const data = await response.json()
        setProblemTypes(data.problemTypes || data || [])
      }
    } catch (error) {
      console.error('Erro ao carregar tipos de problema:', error)
    }
  }

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validar tamanho (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Arquivo muito grande. Máximo 5MB.')
      return
    }

    try {
      const formData = new FormData()
      formData.append('logo', file)

      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        setProfileData(prev => ({ ...prev, logo: data.logoUrl }))
        alert('Logo enviado com sucesso!')
      } else {
        alert('Erro ao enviar logo')
      }
    } catch (error) {
      console.error('Erro ao fazer upload do logo:', error)
      alert('Erro ao enviar logo')
    }
  }

  const handleLegalDocumentUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validar tamanho (máximo 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Arquivo muito grande. Máximo 10MB.')
      return
    }

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'legal-document')

      const response = await fetch('/api/lojista/upload', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        setProfileData(prev => ({ ...prev, legalDocument: data.url }))
        alert('Documento legal enviado com sucesso!')
      } else {
        alert('Erro ao enviar documento legal')
      }
    } catch (error) {
      console.error('Erro ao fazer upload do documento legal:', error)
      alert('Erro ao enviar documento legal')
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Limpar dados para evitar duplicação
      const cleanData = {
        companyName: profileData.companyName,
        companyNif: profileData.companyNif || profileData.nif,
        description: profileData.description,
        phone: profileData.phone,
        companyType: profileData.companyType,
        legalDocument: profileData.legalDocument,
        workingCategories: profileData.workingCategories,
        workingBrands: profileData.workingBrands,
        workingProblems: profileData.workingProblems,
        serviceRadius: profileData.serviceRadius,
        averageRepairTime: profileData.averageRepairTime,
        monthlyRepairs: profileData.monthlyRepairs,
        expectedGrowth: profileData.expectedGrowth,
        businessHours: profileData.businessHours,
        address: profileData.address,
        city: profileData.city,
        postalCode: profileData.postalCode,
        district: profileData.district,
        logo: profileData.logo
      }

      console.log('Dados a enviar:', cleanData)

      const response = await fetch('/api/lojista/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(cleanData)
      })

      if (response.ok) {
        alert('Perfil atualizado com sucesso!')
        // Recarregar dados para sincronizar
        loadProfileData()
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Erro ao atualizar perfil')
      }
    } catch (error) {
      console.error('Erro ao salvar perfil:', error)
      alert('Erro ao atualizar perfil')
    } finally {
      setIsSaving(false)
    }
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">A carregar...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Perfil da Loja</h1>
            <p className="text-gray-600 mt-2">
              Gerir as informações e configurações da sua loja
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="inline-flex items-center"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'A guardar...' : 'Guardar'}
          </Button>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            {tabs.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="geral" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="w-5 h-5" />
                    Informações da Empresa
                  </CardTitle>
                  <CardDescription>
                    Dados básicos da sua loja de reparações
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="companyName" className="text-sm font-medium">Nome da Empresa</label>
                    <Input
                      id="companyName"
                      value={profileData.companyName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, companyName: e.target.value }))}
                      placeholder="Nome da sua loja"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="nif" className="text-sm font-medium">NIF</label>
                    <Input
                      id="nif"
                      value={profileData.nif}
                      onChange={(e) => setProfileData(prev => ({ ...prev, nif: e.target.value }))}
                      placeholder="Número de Identificação Fiscal"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="phone" className="text-sm font-medium">Telefone</label>
                    <Input
                      id="phone"
                      value={profileData.phone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="+351 XXX XXX XXX"
                    />
                  </div>

                  {/* Tipo de Empresa */}
                  <div className="space-y-2">
                    <label htmlFor="companyType" className="text-sm font-medium">Tipo de Empresa</label>
                    <select
                      id="companyType"
                      value={profileData.companyType}
                      onChange={(e) => setProfileData(prev => ({ ...prev, companyType: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Selecione o tipo</option>
                      <option value="ENI">ENI (Empresário em Nome Individual)</option>
                      <option value="EMPRESA">Empresa</option>
                      <option value="UNIPESSOAL">Sociedade Unipessoal</option>
                      <option value="LDA">Sociedade por Quotas (Lda.)</option>
                      <option value="SA">Sociedade Anónima (S.A.)</option>
                    </select>
                  </div>

                  {/* Upload de Documento Legal */}
                  <div className="space-y-2">
                    <label htmlFor="legalDocument" className="text-sm font-medium">
                      Documento Legal
                      {profileData.companyType === 'ENI' ? ' (Declaração de Atividade)' : ' (Certidão Permanente)'}
                    </label>
                    <div className="flex items-center space-x-4">
                      {profileData.legalDocument && (
                        <div className="flex items-center space-x-2 text-sm text-green-600">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span>Documento enviado</span>
                        </div>
                      )}
                      <div className="flex-1">
                        <Input
                          id="legalDocument"
                          type="file"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={handleLegalDocumentUpload}
                          className="cursor-pointer"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Formatos aceitos: PDF, JPG, PNG (máx. 10MB)
                          {profileData.companyType === 'ENI'
                            ? ' - Declaração de Início de Atividade'
                            : ' - Certidão Permanente do Registo Comercial'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium">Email</label>
                    <Input
                      id="email"
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                      disabled
                      className="bg-gray-50"
                    />
                    <p className="text-xs text-gray-500">Email da conta (não editável)</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    Localização
                  </CardTitle>
                  <CardDescription>
                    Endereço da sua loja física
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="address" className="text-sm font-medium">Morada</label>
                    <Input
                      id="address"
                      value={profileData.address}
                      onChange={(e) => setProfileData(prev => ({ ...prev, address: e.target.value }))}
                      placeholder="Rua, número, andar"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="city" className="text-sm font-medium">Cidade</label>
                      <Input
                        id="city"
                        value={profileData.city}
                        onChange={(e) => setProfileData(prev => ({ ...prev, city: e.target.value }))}
                        placeholder="Lisboa"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="postalCode" className="text-sm font-medium">Código Postal</label>
                      <Input
                        id="postalCode"
                        value={profileData.postalCode}
                        onChange={(e) => setProfileData(prev => ({ ...prev, postalCode: e.target.value }))}
                        placeholder="1000-001"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Configurações de Serviço */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10"/>
                      <polyline points="12,6 12,12 16,14"/>
                    </svg>
                    Configurações de Serviço
                  </CardTitle>
                  <CardDescription>
                    Configurações de área de atuação e tempos de serviço
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="serviceRadius" className="text-sm font-medium">Raio de Atuação (km)</label>
                      <Input
                        id="serviceRadius"
                        type="number"
                        min="1"
                        max="100"
                        value={profileData.serviceRadius}
                        onChange={(e) => setProfileData(prev => ({ ...prev, serviceRadius: parseInt(e.target.value) || 10 }))}
                        placeholder="10"
                      />
                      <p className="text-xs text-gray-500">Distância máxima para recolha/entrega</p>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="averageRepairTime" className="text-sm font-medium">Tempo Médio de Reparação (minutos)</label>
                      <Input
                        id="averageRepairTime"
                        type="number"
                        min="30"
                        max="1440"
                        value={profileData.averageRepairTime}
                        onChange={(e) => setProfileData(prev => ({ ...prev, averageRepairTime: parseInt(e.target.value) || 120 }))}
                        placeholder="120"
                      />
                      <p className="text-xs text-gray-500">Tempo estimado por reparação</p>
                    </div>
                  </div>

                  {/* Upload de Logo */}
                  <div className="space-y-2">
                    <label htmlFor="logo" className="text-sm font-medium">Logo da Loja</label>
                    <div className="flex items-center space-x-4">
                      {profileData.logo && (
                        <div className="w-16 h-16 rounded-lg border border-gray-200 overflow-hidden">
                          <img
                            src={profileData.logo}
                            alt="Logo da loja"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                      <div className="flex-1">
                        <Input
                          id="logo"
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="cursor-pointer"
                        />
                        <p className="text-xs text-gray-500 mt-1">Formatos aceitos: JPG, PNG, GIF (máx. 5MB)</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="especialidades" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>Categorias</CardTitle>
                  <CardDescription>
                    Tipos de dispositivos que repara
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`category-${category.id}`}
                          checked={profileData.workingCategories.includes(category.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProfileData(prev => ({
                                ...prev,
                                workingCategories: [...prev.workingCategories, category.id]
                              }))
                            } else {
                              setProfileData(prev => ({
                                ...prev,
                                workingCategories: prev.workingCategories.filter(id => id !== category.id)
                              }))
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor={`category-${category.id}`} className="text-sm">{category.name}</label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Marcas</CardTitle>
                  <CardDescription>
                    Marcas com que trabalha
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {brands.map((brand) => (
                      <div key={brand.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`brand-${brand.id}`}
                          checked={profileData.workingBrands.includes(brand.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProfileData(prev => ({
                                ...prev,
                                workingBrands: [...prev.workingBrands, brand.id]
                              }))
                            } else {
                              setProfileData(prev => ({
                                ...prev,
                                workingBrands: prev.workingBrands.filter(id => id !== brand.id)
                              }))
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor={`brand-${brand.id}`} className="text-sm">{brand.name}</label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Tipos de Problema</CardTitle>
                  <CardDescription>
                    Problemas que consegue resolver
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {problemTypes.map((problem) => (
                      <div key={problem.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`problem-${problem.id}`}
                          checked={profileData.workingProblems.includes(problem.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProfileData(prev => ({
                                ...prev,
                                workingProblems: [...prev.workingProblems, problem.id]
                              }))
                            } else {
                              setProfileData(prev => ({
                                ...prev,
                                workingProblems: prev.workingProblems.filter(id => id !== problem.id)
                              }))
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor={`problem-${problem.id}`} className="text-sm">{problem.name}</label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="horarios" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Horário de Funcionamento
                </CardTitle>
                <CardDescription>
                  Configure os horários da sua loja
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(profileData.businessHours).map(([day, hours]) => (
                    <div key={day} className="flex items-center space-x-4">
                      <div className="w-24">
                        <label className="text-sm font-medium capitalize">
                          {day === 'monday' ? 'Segunda' :
                           day === 'tuesday' ? 'Terça' :
                           day === 'wednesday' ? 'Quarta' :
                           day === 'thursday' ? 'Quinta' :
                           day === 'friday' ? 'Sexta' :
                           day === 'saturday' ? 'Sábado' : 'Domingo'}
                        </label>
                      </div>
                      <input
                        type="checkbox"
                        checked={!hours.closed}
                        onChange={(e) => {
                          setProfileData(prev => ({
                            ...prev,
                            businessHours: {
                              ...prev.businessHours,
                              [day]: { ...hours, closed: !e.target.checked }
                            }
                          }))
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      {!hours.closed && (
                        <div className="flex flex-col space-y-3">
                          {/* Horário principal */}
                          <div className="flex items-center space-x-2">
                            <Input
                              type="time"
                              value={hours.open}
                              onChange={(e) => {
                                setProfileData(prev => ({
                                  ...prev,
                                  businessHours: {
                                    ...prev.businessHours,
                                    [day]: { ...hours, open: e.target.value }
                                  }
                                }))
                              }}
                              className="w-32"
                            />
                            <span className="text-sm text-gray-500">às</span>
                            <Input
                              type="time"
                              value={hours.close}
                              onChange={(e) => {
                                setProfileData(prev => ({
                                  ...prev,
                                  businessHours: {
                                    ...prev.businessHours,
                                    [day]: { ...hours, close: e.target.value }
                                  }
                                }))
                              }}
                              className="w-32"
                            />
                          </div>

                          {/* Horário de break/almoço */}
                          <div className="flex items-center space-x-2 pl-4">
                            <input
                              type="checkbox"
                              checked={hours.breakStart && hours.breakEnd}
                              onChange={(e) => {
                                setProfileData(prev => ({
                                  ...prev,
                                  businessHours: {
                                    ...prev.businessHours,
                                    [day]: {
                                      ...hours,
                                      breakStart: e.target.checked ? '12:00' : undefined,
                                      breakEnd: e.target.checked ? '13:00' : undefined
                                    }
                                  }
                                }))
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-600">Pausa para almoço:</span>

                            {hours.breakStart && hours.breakEnd && (
                              <>
                                <Input
                                  type="time"
                                  value={hours.breakStart}
                                  onChange={(e) => {
                                    setProfileData(prev => ({
                                      ...prev,
                                      businessHours: {
                                        ...prev.businessHours,
                                        [day]: { ...hours, breakStart: e.target.value }
                                      }
                                    }))
                                  }}
                                  className="w-28"
                                />
                                <span className="text-sm text-gray-500">às</span>
                                <Input
                                  type="time"
                                  value={hours.breakEnd}
                                  onChange={(e) => {
                                    setProfileData(prev => ({
                                      ...prev,
                                      businessHours: {
                                        ...prev.businessHours,
                                        [day]: { ...hours, breakEnd: e.target.value }
                                      }
                                    }))
                                  }}
                                  className="w-28"
                                />
                              </>
                            )}
                          </div>
                        </div>
                      )}
                      {hours.closed && (
                        <Badge variant="secondary">Fechado</Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
  )
}
