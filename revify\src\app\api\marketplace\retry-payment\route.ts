import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createUnifiedPaymentSystem } from '@/lib/payments/unified-payment-system'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const { orderIds, paymentMethod } = await request.json()

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { message: 'IDs das encomendas são obrigatórios' },
        { status: 400 }
      )
    }

    if (!paymentMethod || !['card', 'multibanco'].includes(paymentMethod)) {
      return NextResponse.json(
        { message: 'Método de pagamento inválido' },
        { status: 400 }
      )
    }

    // Buscar encomendas pendentes
    const orders = await prisma.order.findMany({
      where: {
        id: { in: orderIds },
        customerId: session.user.id,
        status: 'PENDING'
      },
      include: {
        items: true,
        customer: true
      }
    })

    if (orders.length === 0) {
      return NextResponse.json(
        { message: 'Nenhuma encomenda pendente encontrada' },
        { status: 404 }
      )
    }

    // Calcular total das encomendas
    const totalAmount = orders.reduce((sum, order) => sum + Number(order.total), 0)

    // Buscar pagamento pendente existente
    const existingPayment = await prisma.payment.findFirst({
      where: {
        orderId: orders[0].id,
        status: 'PENDING'
      }
    })

    if (!existingPayment) {
      return NextResponse.json(
        { message: 'Pagamento pendente não encontrado' },
        { status: 404 }
      )
    }

    // Criar sistema de pagamento unificado
    const paymentSystem = await createUnifiedPaymentSystem()

    // Remover referências Multibanco antigas se existirem
    await prisma.multibancoReference.deleteMany({
      where: {
        orderId: { in: orderIds },
        status: 'PENDING'
      }
    })

    // Preparar request de pagamento
    const paymentRequest = {
      amount: totalAmount,
      currency: 'EUR',
      description: `Retry Encomenda Marketplace - ${orders.length} encomenda(s)`,
      metadata: {
        type: 'marketplace_retry',
        orderIds: orderIds.join(','),
        userId: session.user.id,
        customerName: orders[0].customerName,
        customerEmail: orders[0].customerEmail,
        paymentId: existingPayment.id
      },
      customerEmail: orders[0].customerEmail,
      successUrl: `${process.env.NEXTAUTH_URL}/marketplace/success?payment_intent={PAYMENT_INTENT_ID}&orders=${orderIds.join(',')}`,
      cancelUrl: `${process.env.NEXTAUTH_URL}/marketplace/checkout`
    }

    // Criar novo pagamento pendente para o retry
    const pendingPayment = await paymentSystem.createPendingPayment(
      'marketplace',
      orders[0].id,
      totalAmount,
      'EUR',
      {
        orderIds: orderIds.join(','),
        customerName: orders[0].customerName,
        customerEmail: orders[0].customerEmail,
        platformFee: totalAmount * 0.05,
        shopAmount: totalAmount * 0.95,
        isRetry: true,
        originalPaymentId: existingPayment.id
      }
    )

    // Processar pagamento
    const result = await paymentSystem.processPayment(
      paymentMethod as 'card' | 'multibanco',
      paymentRequest,
      pendingPayment
    )

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || 'Erro ao processar retry de pagamento' },
        { status: 500 }
      )
    }

    // Marcar pagamento antigo como cancelado
    await prisma.payment.update({
      where: { id: existingPayment.id },
      data: { status: 'FAILED' }
    })

    console.log(`✅ Retry de pagamento criado para encomendas: ${orderIds.join(', ')}`)

    // Retornar resposta baseada no método de pagamento
    if (paymentMethod === 'card') {
      return NextResponse.json({
        success: true,
        paymentMethod: 'card',
        orderIds,
        checkoutUrl: result.checkoutUrl
      })
    } else {
      return NextResponse.json({
        success: true,
        paymentMethod: 'multibanco',
        orderIds,
        multibanco: result.multibanco,
        paymentIntent: result.paymentIntent,
        redirectUrl: result.redirectUrl
      })
    }

  } catch (error) {
    console.error('Erro no retry de pagamento marketplace:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
