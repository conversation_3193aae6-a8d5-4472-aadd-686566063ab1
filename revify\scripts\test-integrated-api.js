const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testIntegratedAPI() {
  console.log('🧪 Testando API Integrada...')
  
  try {
    // Verificar se o arquivo existe
    const fs = require('fs')
    const path = require('path')
    
    const apiPath = path.join(__dirname, '../src/app/api/lojista/subscription/checkout-integrated/route.ts')
    const systemPath = path.join(__dirname, '../src/lib/stripe/integrated-system.ts')
    
    console.log('📁 Verificando arquivos...')
    console.log('   API integrada existe:', fs.existsSync(apiPath))
    console.log('   Sistema integrado existe:', fs.existsSync(systemPath))
    
    if (fs.existsSync(apiPath)) {
      const apiContent = fs.readFileSync(apiPath, 'utf8')
      console.log('   ✅ API contém "checkout-integrated":', apiContent.includes('checkout-integrated'))
      console.log('   ✅ API contém "createStripeIntegratedSystem":', apiContent.includes('createStripeIntegratedSystem'))
      console.log('   ✅ API contém "extractMultibancoDetails":', apiContent.includes('extractMultibancoDetails'))
    }
    
    if (fs.existsSync(systemPath)) {
      const systemContent = fs.readFileSync(systemPath, 'utf8')
      console.log('   ✅ Sistema contém "multibanco_display_details":', systemContent.includes('multibanco_display_details'))
      console.log('   ✅ Sistema contém "confirmPaymentIntent":', systemContent.includes('confirmPaymentIntent'))
    }
    
    // Verificar página de upgrade
    const upgradePath = path.join(__dirname, '../src/app/lojista/upgrade/page.tsx')
    if (fs.existsSync(upgradePath)) {
      const upgradeContent = fs.readFileSync(upgradePath, 'utf8')
      console.log('   ✅ Upgrade usa API integrada:', upgradeContent.includes('checkout-integrated'))
      console.log('   ✅ Upgrade tem log Multibanco REAL:', upgradeContent.includes('Multibanco REAL do Stripe'))
    }
    
    // Verificar se há planos de subscrição
    const plans = await prisma.subscriptionPlan.findMany()
    console.log(`📊 Planos encontrados: ${plans.length}`)
    
    if (plans.length > 0) {
      console.log('   Planos disponíveis:')
      plans.forEach(plan => {
        console.log(`   - ${plan.name}: €${plan.monthlyPrice}/mês`)
      })
    }
    
    // Verificar configurações do Stripe
    const stripeConfig = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: ['stripeSecretKey', 'stripePublishableKey']
        }
      }
    })
    
    console.log('🔑 Configurações Stripe:')
    stripeConfig.forEach(config => {
      const value = config.value || 'Não configurado'
      const masked = value.includes('sk_') || value.includes('pk_') 
        ? value.substring(0, 8) + '***' 
        : value
      console.log(`   ${config.key}: ${masked}`)
    })
    
    console.log('\n✅ Teste concluído!')
    console.log('\n📝 Próximos passos:')
    console.log('1. Fazer novo deploy no Vercel para limpar cache')
    console.log('2. Verificar logs do Vercel para erros na API')
    console.log('3. Testar localmente com npm run dev')
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testIntegratedAPI()
