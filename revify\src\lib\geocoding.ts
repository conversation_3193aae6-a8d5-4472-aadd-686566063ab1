import { prisma } from '@/lib/prisma'

interface GeocodeResult {
  latitude: number
  longitude: number
  formattedAddress: string
}

// Função para obter a API key do Google Maps das configurações
async function getGoogleMapsApiKey(): Promise<string | null> {
  try {
    const setting = await prisma.systemSettings.findUnique({
      where: { key: 'googleMapsApiKey' }
    })
    return setting?.value || null
  } catch (error) {
    console.error('Erro ao buscar API key do Google Maps:', error)
    return null
  }
}

// Função para geocodificar um endereço usando Google Maps API
export async function geocodeAddress(address: string): Promise<GeocodeResult | null> {
  try {
    const apiKey = await getGoogleMapsApiKey()
    if (!apiKey) {
      console.warn('Google Maps API key não configurada')
      return null
    }

    const encodedAddress = encodeURIComponent(address)
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedAddress}&key=${api<PERSON><PERSON>}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'OK' && data.results.length > 0) {
      const result = data.results[0]
      return {
        latitude: result.geometry.location.lat,
        longitude: result.geometry.location.lng,
        formattedAddress: result.formatted_address
      }
    }

    console.warn('Geocodificação falhou:', data.status)
    return null
  } catch (error) {
    console.error('Erro na geocodificação:', error)
    return null
  }
}

// Função para calcular distância entre duas coordenadas (fórmula de Haversine)
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371 // Raio da Terra em km
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// Função para calcular distância real usando Google Distance Matrix API
export async function calculateRealDistance(
  originLat: number,
  originLng: number,
  destLat: number,
  destLng: number
): Promise<{ distance: number; duration: number } | null> {
  try {
    const apiKey = await getGoogleMapsApiKey()
    if (!apiKey) {
      console.warn('Google Maps API key não configurada, usando cálculo Haversine')
      return {
        distance: calculateDistance(originLat, originLng, destLat, destLng),
        duration: 0
      }
    }

    const origins = `${originLat},${originLng}`
    const destinations = `${destLat},${destLng}`
    const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origins}&destinations=${destinations}&units=metric&key=${apiKey}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'OK' && data.rows[0]?.elements[0]?.status === 'OK') {
      const element = data.rows[0].elements[0]
      return {
        distance: Math.round(element.distance.value / 1000 * 10) / 10, // Converter metros para km
        distanceInMeters: element.distance.value, // Manter valor original em metros
        duration: Math.round(element.duration.value / 60) // Converter segundos para minutos
      }
    }

    console.warn('Distance Matrix API falhou:', data.status)
    // Fallback para Haversine
    return {
      distance: calculateDistance(originLat, originLng, destLat, destLng),
      duration: 0
    }
  } catch (error) {
    console.error('Erro na Distance Matrix API:', error)
    // Fallback para Haversine
    return {
      distance: calculateDistance(originLat, originLng, destLat, destLng),
      duration: 0
    }
  }
}

// Função para geocodificar código postal do cliente
export async function geocodePostalCode(postalCode: string): Promise<GeocodeResult | null> {
  const address = `${postalCode}, Portugal`
  return await geocodeAddress(address)
}

// Função para formatar distância (metros ou km)
export function formatDistance(distanceKm: number, distanceMeters?: number): string {
  // Se temos metros e a distância é menor que 1km, mostrar em metros
  if (distanceMeters && distanceKm < 1) {
    return `${Math.round(distanceMeters)}m`
  }

  // Senão, mostrar em km
  if (distanceKm < 1) {
    return `${Math.round(distanceKm * 1000)}m`
  } else if (distanceKm < 10) {
    return `${distanceKm.toFixed(1)}km`
  } else {
    return `${Math.round(distanceKm)}km`
  }
}

// Função para obter coordenadas do usuário (para usar no frontend)
export function getCurrentLocation(): Promise<{ latitude: number; longitude: number } | null> {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      console.warn('Geolocalização não suportada pelo navegador')
      resolve(null)
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        })
      },
      (error) => {
        console.warn('Erro ao obter localização:', error.message)
        resolve(null)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutos
      }
    )
  })
}

// Função para geocodificar endereços de lojas que ainda não têm coordenadas
export async function geocodeRepairShops() {
  try {
    const shopsWithoutCoords = await prisma.user.findMany({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          OR: [
            { latitude: null },
            { longitude: null }
          ]
        }
      },
      include: {
        profile: true
      }
    })

    console.log(`Encontradas ${shopsWithoutCoords.length} lojas sem coordenadas`)

    for (const shop of shopsWithoutCoords) {
      if (!shop.profile) continue

      const address = `${shop.profile.street || ''} ${shop.profile.city || ''} ${shop.profile.postalCode || ''} Portugal`.trim()
      
      if (!address || address === 'Portugal') continue

      console.log(`Geocodificando: ${shop.profile.companyName || shop.name} - ${address}`)

      const result = await geocodeAddress(address)
      
      if (result) {
        await prisma.profile.update({
          where: { userId: shop.id },
          data: {
            latitude: result.latitude,
            longitude: result.longitude,
            address: result.formattedAddress
          }
        })
        console.log(`✓ Coordenadas atualizadas para ${shop.profile.companyName || shop.name}`)
      } else {
        console.log(`✗ Falha na geocodificação para ${shop.profile.companyName || shop.name}`)
      }

      // Delay para evitar rate limiting
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    return { success: true, processed: shopsWithoutCoords.length }
  } catch (error) {
    console.error('Erro na geocodificação em lote:', error)
    return { success: false, error: error.message }
  }
}
