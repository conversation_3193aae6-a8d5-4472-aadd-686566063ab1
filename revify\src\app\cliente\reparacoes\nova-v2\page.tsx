'use client'

import { useState, useEffect, Suspense, useCallback, useMemo } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useTranslation } from '@/hooks/useTranslation'
import AIDiagnosticInput from '@/components/AIDiagnosticInput'
import AddressAutocomplete from '@/components/ui/AddressAutocomplete'
import ModernLayout from '@/components/ModernLayout'
import Link from 'next/link'
import { ArrowLeft, ArrowRight, MapPin, Clock, Euro, Truck, Store, Mail, User, CreditCard, Star } from 'lucide-react'

interface RepairFormData {
  // Passo 1: Dispositivo e problema
  categoryId: string
  brandId: string
  deviceId: string
  problemTypeId: string
  description: string
  problemImages: File[]

  // Passo 2: Loja selecionada (baseado na localização)
  selectedShopId: string
  customerLocation: string // Para filtrar lojas próximas

  // Passo 3: Método de entrega (baseado na loja escolhida)
  deliveryMethod: string // 'STORE_PICKUP', 'COURIER_PICKUP', 'MAIL_SEND'
  pickupAddress: string
  deliveryAddress: string

  // Passo 4: Dados do cliente
  customerName: string
  customerPhone: string
  customerNif: string
  customerPostalCode: string
}

function NovaReparacaoV2PageContent() {
  const router = useRouter()
  const { data: session } = useSession()
  const { t } = useTranslation()
  const searchParams = useSearchParams()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState('card')
  
  const [formData, setFormData] = useState<RepairFormData>({
    categoryId: searchParams?.get('categoryId') || '',
    brandId: searchParams?.get('brandId') || '',
    deviceId: searchParams?.get('deviceId') || '',
    problemTypeId: searchParams?.get('problemTypeId') || searchParams?.get('problemType') || '',
    description: searchParams?.get('description') || '',
    problemImages: [],
    selectedShopId: '',
    customerLocation: '',
    deliveryMethod: '',
    pickupAddress: '',
    deliveryAddress: '',
    customerName: '',
    customerPhone: '',
    customerNif: '',
    customerPostalCode: ''
  })

  // Estados para dados da IA
  const [aiDiagnosis, setAiDiagnosis] = useState<any>(null)
  const [showAiInput, setShowAiInput] = useState(false)

  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [devices, setDevices] = useState<any[]>([])
  const [problemTypes, setProblemTypes] = useState<any[]>([])
  const [availableShops, setAvailableShops] = useState<any[]>([])
  const [filteredShops, setFilteredShops] = useState<any[]>([])
  const [shopFilter, setShopFilter] = useState<'distance' | 'price' | 'rating'>('distance')
  const [isLoadingShops, setIsLoadingShops] = useState(false)
  const [searchRadius, setSearchRadius] = useState(10)
  const [radiusInfo, setRadiusInfo] = useState<any>(null)
  const [savedAddresses, setSavedAddresses] = useState<any[]>([])
  const [showPostalCodeInput, setShowPostalCodeInput] = useState(true)
  const [selectedShopDistance, setSelectedShopDistance] = useState<number | null>(null)
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null)
  const [estimatedTime, setEstimatedTime] = useState<number | null>(null)

  // Definir funções antes dos useEffect
  const handleInputChange = (field: keyof RepairFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Se selecionou uma loja, guardar a distância para validação de estafeta
    if (field === 'selectedShopId' && availableShops.length > 0) {
      const selectedShop = availableShops.find(shop => shop.id === value)
      setSelectedShopDistance(selectedShop?.distance || null)
    }
  }

  const fetchInitialData = async () => {
    try {
      const [categoriesRes, brandsRes, problemTypesRes] = await Promise.all([
        fetch('/api/admin/categories'),
        fetch('/api/admin/brands'),
        fetch('/api/problem-types')
      ])

      if (categoriesRes.ok) setCategories(await categoriesRes.json())
      if (brandsRes.ok) setBrands(await brandsRes.json())
      if (problemTypesRes.ok) setProblemTypes(await problemTypesRes.json())
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    }
  }

  const fetchSavedAddresses = async () => {
    if (!session?.user?.id) return

    try {
      const response = await fetch('/api/cliente/addresses')
      if (response.ok) {
        const data = await response.json()
        setSavedAddresses(data.addresses || [])

        // Se há endereço padrão, usar seu código postal
        const defaultAddress = data.addresses?.find((addr: any) => addr.isDefault)
        if (defaultAddress && !formData.customerPostalCode) {
          handleInputChange('customerPostalCode', defaultAddress.postalCode)
        }
      }
    } catch (error) {
      console.error('Erro ao buscar endereços salvos:', error)
    }
  }

  const fetchEstimate = useCallback(async () => {
    if (!formData.categoryId || !formData.problemTypeId || !formData.deviceId) return

    try {
      const response = await fetch(`/api/cliente/estimate-price?categoryId=${formData.categoryId}&problemTypeId=${formData.problemTypeId}&deviceId=${formData.deviceId}`)
      if (response.ok) {
        const estimate = await response.json()

        // Só atualizar se não temos estimativa da IA ou se a nova estimativa é diferente
        const hasAiEstimate = searchParams?.get('aiDiagnosis') === 'true' && searchParams?.get('estimatedPrice')

        if (!hasAiEstimate || estimate.averagePrice !== estimatedPrice) {
          setEstimatedPrice(estimate.averagePrice)
          setEstimatedTime(estimate.averageTime)
          console.log('💰 Estimativa atualizada da API:', estimate.averagePrice)
        } else {
          console.log('💰 Mantendo estimativa da IA:', estimatedPrice)
        }
      }
    } catch (error) {
      console.error('Erro ao buscar estimativa:', error)
    }
  }, [formData.categoryId, formData.problemTypeId, formData.deviceId, estimatedPrice, searchParams])

  useEffect(() => {
    fetchInitialData()
    fetchSavedAddresses()

    // Verificar se veio do diagnóstico IA
    if (searchParams?.get('aiDiagnosis') === 'true') {
      const aiEstimatedPrice = searchParams.get('estimatedPrice')
      const aiEstimatedTime = searchParams.get('estimatedTime')

      setAiDiagnosis({
        problemType: searchParams.get('problemType'),
        component: searchParams.get('component'),
        suggestion: searchParams.get('suggestion'),
        estimatedPrice: aiEstimatedPrice,
        confidence: searchParams.get('confidence')
      })

      // Definir estimativa inicial da IA
      if (aiEstimatedPrice) {
        setEstimatedPrice(parseFloat(aiEstimatedPrice))
        console.log('💰 Usando estimativa da IA:', aiEstimatedPrice)
      }
      if (aiEstimatedTime) {
        setEstimatedTime(parseFloat(aiEstimatedTime))
      }
    }
  }, [])

  // Preencher dados do cliente automaticamente
  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        customerName: session.user.name || '',
        customerPhone: session.user.profile?.phone || ''
      }))
    }
  }, [session])

  useEffect(() => {
    if (formData.categoryId && formData.brandId) {
      fetchDevices()
    }
  }, [formData.categoryId, formData.brandId])

  useEffect(() => {
    if (formData.categoryId && formData.problemTypeId && formData.deviceId) {
      fetchEstimate()
    }
  }, [fetchEstimate])

  const fetchDevices = async () => {
    if (formData.categoryId && formData.brandId) {
      try {
        const response = await fetch(`/api/admin/device-models?categoryId=${formData.categoryId}&brandId=${formData.brandId}`)
        if (response.ok) {
          setDevices(await response.json())
        }
      } catch (error) {
        console.error('Erro ao carregar modelos:', error)
      }
    }
  }

  const fetchAvailableShops = useCallback(async () => {
    if (!formData.categoryId || !formData.problemTypeId || !formData.deviceId) {
      console.log('❌ Dados insuficientes para buscar lojas')
      return
    }

    setIsLoadingShops(true)
    try {
      console.log('🔍 Buscando lojas disponíveis...', {
        categoryId: formData.categoryId,
        problemTypeId: formData.problemTypeId,
        deviceId: formData.deviceId,
        postalCode: formData.customerPostalCode,
        radius: searchRadius
      })

      // Incluir código postal e raio se disponíveis
      const postalCodeParam = formData.customerPostalCode ? `&postalCode=${encodeURIComponent(formData.customerPostalCode)}` : ''
      const radiusParam = `&radius=${searchRadius}`
      const response = await fetch(`/api/cliente/available-shops?categoryId=${formData.categoryId}&problemTypeId=${formData.problemTypeId}&deviceId=${formData.deviceId}${postalCodeParam}${radiusParam}`)

      console.log('📡 Resposta da API:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        console.log('📊 Dados recebidos:', data)

        const shops = data.shops || data || []
        setAvailableShops(shops)
        setFilteredShops(shops) // Inicializar filtro
        setRadiusInfo(data.radiusInfo)

        console.log(`✅ ${shops.length} lojas carregadas`)
      } else {
        console.error('❌ Erro na API:', response.status, response.statusText)
        const errorText = await response.text()
        console.error('Detalhes do erro:', errorText)

        // Definir array vazio em caso de erro
        setAvailableShops([])
        setFilteredShops([])
      }
    } catch (error) {
      console.error('❌ Erro ao buscar lojas:', error)
      // Definir array vazio em caso de erro
      setAvailableShops([])
      setFilteredShops([])
    } finally {
      setIsLoadingShops(false)
    }
  }, [formData.categoryId, formData.problemTypeId, formData.deviceId, formData.customerPostalCode, searchRadius])

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return formData.categoryId && formData.brandId && formData.deviceId && formData.problemTypeId && formData.description
      case 2:
        return formData.selectedShopId && formData.customerPostalCode
      case 3:
        return formData.deliveryMethod &&
               (formData.deliveryMethod === 'STORE_PICKUP' ||
                (formData.deliveryMethod === 'COURIER_PICKUP' && formData.pickupAddress && formData.deliveryAddress) ||
                (formData.deliveryMethod === 'MAIL_SEND' && formData.deliveryAddress))
      case 4:
        return formData.customerName && formData.customerPhone && formData.customerPostalCode
      default:
        return true
    }
  }

  const handlePreviousStep = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleNextStep = async () => {
    if (canProceed()) {
      const nextStep = currentStep + 1
      setCurrentStep(nextStep)

      // Se for para o passo 2 (escolher loja), mostrar caixa de código postal
      if (nextStep === 2) {
        setShowPostalCodeInput(true)
        // Se já tem código postal, buscar lojas automaticamente
        if (formData.customerPostalCode) {
          setShowPostalCodeInput(false)
          await fetchAvailableShops()
        }
      }
    }
  }


  // Função para aplicar filtros às lojas
  const applyShopFilter = (filterType: 'distance' | 'price' | 'rating') => {
    setShopFilter(filterType)

    const sorted = [...availableShops].sort((a, b) => {
      switch (filterType) {
        case 'distance':
          return a.distance - b.distance // Mais próximo primeiro
        case 'price':
          return a.price - b.price // Mais barato primeiro
        case 'rating':
          return b.rating - a.rating // Melhor rating primeiro
        default:
          return 0
      }
    })

    setFilteredShops(sorted)
  }

  // Função para expandir raio de busca
  const expandSearchRadius = (newRadius: number) => {
    setSearchRadius(newRadius)
    fetchAvailableShops()
  }

  // Função para salvar código postal na conta do cliente
  const savePostalCodeToAccount = async (postalCode: string) => {
    try {
      const response = await fetch('/api/cliente/addresses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          label: 'Localização Principal',
          street: 'Endereço não especificado',
          city: 'Cidade não especificada',
          postalCode: postalCode,
          country: 'Portugal',
          isDefault: savedAddresses.length === 0,
          type: 'HOME'
        })
      })

      if (response.ok) {
        fetchSavedAddresses() // Recarregar endereços
      }
    } catch (error) {
      console.error('Erro ao salvar código postal:', error)
    }
  }

  const handlePayment = async () => {
    setIsLoading(true)
    try {
      const selectedShop = availableShops.find(s => s.id === formData.selectedShopId)
      if (!selectedShop) return

      // Criar sessão de pagamento
      const response = await fetch('/api/payments/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          repairData: {
            ...formData,
            deviceName: devices.find(d => d.id === formData.deviceId)?.name,
            problemType: problemTypes.find(p => p.id === formData.problemTypeId)?.name,
            aiDiagnosis: aiDiagnosis // Incluir diagnóstico IA se disponível
          },
          amount: selectedShop.price,
          shopId: formData.selectedShopId,
          deliveryMethod: formData.deliveryMethod,
          paymentMethod
        })
      })

      if (response.ok) {
        const { url, repairId } = await response.json()

        if (paymentMethod === 'multibanco') {
          // Para Multibanco, redirecionar para página de sucesso com dados
          window.location.href = `/cliente/reparacoes/${repairId}/sucesso?payment_method=multibanco`
        } else {
          // Para cartão e Klarna, redirecionar para Stripe checkout
          window.location.href = url
        }
      } else {
        const error = await response.json()
        if (error.requiresAuth) {
          // Redirecionar para login preservando os dados
          const params = new URLSearchParams()
          Object.entries(formData).forEach(([key, value]) => {
            if (value) params.set(key, value.toString())
          })
          if (estimatedPrice) params.set('estimatedPrice', estimatedPrice.toString())
          if (estimatedTime) params.set('estimatedTime', estimatedTime.toString())

          const callbackUrl = `/cliente/reparacoes/nova-v2?${params.toString()}`
          router.push(`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`)
        } else {
          alert(error.message || 'Erro ao criar sessão de pagamento')
        }
      }
    } catch (error) {
      console.error('Erro no pagamento:', error)
      alert('Erro ao processar pagamento')
    } finally {
      setIsLoading(false)
    }
  }

  const steps = [
    { id: 1, title: 'Dispositivo', icon: MapPin },
    { id: 2, title: 'Loja', icon: Store },
    { id: 3, title: 'Entrega', icon: Truck },
    { id: 4, title: 'Dados', icon: User },
    { id: 5, title: 'Pagamento', icon: CreditCard }
  ]

  return (
    <ModernLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Login Notice for Non-Authenticated Users */}
        {!session && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <User className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  {t('simulateFree')}
                </p>
              </div>
            </div>
          </div>
        )}
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'border-gray-300 text-gray-500'
                }`}>
                  <step.icon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {currentStep === 1 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Dispositivo e Problema</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Categoria do Dispositivo *
                    </label>
                    <select
                      value={formData.categoryId}
                      onChange={(e) => handleInputChange('categoryId', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="">Selecione a categoria</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Marca *
                    </label>
                    <select
                      value={formData.brandId}
                      onChange={(e) => handleInputChange('brandId', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="">Selecione a marca</option>
                      {brands.map((brand) => (
                        <option key={brand.id} value={brand.id}>
                          {brand.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {formData.categoryId && formData.brandId && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Modelo do Dispositivo *
                    </label>
                    <select
                      value={formData.deviceId}
                      onChange={(e) => handleInputChange('deviceId', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="">Selecione o modelo</option>
                      {devices.map((device) => (
                        <option key={device.id} value={device.id}>
                          {device.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Problema *
                  </label>
                  <select
                    value={formData.problemTypeId}
                    onChange={(e) => handleInputChange('problemTypeId', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  >
                    <option value="">Selecione o problema</option>
                    {problemTypes.map((problem) => (
                      <option key={problem.id} value={problem.id}>
                        {problem.icon} {problem.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Mostrar diagnóstico IA se disponível */}
                {aiDiagnosis && (
                  <div className="bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">🧠 Diagnóstico Inteligente</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="font-medium text-gray-900">Problema Identificado</h4>
                        <p className="text-gray-700">{aiDiagnosis.problemType}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Componente</h4>
                        <p className="text-gray-700">{aiDiagnosis.component}</p>
                      </div>
                    </div>
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-900">Sugestão</h4>
                      <p className="text-gray-700">{aiDiagnosis.suggestion}</p>
                    </div>
                    {aiDiagnosis.estimatedPrice && (
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <span className="text-green-600 font-semibold">Estimativa: €{aiDiagnosis.estimatedPrice}</span>
                        <span className="text-sm text-gray-500">{aiDiagnosis.confidence}% confiança</span>
                      </div>
                    )}
                  </div>
                )}

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Descrição do Problema *
                    </label>
                    {!aiDiagnosis && (
                      <button
                        type="button"
                        onClick={() => setShowAiInput(!showAiInput)}
                        className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                      >
                        {showAiInput ? 'Usar formulário manual' : '🧠 Usar IA para diagnóstico'}
                      </button>
                    )}
                  </div>

                  {showAiInput && !aiDiagnosis ? (
                    <AIDiagnosticInput
                      onDiagnosisComplete={(result) => {
                        setAiDiagnosis({
                          problemType: result.tipoAvaria,
                          component: result.componenteAfetado,
                          suggestion: result.sugestaoReparacao,
                          estimatedPrice: result.estimatedPrice,
                          confidence: result.confidence
                        })

                        // Pré-preencher campos
                        if (result.categoryId) handleInputChange('categoryId', result.categoryId)
                        if (result.problemTypeId) handleInputChange('problemTypeId', result.problemTypeId)
                        if (result.deviceModelId) handleInputChange('deviceId', result.deviceModelId)
                        handleInputChange('description', result.originalDescription)

                        setShowAiInput(false)
                      }}
                      categoryId={formData.categoryId}
                      brandId={formData.brandId}
                      deviceId={formData.deviceId}
                    />
                  ) : (
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      rows={4}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="Descreva detalhadamente o problema..."
                    />
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Imagens do Problema (opcional)
                  </label>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || [])
                      setFormData(prev => ({ ...prev, problemImages: files }))
                    }}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  />
                  {formData.problemImages.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.problemImages.map((file, index) => (
                        <div key={index} className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                          📷 {file.name}
                        </div>
                      ))}
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-1">
                    Máximo 5 imagens, até 5MB cada
                  </p>
                </div>

                {estimatedPrice && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-blue-900 mb-2">Estimativa Inicial</h3>
                    <div className="flex items-center space-x-4 text-sm text-blue-700">
                      <div className="flex items-center">
                        <Euro className="w-4 h-4 mr-1" />
                        €{estimatedPrice}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {estimatedTime ? Math.round(estimatedTime / 60 * 10) / 10 : 0}h
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Escolher Loja de Reparação</h2>

              {/* Caixa de código postal */}
              {showPostalCodeInput && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">📍</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Onde está localizado?</h3>
                  </div>

                  <p className="text-gray-600 mb-4">
                    Insira o seu código postal para encontrarmos as lojas mais próximas de si.
                  </p>

                  <div className="space-y-4">
                    {/* Endereços salvos */}
                    {savedAddresses.length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Endereços salvos:
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {savedAddresses.slice(0, 3).map((address: any) => (
                            <button
                              key={address.id}
                              onClick={() => {
                                handleInputChange('customerPostalCode', address.postalCode)
                                setShowPostalCodeInput(false)
                                fetchAvailableShops()
                              }}
                              className="text-left p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                            >
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-gray-900">{address.label}</span>
                                {address.isDefault && (
                                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Padrão</span>
                                )}
                              </div>
                              <div className="text-sm text-gray-600">{address.postalCode} {address.city}</div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Input manual */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Ou insira manualmente:
                      </label>
                      <div className="flex space-x-3">
                        <input
                          type="text"
                          value={formData.customerPostalCode}
                          onChange={(e) => handleInputChange('customerPostalCode', e.target.value)}
                          className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                          placeholder="1000-001"
                          maxLength={8}
                        />
                        <button
                          onClick={() => {
                            if (formData.customerPostalCode) {
                              setShowPostalCodeInput(false)
                              savePostalCodeToAccount(formData.customerPostalCode)
                              fetchAvailableShops()
                            }
                          }}
                          disabled={!formData.customerPostalCode}
                          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
                        >
                          Procurar Lojas
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {isLoadingShops ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-500">A procurar lojas disponíveis...</p>
                  </div>
                ) : availableShops.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="text-6xl mb-4">🔍</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {radiusInfo?.expanded ? 'Nenhuma loja encontrada em Portugal' : `Nenhuma loja encontrada num raio de ${searchRadius}km`}
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {radiusInfo?.expanded
                        ? 'Não há lojas disponíveis para este tipo de reparação em toda a plataforma.'
                        : `Não encontrámos lojas disponíveis para este tipo de reparação num raio de ${searchRadius}km da sua localização.`
                      }
                    </p>

                    {!radiusInfo?.expanded && radiusInfo?.availableExpansions?.length > 0 && (
                      <div className="space-y-3">
                        <p className="text-sm text-gray-600 mb-3">Quer ver lojas mais distantes?</p>
                        <div className="flex flex-wrap justify-center gap-2">
                          {radiusInfo.availableExpansions.map((radius: number) => (
                            <button
                              key={radius}
                              onClick={() => expandSearchRadius(radius)}
                              className="px-4 py-2 bg-orange-100 text-orange-800 rounded-lg hover:bg-orange-200 transition-colors text-sm font-medium"
                            >
                              {radius === 999 ? 'Toda Portugal' : `${radius}km`}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    <button
                      onClick={fetchAvailableShops}
                      className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Tentar Novamente
                    </button>
                  </div>
                ) : (
                  <>
                    {/* Informações do raio de busca */}
                    {radiusInfo && formData.customerPostalCode && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-green-600">📍</span>
                          <span className="text-sm font-medium text-green-900">
                            {radiusInfo.expanded
                              ? `Expandimos a busca para ${radiusInfo.used === 999 ? 'toda Portugal' : `${radiusInfo.used}km`}`
                              : `Lojas num raio de ${radiusInfo.used}km`
                            }
                          </span>
                        </div>
                        <div className="text-xs text-green-700">
                          Código postal: {formData.customerPostalCode}
                          {radiusInfo.expanded && (
                            <span className="ml-2 px-2 py-1 bg-green-100 rounded-full">Busca expandida</span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Filtros */}
                    <div className="flex items-center justify-between mb-4 p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-700">Ordenar por:</span>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => applyShopFilter('distance')}
                            className={`px-3 py-1 text-xs rounded-full transition-colors ${
                              shopFilter === 'distance'
                                ? 'bg-blue-600 text-white'
                                : 'bg-white text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            📍 Mais Perto
                          </button>
                          <button
                            onClick={() => applyShopFilter('price')}
                            className={`px-3 py-1 text-xs rounded-full transition-colors ${
                              shopFilter === 'price'
                                ? 'bg-blue-600 text-white'
                                : 'bg-white text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            💰 Mais Barato
                          </button>
                          <button
                            onClick={() => applyShopFilter('rating')}
                            className={`px-3 py-1 text-xs rounded-full transition-colors ${
                              shopFilter === 'rating'
                                ? 'bg-blue-600 text-white'
                                : 'bg-white text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            ⭐ Melhor Avaliado
                          </button>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {filteredShops.length} loja{filteredShops.length !== 1 ? 's' : ''} encontrada{filteredShops.length !== 1 ? 's' : ''}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      {filteredShops.map((shop) => (
                      <div
                        key={shop.id}
                        onClick={() => handleInputChange('selectedShopId', shop.id)}
                        className={`p-6 border-2 rounded-lg cursor-pointer transition-all ${
                          formData.selectedShopId === shop.id
                            ? 'border-blue-600 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {/* Aviso de distância superior a 5km */}
                        {shop.distance > 5 && (
                          <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                            <div className="flex items-center text-amber-800">
                              <span className="text-amber-600 mr-2">⚠️</span>
                              <span className="text-sm font-medium">
                                Esta loja encontra-se a {shop.distanceFormatted || `${shop.distance.toFixed(1)}km`} de distância
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{shop.name}</h3>

                              {/* Badges */}
                              <div className="flex items-center space-x-1">
                                {shop.isVerified && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    ✓ Verificado
                                  </span>
                                )}
                                {shop.isCertified && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    🏆 Certificado
                                  </span>
                                )}
                                {shop.isFeatured && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    ⭐ Destaque
                                  </span>
                                )}
                              </div>

                              <div className="flex items-center space-x-1">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="text-sm font-medium text-gray-700">{shop.rating}</span>
                                <span className="text-sm text-gray-500">({shop.reviewCount} avaliações)</span>
                              </div>
                            </div>

                            <p className="text-gray-600 mb-3">{shop.description}</p>

                            <div className="flex items-center space-x-6 text-sm">
                              <div className="flex items-center space-x-1">
                                <MapPin className="w-4 h-4 text-gray-400" />
                                <span className="text-gray-600">{shop.distanceFormatted || `${shop.distance}km`}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4 text-gray-400" />
                                <span className="text-gray-600">{Math.round(shop.estimatedTime / 60 * 10) / 10}h</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Euro className="w-4 h-4 text-gray-400" />
                                <span className="text-gray-600">€{shop.price}</span>
                              </div>
                            </div>

                            {shop.availability.isOpen ? (
                              <div className="mt-2 text-sm text-green-600">
                                ✅ Disponível agora
                              </div>
                            ) : (
                              <div className="mt-2 text-sm text-orange-600">
                                ⏰ Próxima disponibilidade: {new Date(shop.availability.nextAvailable).toLocaleString('pt-PT')}
                              </div>
                            )}
                          </div>

                          <div className="text-right">
                            <div className="text-2xl font-bold text-blue-600">€{shop.price}</div>
                            <div className="text-sm text-gray-500">
                              {shop.estimatedTimeFormatted ||
                                (shop.estimatedTime && typeof shop.estimatedTime === 'number'
                                  ? (shop.estimatedTime >= 60
                                      ? `${Math.floor(shop.estimatedTime / 60)}h ${shop.estimatedTime % 60 > 0 ? `${shop.estimatedTime % 60}min` : ''}`
                                      : `${shop.estimatedTime}min`)
                                  : '2h estimadas'
                                )
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  </>
                )}
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Método de Entrega</h2>

              {/* Mostrar loja selecionada */}
              {formData.selectedShopId && availableShops.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h3 className="text-sm font-medium text-blue-900 mb-2">🏪 Loja Selecionada</h3>
                  {(() => {
                    const selectedShop = availableShops.find(s => s.id === formData.selectedShopId)
                    return selectedShop ? (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-blue-900">{selectedShop.name}</p>
                          <p className="text-sm text-blue-700">{selectedShop.distanceFormatted || `${selectedShop.distance}km`} • €{selectedShop.price}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">€{selectedShop.price}</div>
                        </div>
                      </div>
                    ) : null
                  })()}
                </div>
              )}

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div
                    onClick={() => handleInputChange('deliveryMethod', 'STORE_PICKUP')}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      formData.deliveryMethod === 'STORE_PICKUP'
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Store className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-medium text-gray-900">Entregar na Loja</h3>
                    <p className="text-sm text-gray-500">Sem custos adicionais</p>

                    {/* Aviso de distância superior a 5km */}
                    {(() => {
                      const selectedShop = availableShops.find(s => s.id === formData.selectedShopId)
                      if (selectedShop && selectedShop.distance > 5) {
                        return (
                          <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-lg">
                            <div className="flex items-center text-amber-800">
                              <span className="text-amber-600 mr-2">⚠️</span>
                              <span className="text-xs font-medium">
                                A loja encontra-se a {selectedShop.distanceFormatted || `${selectedShop.distance.toFixed(1)}km`} de distância
                              </span>
                            </div>
                          </div>
                        )
                      }
                      return null
                    })()}
                  </div>

                  <div
                    onClick={() => {
                      // Verificar se a distância permite estafeta
                      if (selectedShopDistance && selectedShopDistance > 10) {
                        // Não permitir seleção se distância > 10km
                        return
                      }
                      handleInputChange('deliveryMethod', 'COURIER_PICKUP')
                    }}
                    className={`p-4 border-2 rounded-lg transition-colors ${
                      selectedShopDistance && selectedShopDistance > 10
                        ? 'border-gray-200 bg-gray-100 cursor-not-allowed opacity-60'
                        : formData.deliveryMethod === 'COURIER_PICKUP'
                        ? 'border-blue-600 bg-blue-50 cursor-pointer'
                        : 'border-gray-300 hover:border-gray-400 cursor-pointer'
                    }`}
                  >
                    <Truck className={`w-8 h-8 mb-2 ${
                      selectedShopDistance && selectedShopDistance > 10 ? 'text-gray-400' : 'text-blue-600'
                    }`} />
                    <h3 className={`font-medium ${
                      selectedShopDistance && selectedShopDistance > 10 ? 'text-gray-500' : 'text-gray-900'
                    }`}>
                      Recolha no Local
                      {selectedShopDistance && selectedShopDistance > 10 && (
                        <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                          Indisponível
                        </span>
                      )}
                    </h3>
                    <p className={`text-sm ${
                      selectedShopDistance && selectedShopDistance > 10 ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {selectedShopDistance && selectedShopDistance > 10
                        ? `Distância muito grande (${selectedShopDistance}km > 10km)`
                        : 'Com custos de estafeta'
                      }
                    </p>
                  </div>

                  <div
                    onClick={() => handleInputChange('deliveryMethod', 'MAIL_SEND')}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      formData.deliveryMethod === 'MAIL_SEND'
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Mail className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-medium text-gray-900">Envio pelos Correios</h3>
                    <p className="text-sm text-gray-500">Custos suportados pelo cliente</p>
                  </div>
                </div>

                {formData.deliveryMethod === 'COURIER_PICKUP' && (
                  <div className="space-y-4 mt-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Endereço de Recolha *
                      </label>
                      <AddressAutocomplete
                        value={formData.pickupAddress}
                        onChange={(value) => handleInputChange('pickupAddress', value)}
                        placeholder="Onde recolher o dispositivo"
                        onAddressSelect={(address) => {
                          // Opcional: salvar coordenadas para cálculos futuros
                          console.log('Endereço de recolha selecionado:', address)
                        }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Endereço de Entrega *
                      </label>
                      <AddressAutocomplete
                        value={formData.deliveryAddress}
                        onChange={(value) => handleInputChange('deliveryAddress', value)}
                        placeholder="Onde entregar o dispositivo"
                        onAddressSelect={(address) => {
                          // Opcional: salvar coordenadas para cálculos futuros
                          console.log('Endereço de entrega selecionado:', address)
                        }}
                      />
                    </div>
                  </div>
                )}

                {formData.deliveryMethod === 'MAIL_SEND' && (
                  <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Endereço de Entrega *
                    </label>
                    <AddressAutocomplete
                      value={formData.deliveryAddress}
                      onChange={(value) => handleInputChange('deliveryAddress', value)}
                      placeholder="Onde entregar o dispositivo reparado"
                      onAddressSelect={(address) => {
                        // Opcional: salvar coordenadas para cálculos futuros
                        console.log('Endereço de entrega selecionado:', address)
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Dados do Cliente</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nome Completo *
                    </label>
                    <input
                      type="text"
                      value={formData.customerName}
                      onChange={(e) => handleInputChange('customerName', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="Seu nome completo"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Telefone *
                    </label>
                    <input
                      type="tel"
                      value={formData.customerPhone}
                      onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="+351 xxx xxx xxx"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Código Postal *
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={formData.customerPostalCode}
                        onChange={(e) => handleInputChange('customerPostalCode', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                        placeholder="1000-001"
                        maxLength={8}
                      />
                      {availableShops.length > 0 && (
                        <button
                          type="button"
                          onClick={fetchAvailableShops}
                          className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                          title="Recalcular distâncias"
                        >
                          📍
                        </button>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Para calcular distâncias precisas às lojas</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      NIF (opcional)
                    </label>
                    <input
                      type="text"
                      value={formData.customerNif}
                      onChange={(e) => handleInputChange('customerNif', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="123456789"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 5 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Pagamento</h2>
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo do Pedido</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Dispositivo:</span>
                      <span>{devices.find(d => d.id === formData.deviceId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Problema:</span>
                      <span>{problemTypes.find(p => p.id === formData.problemTypeId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Loja:</span>
                      <span>{availableShops.find(s => s.id === formData.selectedShopId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Método de entrega:</span>
                      <span>
                        {formData.deliveryMethod === 'STORE_PICKUP' && 'Entregar na loja'}
                        {formData.deliveryMethod === 'COURIER_PICKUP' && 'Recolha por estafeta'}
                        {formData.deliveryMethod === 'MAIL_SEND' && 'Envio pelos correios'}
                      </span>
                    </div>
                    <hr className="my-3" />
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Total:</span>
                      <span>€{availableShops.find(s => s.id === formData.selectedShopId)?.price}</span>
                    </div>
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-4">Método de Pagamento</h4>
                  <div className="space-y-3">
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="card"
                        checked={paymentMethod === 'card'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-3"
                      />
                      <CreditCard className="w-5 h-5 mr-3 text-gray-600" />
                      <div>
                        <div className="font-medium text-gray-900">Cartão de Crédito/Débito</div>
                        <div className="text-sm text-gray-600">Visa, Mastercard, American Express</div>
                      </div>
                    </label>

                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="multibanco"
                        checked={paymentMethod === 'multibanco'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-3"
                      />
                      <div className="w-5 h-5 mr-3 bg-red-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">MB</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Multibanco</div>
                        <div className="text-sm text-gray-600">Referência Multibanco</div>
                      </div>
                    </label>

                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="klarna"
                        checked={paymentMethod === 'klarna'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-3"
                      />
                      <div className="w-5 h-5 mr-3 bg-pink-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">K</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Klarna</div>
                        <div className="text-sm text-gray-600">Pague em 3x sem juros</div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">💳 Pagamento Seguro</h4>
                  <p className="text-sm text-blue-700">
                    O pagamento é processado de forma segura. O valor fica em escrow até a reparação estar concluída.
                    Só depois é transferido para a loja de reparação.
                  </p>
                </div>

                <button
                  onClick={handlePayment}
                  disabled={isLoading}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold disabled:opacity-50"
                >
                  {isLoading ? 'A processar...' : `Pagar com ${
                    paymentMethod === 'card' ? 'Cartão' :
                    paymentMethod === 'multibanco' ? 'Multibanco' :
                    paymentMethod === 'klarna' ? 'Klarna' : 'Cartão'
                  } - €${availableShops.find(s => s.id === formData.selectedShopId)?.price}`}
                </button>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handlePreviousStep}
              disabled={currentStep === 1}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Anterior
            </button>

            <span className="text-sm text-gray-500">
              Passo {currentStep} de {steps.length}
            </span>

            <button
              onClick={handleNextStep}
              disabled={!canProceed() || isLoading}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {currentStep === steps.length ? (
                isLoading ? 'A processar...' : 'Finalizar'
              ) : (
                <>
                  Próximo
                  <ArrowRight className="w-4 h-4 ml-2" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </ModernLayout>
  )
}

export default function NovaReparacaoV2Page() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      }
    >
      <NovaReparacaoV2PageContent />
    </Suspense>
  )
}
