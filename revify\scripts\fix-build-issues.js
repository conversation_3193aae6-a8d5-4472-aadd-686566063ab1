const fs = require('fs')
const path = require('path')

console.log('🔧 CORRIGINDO PROBLEMAS DE BUILD...\n')

// 1. Limpar cache do Next.js
console.log('1️⃣ Limpando cache do Next.js...')
const nextDir = path.join(__dirname, '..', '.next')
if (fs.existsSync(nextDir)) {
  fs.rmSync(nextDir, { recursive: true, force: true })
  console.log('✅ Cache .next removido')
} else {
  console.log('ℹ️ Cache .next não existe')
}

// 2. Verificar problemas de sintaxe na página nova-v2
console.log('\n2️⃣ Verificando sintaxe da página nova-v2...')
const novaReparacaoPath = path.join(__dirname, '..', 'src/app/cliente/reparacoes/nova-v2/page.tsx')

if (fs.existsSync(novaReparacaoPath)) {
  const content = fs.readFileSync(novaReparacaoPath, 'utf8')
  
  // Verificar problemas comuns
  const issues = []
  
  // Verificar se há funções arrow com problemas
  const arrowFunctionRegex = /=>\s*{[^}]*}/g
  const arrowFunctions = content.match(arrowFunctionRegex) || []
  
  arrowFunctions.forEach((func, index) => {
    if (func.includes('const ') || func.includes('let ') || func.includes('var ')) {
      issues.push(`Arrow function ${index + 1} pode ter problema de hoisting`)
    }
  })
  
  // Verificar se há problemas com .map() keys
  const mapRegex = /\.map\([^)]*\)/g
  const mapCalls = content.match(mapRegex) || []
  
  mapCalls.forEach((mapCall, index) => {
    if (!mapCall.includes('key=')) {
      issues.push(`Map call ${index + 1} pode estar sem key prop`)
    }
  })
  
  if (issues.length > 0) {
    console.log('⚠️ Problemas encontrados:')
    issues.forEach(issue => console.log(`   - ${issue}`))
  } else {
    console.log('✅ Nenhum problema de sintaxe encontrado')
  }
} else {
  console.log('❌ Arquivo nova-v2/page.tsx não encontrado')
}

// 3. Verificar se há problemas com imports
console.log('\n3️⃣ Verificando imports...')
const srcDir = path.join(__dirname, '..', 'src')

function checkImports(dir) {
  const files = fs.readdirSync(dir, { withFileTypes: true })
  
  files.forEach(file => {
    if (file.isDirectory()) {
      checkImports(path.join(dir, file.name))
    } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
      const filePath = path.join(dir, file.name)
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Verificar imports problemáticos
      const imports = content.match(/import.*from.*/g) || []
      imports.forEach(imp => {
        if (imp.includes('../../../') || imp.includes('../../../../')) {
          console.log(`⚠️ Import longo em ${file.name}: ${imp}`)
        }
      })
    }
  })
}

try {
  checkImports(srcDir)
  console.log('✅ Verificação de imports concluída')
} catch (error) {
  console.log('❌ Erro ao verificar imports:', error.message)
}

console.log('\n🎯 SOLUÇÕES RECOMENDADAS:')
console.log('1. Cache limpo - reiniciar servidor: npm run dev')
console.log('2. Se erro persistir: npm install --force')
console.log('3. Verificar se não há conflitos de nomes de variáveis')
console.log('4. Verificar se todos os componentes têm key props em loops')
console.log('5. Build limpo: npm run build')

console.log('\n✅ Script de correção concluído!')
