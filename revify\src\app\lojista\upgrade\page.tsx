'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Check, Star, Crown, ArrowLeft } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import PaymentMethodModal from '@/components/PaymentMethodModal'
import BalanceDisplay from '@/components/BalanceDisplay'

interface SubscriptionPlan {
  id: string
  name: string
  description: string | null
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  maxProducts: number | null
  maxRepairs: number | null
  marketplaceCommission: number
  repairCommission: number
  smsAccess: boolean
  whatsappAccess: boolean
  certifiedBadge: boolean
  priority: number
  isPopular: boolean
}

interface UserSubscription {
  plan: {
    id: string
    name: string
  }
  status: string
}

export default function UpgradePage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [billingCycle, setBillingCycle] = useState<'MONTHLY' | 'YEARLY'>('MONTHLY')
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      // Buscar planos disponíveis
      const plansResponse = await fetch('/api/subscription-plans')
      if (plansResponse.ok) {
        const plansData = await plansResponse.json()
        setPlans(plansData.plans || [])
      }

      // Buscar subscrição atual
      const subscriptionResponse = await fetch('/api/lojista/subscription')
      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json()
        setCurrentSubscription(subscriptionData.subscription)
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubscribe = (planId: string) => {
    const plan = plans.find(p => p.id === planId)
    if (plan) {
      setSelectedPlan(plan)
      setShowPaymentModal(true)
    }
  }

  const handlePaymentMethodSelected = async (paymentMethod: 'card' | 'paypal' | 'multibanco' | 'klarna', useBalance?: boolean) => {
    if (!selectedPlan) return

    try {
      if (paymentMethod === 'multibanco' || paymentMethod === 'klarna') {
        // Criar subscrição primeiro
        const subscriptionResponse = await fetch('/api/lojista/subscription/checkout-integrated', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            planId: selectedPlan.id,
            billingCycle,
            paymentMethod: 'multibanco'
          })
        })

        const subscriptionData = await subscriptionResponse.json()

        if (subscriptionResponse.ok) {
          if (subscriptionData.redirectUrl) {
            window.location.href = subscriptionData.redirectUrl
          } else if (subscriptionData.multibanco) {
            // Mostrar dados Multibanco REAIS do Stripe
            console.log('✅ Referência Multibanco REAL do Stripe:', subscriptionData.multibanco)
            window.location.href = `/lojista/subscription/success?subscription_id=${subscriptionData.subscription?.id}&multibanco=true`
          } else {
            alert('Erro: Resposta inesperada do servidor')
          }
        } else {
          const errorData = await subscriptionResponse.json()
          alert(errorData.message || 'Erro ao processar subscrição')
        }
      } else {
        // Para cartão e PayPal, usar nova API integrada
        const response = await fetch('/api/lojista/subscription/checkout-integrated', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            planId: selectedPlan.id,
            billingCycle,
            paymentMethod,
            useBalance: useBalance || false
          })
        })

        const data = await response.json()

        if (response.ok && data.checkoutUrl) {
          window.location.href = data.checkoutUrl
        } else {
          alert(data.message || 'Erro ao processar subscrição')
        }
      }
    } catch (error) {
      console.error('Erro ao processar subscrição:', error)
      alert('Erro ao processar subscrição')
    }
  }

  const getPrice = (plan: SubscriptionPlan) => {
    const monthlyPrice = Number(plan.monthlyPrice) || 0
    const yearlyPrice = Number(plan.yearlyPrice) || 0
    return billingCycle === 'MONTHLY' ? monthlyPrice : yearlyPrice
  }

  const getYearlySavings = (plan: SubscriptionPlan) => {
    const monthlyPrice = Number(plan.monthlyPrice) || 0
    const yearlyPrice = Number(plan.yearlyPrice) || 0
    const monthlyTotal = monthlyPrice * 12
    return monthlyTotal - yearlyPrice
  }

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.plan.id === planId
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Upgrade de Plano</h1>
            <p className="text-muted-foreground">
              Escolha o plano ideal para o seu negócio
            </p>
          </div>
          <Link href="/lojista">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </Link>
        </div>

        {/* Balance Display */}
        <div className="max-w-md mx-auto mb-6">
          <BalanceDisplay />
        </div>

        {/* Billing Cycle Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setBillingCycle('MONTHLY')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingCycle === 'MONTHLY'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Mensal
            </button>
            <button
              onClick={() => setBillingCycle('YEARLY')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingCycle === 'YEARLY'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Anual
              <Badge variant="secondary" className="ml-2">
                Poupe até 20%
              </Badge>
            </button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative ${
                plan.isPopular ? 'border-2 border-yellow-400' : ''
              } ${
                isCurrentPlan(plan.id) ? 'ring-2 ring-green-500' : ''
              }`}
            >
              {plan.isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-yellow-400 text-yellow-900">
                    <Star className="w-3 h-3 mr-1" />
                    Mais Popular
                  </Badge>
                </div>
              )}

              {isCurrentPlan(plan.id) && (
                <div className="absolute -top-3 right-4">
                  <Badge className="bg-green-500 text-white">
                    Plano Atual
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Crown className="w-8 h-8 text-yellow-500" />
                </div>
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                <div className="mt-4">
                  <div className="text-4xl font-bold">
                    €{getPrice(plan).toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-600">
                    por {billingCycle === 'MONTHLY' ? 'mês' : 'ano'}
                  </div>
                  {billingCycle === 'YEARLY' && getYearlySavings(plan) > 0 && (
                    <div className="text-sm text-green-600 font-medium">
                      Poupe €{getYearlySavings(plan).toFixed(2)} por ano
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="space-y-2 text-xs text-gray-600 mb-6">
                  {plan.maxProducts && (
                    <div>Máximo {plan.maxProducts} produtos</div>
                  )}
                  {plan.maxRepairs && (
                    <div>Máximo {plan.maxRepairs} reparações/mês</div>
                  )}
                  <div>Comissão marketplace: {plan.marketplaceCommission}%</div>
                  <div>Comissão reparações: {plan.repairCommission}%</div>
                  {plan.smsAccess && <div>✓ Acesso SMS</div>}
                  {plan.whatsappAccess && <div>✓ Acesso WhatsApp</div>}
                  {plan.certifiedBadge && <div>✓ Selo Certificado</div>}
                </div>

                <Button
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={isCurrentPlan(plan.id)}
                  className={`w-full ${
                    plan.isPopular
                      ? 'bg-yellow-500 hover:bg-yellow-600 text-yellow-900'
                      : ''
                  }`}
                >
                  {isCurrentPlan(plan.id)
                    ? 'Plano Atual'
                    : currentSubscription
                    ? 'Fazer Upgrade'
                    : 'Subscrever'
                  }
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>



        {/* Banner de Suporte */}
        <div className="mt-8 bg-gradient-to-r from-gray-900 to-black rounded-lg p-6 text-center">
          <h3 className="text-xl font-semibold text-gray-100 mb-2">
            Precisa de ajuda para escolher?
          </h3>
          <p className="text-gray-300 mb-4">
            Entre em contacto connosco e ajudamos a encontrar o plano ideal para o seu negócio.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-gray-200">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center gap-2 hover:text-white transition-colors"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
              <EMAIL>
            </a>
            <span className="text-gray-400">|</span>
            <a
              href="tel:+351123456789"
              className="flex items-center gap-2 hover:text-white transition-colors"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              +351 123 456 789
            </a>
          </div>
        </div>

        {/* Tabela Comparativa Detalhada */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-center">Comparação Detalhada de Planos</CardTitle>
            <CardDescription className="text-center">
              Compare todas as funcionalidades disponíveis em cada plano
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-semibold">Funcionalidade</th>
                    {plans.map(plan => (
                      <th key={plan.id} className="text-center p-4 font-semibold min-w-[150px]">
                        <div className="flex flex-col items-center">
                          <span>{plan.name}</span>
                          {plan.isPopular && (
                            <Badge className="mt-1 bg-primary text-primary-foreground text-xs">
                              <Star className="w-3 h-3 mr-1" />
                              Popular
                            </Badge>
                          )}
                          {isCurrentPlan(plan.id) && (
                            <Badge className="mt-1 bg-green-500 text-white text-xs">
                              Atual
                            </Badge>
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {/* Preços */}
                  <tr className="border-b bg-gray-50">
                    <td className="p-4 font-medium">Preço {billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        <div className="font-bold text-lg">
                          €{getPrice(plan).toFixed(2)}
                          <span className="text-sm text-gray-500">
                            /{billingCycle === 'MONTHLY' ? 'mês' : 'ano'}
                          </span>
                        </div>
                        {billingCycle === 'YEARLY' && getYearlySavings(plan) > 0 && (
                          <div className="text-xs text-green-600 mt-1">
                            Poupa €{getYearlySavings(plan).toFixed(2)}/ano
                          </div>
                        )}
                      </td>
                    ))}
                  </tr>

                  {/* Comissões */}
                  <tr className="border-b">
                    <td className="p-4">Comissão Marketplace</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.marketplaceCommission}%
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Comissão Reparações</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.repairCommission}%
                      </td>
                    ))}
                  </tr>

                  {/* Limites */}
                  <tr className="border-b">
                    <td className="p-4">Máximo de Produtos</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.maxProducts === null || plan.maxProducts === -1 ? 'Ilimitado' : plan.maxProducts}
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Máximo de Reparações</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.maxRepairs === null || plan.maxRepairs === -1 ? 'Ilimitado' : plan.maxRepairs}
                      </td>
                    ))}
                  </tr>

                  {/* Funcionalidades */}
                  <tr className="border-b">
                    <td className="p-4">Acesso SMS</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.smsAccess ? (
                          <Check className="w-5 h-5 text-green-500 mx-auto" />
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Acesso WhatsApp</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.whatsappAccess ? (
                          <Check className="w-5 h-5 text-green-500 mx-auto" />
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Badge Certificado</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {plan.certifiedBadge ? (
                          <Check className="w-5 h-5 text-green-500 mx-auto" />
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                    ))}
                  </tr>

                  {/* Funcionalidades dos planos */}
                  {plans.length > 0 && plans[0].features && (
                    <>
                      {/* Extrair todas as funcionalidades únicas */}
                      {Array.from(new Set(plans.flatMap(plan => plan.features || []))).map(feature => (
                        <tr key={feature} className="border-b">
                          <td className="p-4">{feature}</td>
                          {plans.map(plan => (
                            <td key={plan.id} className="text-center p-4">
                              {plan.features?.includes(feature) ? (
                                <Check className="w-5 h-5 text-green-500 mx-auto" />
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </>
                  )}

                  {/* Botões de ação */}
                  <tr>
                    <td className="p-4 font-medium">Ação</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center p-4">
                        {isCurrentPlan(plan.id) ? (
                          <Badge className="bg-green-500 text-white">
                            Plano Atual
                          </Badge>
                        ) : (
                          <Button
                            onClick={() => handleSubscribe(plan.id)}
                            disabled={isLoading}
                            className="w-full"
                            variant={plan.isPopular ? "default" : "outline"}
                          >
                            {isLoading ? 'Processando...' : 'Escolher Plano'}
                          </Button>
                        )}
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Payment Method Modal */}
        {selectedPlan && (
          <PaymentMethodModal
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            onSelectPaymentMethod={handlePaymentMethodSelected}
            planName={selectedPlan.name}
            amount={getPrice(selectedPlan)}
            billingCycle={billingCycle}
          />
        )}
      </div>
  )
}
