/**
 * Script de teste para validar a integração Stripe completa
 * 
 * Execute com: npx tsx src/scripts/test-stripe-integration.ts
 */

import { createStripeInstance } from '../lib/stripe'
import { prisma } from '../lib/prisma'
import { recordPaymentHistory } from '../lib/payments/payment-history'
import { reconcileAllPayments } from '../lib/payments/reconciliation'

interface TestResult {
  test: string
  success: boolean
  message: string
  data?: any
}

class StripeIntegrationTester {
  private results: TestResult[] = []
  private stripe: any

  async runAllTests() {
    console.log('🧪 Iniciando testes da integração Stripe...\n')

    try {
      // Inicializar Stripe
      this.stripe = await createStripeInstance()
      this.addResult('Stripe Initialization', true, 'Stripe inicializado com sucesso')

      // Executar testes
      await this.testDatabaseConnection()
      await this.testPaymentIntentCreation()
      await this.testMultibancoPayment()
      await this.testKlarnaAvailability()
      await this.testPaymentHistory()
      await this.testWebhookProcessing()
      await this.testReconciliation()
      await this.testAdminAPIs()

      // Mostrar resultados
      this.showResults()

    } catch (error) {
      console.error('❌ Erro geral nos testes:', error)
      this.addResult('General Error', false, error.message)
      this.showResults()
    }
  }

  private addResult(test: string, success: boolean, message: string, data?: any) {
    this.results.push({ test, success, message, data })
    const icon = success ? '✅' : '❌'
    console.log(`${icon} ${test}: ${message}`)
  }

  private async testDatabaseConnection() {
    try {
      const count = await prisma.paymentHistory.count()
      this.addResult('Database Connection', true, `Conexão OK - ${count} registos de pagamento`)
    } catch (error) {
      this.addResult('Database Connection', false, `Erro na conexão: ${error.message}`)
    }
  }

  private async testPaymentIntentCreation() {
    try {
      // Teste com cartão
      const cardPI = await this.stripe.paymentIntents.create({
        amount: 1000, // €10.00
        currency: 'eur',
        payment_method_types: ['card'],
        metadata: {
          test: 'true',
          type: 'test_card_payment'
        }
      })

      this.addResult('Card PaymentIntent', true, `PaymentIntent criado: ${cardPI.id}`)

      // Teste com Multibanco
      const multibancoPI = await this.stripe.paymentIntents.create({
        amount: 2000, // €20.00
        currency: 'eur',
        payment_method_types: ['multibanco'],
        metadata: {
          test: 'true',
          type: 'test_multibanco_payment'
        }
      })

      this.addResult('Multibanco PaymentIntent', true, `PaymentIntent criado: ${multibancoPI.id}`)

    } catch (error) {
      this.addResult('PaymentIntent Creation', false, `Erro: ${error.message}`)
    }
  }

  private async testMultibancoPayment() {
    try {
      // Criar e confirmar PaymentIntent Multibanco
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: 1500, // €15.00
        currency: 'eur',
        payment_method_types: ['multibanco'],
        confirmation_method: 'manual',
        metadata: {
          test: 'true',
          type: 'test_multibanco_reference'
        }
      })

      const confirmedPI = await this.stripe.paymentIntents.confirm(paymentIntent.id, {
        payment_method: {
          type: 'multibanco'
        }
      })

      if (confirmedPI.next_action?.multibanco_display_details) {
        const details = confirmedPI.next_action.multibanco_display_details
        this.addResult('Multibanco Reference', true, 
          `Referência gerada - Entidade: ${details.entity}, Ref: ${details.reference}`,
          details
        )
      } else {
        this.addResult('Multibanco Reference', false, 'Referência não gerada')
      }

    } catch (error) {
      this.addResult('Multibanco Payment', false, `Erro: ${error.message}`)
    }
  }

  private async testKlarnaAvailability() {
    try {
      // Testar criação de PaymentIntent com Klarna
      const klarnaPI = await this.stripe.paymentIntents.create({
        amount: 5000, // €50.00
        currency: 'eur',
        payment_method_types: ['klarna'],
        payment_method_options: {
          klarna: {
            preferred_locale: 'pt-PT'
          }
        },
        metadata: {
          test: 'true',
          type: 'test_klarna_payment'
        }
      })

      this.addResult('Klarna PaymentIntent', true, `Klarna PaymentIntent criado: ${klarnaPI.id}`)

    } catch (error) {
      if (error.code === 'payment_method_not_available') {
        this.addResult('Klarna Availability', false, 'Klarna não disponível para esta conta/região')
      } else {
        this.addResult('Klarna Test', false, `Erro: ${error.message}`)
      }
    }
  }

  private async testPaymentHistory() {
    try {
      // Testar registo no histórico
      const testPayment = await recordPaymentHistory({
        stripePaymentIntentId: 'pi_test_' + Date.now(),
        amount: 25.99,
        currency: 'EUR',
        status: 'SUCCEEDED',
        paymentType: 'REPAIR',
        paymentMethodType: 'card',
        description: 'Teste de registo no histórico',
        customerEmail: '<EMAIL>',
        customerName: 'Cliente Teste',
        metadata: { test: 'true' }
      })

      this.addResult('Payment History', true, `Pagamento registado no histórico: ${testPayment.id}`)

      // Testar busca
      const foundPayment = await prisma.paymentHistory.findUnique({
        where: { id: testPayment.id }
      })

      if (foundPayment) {
        this.addResult('Payment History Search', true, 'Pagamento encontrado na base de dados')
      } else {
        this.addResult('Payment History Search', false, 'Pagamento não encontrado')
      }

    } catch (error) {
      this.addResult('Payment History', false, `Erro: ${error.message}`)
    }
  }

  private async testWebhookProcessing() {
    try {
      // Simular evento de webhook
      const mockEvent = {
        id: 'evt_test_' + Date.now(),
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test_webhook_' + Date.now(),
            amount: 3000,
            currency: 'eur',
            status: 'succeeded',
            metadata: {
              test: 'true',
              type: 'webhook_test'
            }
          }
        }
      }

      // Importar função de processamento
      const { processStripeWebhookForHistory } = await import('../lib/payments/payment-history')
      await processStripeWebhookForHistory(mockEvent as any)

      this.addResult('Webhook Processing', true, 'Webhook simulado processado com sucesso')

    } catch (error) {
      this.addResult('Webhook Processing', false, `Erro: ${error.message}`)
    }
  }

  private async testReconciliation() {
    try {
      // Testar reconciliação
      const result = await reconcileAllPayments()

      this.addResult('Payment Reconciliation', result.success, 
        `Reconciliação: ${result.reconciledCount} processados, ${result.errorCount} erros`,
        {
          reconciledCount: result.reconciledCount,
          newPaymentsFound: result.newPaymentsFound,
          errorCount: result.errorCount
        }
      )

    } catch (error) {
      this.addResult('Payment Reconciliation', false, `Erro: ${error.message}`)
    }
  }

  private async testAdminAPIs() {
    try {
      // Testar contagem de pagamentos para admin
      const totalPayments = await prisma.paymentHistory.count()
      const successfulPayments = await prisma.paymentHistory.count({
        where: { status: 'SUCCEEDED' }
      })

      this.addResult('Admin API Data', true, 
        `${totalPayments} pagamentos total, ${successfulPayments} bem-sucedidos`
      )

      // Testar agregações
      const stats = await prisma.paymentHistory.aggregate({
        _sum: { amount: true },
        _count: { id: true }
      })

      this.addResult('Admin Statistics', true, 
        `Total processado: €${(stats._sum.amount || 0).toFixed(2)}`
      )

    } catch (error) {
      this.addResult('Admin APIs', false, `Erro: ${error.message}`)
    }
  }

  private showResults() {
    console.log('\n📊 RESUMO DOS TESTES')
    console.log('='.repeat(50))

    const successful = this.results.filter(r => r.success).length
    const failed = this.results.filter(r => !r.success).length
    const total = this.results.length

    console.log(`✅ Sucessos: ${successful}/${total}`)
    console.log(`❌ Falhas: ${failed}/${total}`)
    console.log(`📈 Taxa de sucesso: ${((successful/total) * 100).toFixed(1)}%`)

    if (failed > 0) {
      console.log('\n❌ TESTES FALHADOS:')
      this.results
        .filter(r => !r.success)
        .forEach(r => console.log(`  • ${r.test}: ${r.message}`))
    }

    console.log('\n🎯 PRÓXIMOS PASSOS:')
    if (failed === 0) {
      console.log('  ✅ Todos os testes passaram! Sistema pronto para produção.')
      console.log('  📝 Lembre-se de:')
      console.log('    • Configurar webhooks no Stripe Dashboard')
      console.log('    • Definir variáveis de ambiente de produção')
      console.log('    • Testar com dados reais em modo test')
    } else {
      console.log('  🔧 Corrigir os testes falhados antes de ir para produção')
      console.log('  📋 Verificar configurações do Stripe e base de dados')
    }

    console.log('\n' + '='.repeat(50))
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  const tester = new StripeIntegrationTester()
  tester.runAllTests().catch(console.error)
}

export { StripeIntegrationTester }
