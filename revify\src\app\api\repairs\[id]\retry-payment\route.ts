import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createUnifiedPaymentSystem } from '@/lib/payments/unified-payment-system'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { paymentMethod } = await request.json()

    if (!paymentMethod || !['card', 'multibanco'].includes(paymentMethod)) {
      return NextResponse.json(
        { message: 'Método de pagamento inválido' },
        { status: 400 }
      )
    }

    // Verificar se a reparação pertence ao cliente
    const repair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        customerId: session.user.id
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Buscar pagamento pendente
    const pendingPayment = await prisma.payment.findFirst({
      where: {
        repairId: params.id,
        status: 'PENDING'
      },
      orderBy: { createdAt: 'desc' }
    })

    if (!pendingPayment) {
      return NextResponse.json(
        { message: 'Nenhum pagamento pendente encontrado' },
        { status: 404 }
      )
    }

    const amount = Number(pendingPayment.amount)

    // Criar sistema de pagamento unificado
    const paymentSystem = await createUnifiedPaymentSystem()

    // Remover referências Multibanco antigas se existirem
    await prisma.multibancoReference.deleteMany({
      where: {
        orderId: params.id,
        status: 'PENDING'
      }
    })

    // Criar novo pagamento pendente para o retry
    const newPendingPayment = await paymentSystem.createPendingPayment(
      'repair',
      params.id,
      amount,
      'EUR',
      {
        type: 'repair_retry',
        originalPaymentId: pendingPayment.id,
        platformFee: amount * 0.05,
        shopAmount: amount * 0.95,
        isRetry: true
      }
    )

    // Preparar request de pagamento
    const paymentRequest = {
      amount,
      currency: 'EUR',
      description: `Retry Pagamento - Reparação #${params.id.slice(-8)}`,
      metadata: {
        type: 'repair_retry_payment',
        repairId: params.id,
        paymentId: newPendingPayment.id,
        originalPaymentId: pendingPayment.id,
        userId: session.user.id
      },
      customerEmail: session.user.email || undefined,
      successUrl: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}?payment=success&payment_intent={PAYMENT_INTENT_ID}`,
      cancelUrl: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}?payment=cancelled`
    }

    // Processar pagamento
    const result = await paymentSystem.processPayment(
      paymentMethod as 'card' | 'multibanco',
      paymentRequest,
      newPendingPayment
    )

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || 'Erro ao processar retry de pagamento' },
        { status: 500 }
      )
    }

    // Marcar pagamento antigo como falhado
    await prisma.payment.update({
      where: { id: pendingPayment.id },
      data: { status: 'FAILED' }
    })

    console.log(`✅ Retry de pagamento criado para reparação: ${params.id}`)

    // Retornar resposta baseada no método de pagamento
    if (paymentMethod === 'card') {
      return NextResponse.json({
        success: true,
        paymentMethod: 'card',
        checkoutUrl: result.checkoutUrl
      })
    } else {
      return NextResponse.json({
        success: true,
        paymentMethod: 'multibanco',
        multibanco: result.multibanco,
        paymentIntent: result.paymentIntent,
        redirectUrl: result.redirectUrl
      })
    }

  } catch (error) {
    console.error('Erro no retry de pagamento de reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
