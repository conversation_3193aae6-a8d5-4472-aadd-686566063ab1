'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Alert<PERSON>riangle, CreditCard, Clock } from 'lucide-react'
import { requiresActiveSubscription, isAllowedWithPendingSubscription } from '@/lib/subscription-access'

interface SubscriptionStatus {
  hasActiveSubscription: boolean
  hasPendingPayment: boolean
  subscriptionStatus: string | null
  planName: string | null
  message?: string
}

interface SubscriptionGuardProps {
  children: React.ReactNode
}

export default function SubscriptionGuard({ children }: SubscriptionGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [shouldRenderChildren, setShouldRenderChildren] = useState(false)
  const [blockingMessage, setBlockingMessage] = useState<React.ReactNode | null>(null)

  // TEMPORÁRIO: Desabilitar verificação de subscrição para evitar bloqueios
  // TODO: Reativar quando o sistema de subscrições estiver estável

  useEffect(() => {
    if (status === 'loading') return

    // Empregados não precisam de verificação de subscrição (usam o plano do lojista)
    if (!session?.user || session.user.role !== 'REPAIR_SHOP') {
      setIsLoading(false)
      setShouldRenderChildren(true)
      return
    }

    // TEMPORÁRIO: Permitir acesso a todos os lojistas
    setIsLoading(false)
    setShouldRenderChildren(true)
    return

    checkSubscription()
  }, [session, status])

  const checkSubscription = async () => {
    try {
      const response = await fetch('/api/lojista/subscription/status')
      if (response.ok) {
        const data = await response.json()
        setSubscriptionStatus(data)
        
        // Verificar se deve mostrar children ou bloqueio
        if (data.hasActiveSubscription) {
          setShouldRenderChildren(true)
        } else {
          // Verificar se a rota atual requer subscrição ativa
          const needsActiveSubscription = requiresActiveSubscription(pathname)
          const allowedWithPending = isAllowedWithPendingSubscription(pathname)
          
          if (!needsActiveSubscription || (data.hasPendingPayment && allowedWithPending)) {
            setShouldRenderChildren(true)
          } else {
            // Preparar mensagem de bloqueio
            setBlockingMessage(renderBlockingMessage(data))
          }
        }
      } else {
        // Se não conseguiu verificar, permitir acesso
        setShouldRenderChildren(true)
      }
    } catch (error) {
      console.error('Erro ao verificar subscrição:', error)
      // Em caso de erro, permitir acesso
      setShouldRenderChildren(true)
    } finally {
      setIsLoading(false)
    }
  }

  const renderBlockingMessage = (status: SubscriptionStatus) => (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="mb-4">
          {status.hasPendingPayment ? (
            <Clock className="w-16 h-16 text-yellow-500 mx-auto" />
          ) : (
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto" />
          )}
        </div>

        <h2 className="text-xl font-bold text-gray-900 mb-2">
          {status.hasPendingPayment ? 'Pagamento Pendente' : 'Subscrição Necessária'}
        </h2>

        <p className="text-gray-600 mb-6">
          {status.message}
        </p>

        {status.hasPendingPayment && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <CreditCard className="w-5 h-5 text-yellow-600 mr-2" />
              <span className="text-sm text-yellow-800">
                Complete o pagamento da sua subscrição para aceder a todas as funcionalidades.
              </span>
            </div>
          </div>
        )}

        <div className="space-y-3">
          <button
            onClick={() => router.push('/lojista/subscricao')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {status.hasPendingPayment ? 'Completar Pagamento' : 'Ver Subscrições'}
          </button>

          <button
            onClick={() => router.push('/lojista/perfil')}
            className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Ir para Perfil
          </button>
        </div>

        {status.planName && (
          <p className="text-xs text-gray-500 mt-4">
            Plano atual: {status.planName}
          </p>
        )}
      </div>
    </div>
  )

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Mostrar bloqueio se necessário
  if (blockingMessage) {
    return blockingMessage
  }

  // Mostrar children se permitido
  return <>{children}</>
}
