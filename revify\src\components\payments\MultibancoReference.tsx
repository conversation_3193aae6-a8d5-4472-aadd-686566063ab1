'use client'

import { useState } from 'react'
import { Copy, Check, Smartphone, Clock, Euro } from 'lucide-react'

interface MultibancoReferenceProps {
  entity: string
  reference: string
  amount: number
  currency?: string
  expiresAt?: string | Date
  description?: string
  onCopy?: () => void
  className?: string
}

export default function MultibancoReference({
  entity,
  reference,
  amount,
  currency = 'EUR',
  expiresAt,
  description,
  onCopy,
  className = ''
}: MultibancoReferenceProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null)

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      onCopy?.()
      
      // Limpar o estado após 2 segundos
      setTimeout(() => {
        setCopiedField(null)
      }, 2000)
    } catch (error) {
      console.error('Erro ao copiar:', error)
    }
  }

  const formatAmount = (value: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: 'currency',
      currency: currency
    }).format(value)
  }

  const formatExpiryDate = (date: string | Date) => {
    const expiryDate = typeof date === 'string' ? new Date(date) : date
    return expiryDate.toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isExpired = () => {
    if (!expiresAt) return false
    const expiryDate = typeof expiresAt === 'string' ? new Date(expiresAt) : expiresAt
    return new Date() > expiryDate
  }

  const CopyButton = ({ text, field, label }: { text: string; field: string; label: string }) => (
    <button
      onClick={() => copyToClipboard(text, field)}
      className="flex items-center px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
      title={`Copiar ${label}`}
    >
      {copiedField === field ? (
        <>
          <Check className="w-4 h-4 text-green-600 mr-1" />
          <span className="text-green-600">Copiado</span>
        </>
      ) : (
        <>
          <Copy className="w-4 h-4 text-gray-600 mr-1" />
          <span className="text-gray-600">Copiar</span>
        </>
      )}
    </button>
  )

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg">
        <div className="flex items-center">
          <Smartphone className="w-8 h-8 mr-3" />
          <div>
            <h3 className="text-xl font-bold">Referência Multibanco</h3>
            <p className="text-blue-100 text-sm">
              Use estes dados para pagar no Multibanco, MB WAY ou homebanking
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Status de expiração */}
        {isExpired() && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-red-600 mr-2" />
              <span className="text-red-800 font-medium">Esta referência expirou</span>
            </div>
            <p className="text-red-600 text-sm mt-1">
              Por favor, gere uma nova referência para efetuar o pagamento.
            </p>
          </div>
        )}

        {/* Dados da referência */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Entidade */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Entidade
            </label>
            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
              <span className="text-2xl font-mono font-bold text-gray-900">
                {entity}
              </span>
              <CopyButton text={entity} field="entity" label="entidade" />
            </div>
          </div>

          {/* Referência */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Referência
            </label>
            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
              <span className="text-2xl font-mono font-bold text-gray-900">
                {reference}
              </span>
              <CopyButton text={reference} field="reference" label="referência" />
            </div>
          </div>
        </div>

        {/* Valor */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Valor a Pagar
          </label>
          <div className="flex items-center justify-between bg-green-50 rounded-lg p-4">
            <div className="flex items-center">
              <Euro className="w-6 h-6 text-green-600 mr-2" />
              <span className="text-3xl font-bold text-green-800">
                {formatAmount(amount)}
              </span>
            </div>
            <CopyButton 
              text={amount.toFixed(2)} 
              field="amount" 
              label="valor" 
            />
          </div>
        </div>

        {/* Descrição */}
        {description && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Descrição
            </label>
            <div className="bg-gray-50 rounded-lg p-3">
              <span className="text-gray-900">{description}</span>
            </div>
          </div>
        )}

        {/* Data de expiração */}
        {expiresAt && !isExpired() && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-yellow-600 mr-2" />
              <div>
                <span className="text-yellow-800 font-medium">
                  Válido até: {formatExpiryDate(expiresAt)}
                </span>
                <p className="text-yellow-700 text-sm mt-1">
                  Certifique-se de efetuar o pagamento antes desta data.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Instruções */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-3">Como pagar:</h4>
          <div className="space-y-2 text-sm text-blue-800">
            <div className="flex items-start">
              <span className="font-bold mr-2">1.</span>
              <span>Vá a um terminal Multibanco ou use o MB WAY</span>
            </div>
            <div className="flex items-start">
              <span className="font-bold mr-2">2.</span>
              <span>Selecione "Pagamentos" ou "Pagar Serviços"</span>
            </div>
            <div className="flex items-start">
              <span className="font-bold mr-2">3.</span>
              <span>Introduza a entidade: <strong>{entity}</strong></span>
            </div>
            <div className="flex items-start">
              <span className="font-bold mr-2">4.</span>
              <span>Introduza a referência: <strong>{reference}</strong></span>
            </div>
            <div className="flex items-start">
              <span className="font-bold mr-2">5.</span>
              <span>Confirme o valor: <strong>{formatAmount(amount)}</strong></span>
            </div>
            <div className="flex items-start">
              <span className="font-bold mr-2">6.</span>
              <span>Complete o pagamento</span>
            </div>
          </div>
        </div>

        {/* Botão para copiar todos os dados */}
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              const allData = `Entidade: ${entity}\nReferência: ${reference}\nValor: ${formatAmount(amount)}`
              copyToClipboard(allData, 'all')
            }}
            className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            {copiedField === 'all' ? (
              <>
                <Check className="w-5 h-5 mr-2" />
                Dados Copiados
              </>
            ) : (
              <>
                <Copy className="w-5 h-5 mr-2" />
                Copiar Todos os Dados
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
