'use client'

import { useState, useEffect } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import { 
  CreditCard, 
  Smartphone, 
  Loader2, 
  CheckCircle, 
  XCircle,
  AlertCircle
} from 'lucide-react'

// Carregar Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

interface UnifiedPaymentFormProps {
  amount: number
  currency?: string
  description: string
  metadata?: Record<string, string>
  customerEmail?: string
  customerName?: string
  onSuccess?: (paymentIntent: any) => void
  onError?: (error: string) => void
  onCancel?: () => void
  returnUrl?: string
  appearance?: any
}

interface PaymentAvailability {
  klarna: {
    available: boolean
    methods: string[]
    minAmount?: number
    maxAmount?: number
  }
  multibanco: boolean
  card: boolean
}

export default function UnifiedPaymentForm({
  amount,
  currency = 'EUR',
  description,
  metadata = {},
  customerEmail,
  customerName,
  onSuccess,
  onError,
  onCancel,
  returnUrl,
  appearance
}: UnifiedPaymentFormProps) {
  const [clientSecret, setClientSecret] = useState<string>('')
  const [paymentAvailability, setPaymentAvailability] = useState<PaymentAvailability | null>(null)
  const [selectedMethod, setSelectedMethod] = useState<'card' | 'multibanco' | 'klarna'>('card')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    initializePayment()
  }, [amount, currency, selectedMethod])

  const initializePayment = async () => {
    try {
      setLoading(true)
      setError('')

      // Verificar disponibilidade de métodos de pagamento
      const availabilityResponse = await fetch(
        `/api/payments/create-payment-intent?country=PT&currency=${currency}&amount=${amount}`
      )
      const availability = await availabilityResponse.json()
      setPaymentAvailability(availability)

      // Criar PaymentIntent
      const response = await fetch('/api/payments/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount,
          currency,
          paymentMethod: selectedMethod,
          metadata: {
            ...metadata,
            description
          },
          customerEmail,
          customerName,
          returnUrl: returnUrl || `${window.location.origin}/payment/success`
        })
      })

      const data = await response.json()

      if (response.ok) {
        setClientSecret(data.paymentIntent.client_secret)
      } else {
        setError(data.error || 'Erro ao inicializar pagamento')
      }

    } catch (error) {
      console.error('Erro ao inicializar pagamento:', error)
      setError('Erro ao inicializar pagamento')
    } finally {
      setLoading(false)
    }
  }

  const handleMethodChange = (method: 'card' | 'multibanco' | 'klarna') => {
    if (selectedMethod !== method) {
      setSelectedMethod(method)
      setClientSecret('') // Limpar para forçar recriação
    }
  }

  // Configurações do Elements
  const elementsOptions = {
    clientSecret,
    appearance: appearance || {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#0570de',
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px'
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">A inicializar pagamento...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <XCircle className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-red-800 font-medium">Erro no Pagamento</h3>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        </div>
        {onCancel && (
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Voltar
          </button>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto">
      {/* Seleção de Método de Pagamento */}
      {paymentAvailability && (
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Escolha o método de pagamento
          </h3>
          
          <div className="space-y-3">
            {/* Cartão */}
            {paymentAvailability.card && (
              <label className="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="card"
                  checked={selectedMethod === 'card'}
                  onChange={() => handleMethodChange('card')}
                  className="mr-3"
                />
                <CreditCard className="w-5 h-5 text-gray-600 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">Cartão de Crédito/Débito</div>
                  <div className="text-sm text-gray-500">Visa, Mastercard, American Express</div>
                </div>
              </label>
            )}

            {/* Multibanco */}
            {paymentAvailability.multibanco && (
              <label className="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="multibanco"
                  checked={selectedMethod === 'multibanco'}
                  onChange={() => handleMethodChange('multibanco')}
                  className="mr-3"
                />
                <Smartphone className="w-5 h-5 text-gray-600 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">Multibanco</div>
                  <div className="text-sm text-gray-500">Referência para pagamento</div>
                </div>
              </label>
            )}

            {/* Klarna */}
            {paymentAvailability.klarna.available && (
              <label className="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="klarna"
                  checked={selectedMethod === 'klarna'}
                  onChange={() => handleMethodChange('klarna')}
                  className="mr-3"
                />
                <div className="w-5 h-5 bg-pink-500 rounded mr-3 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">K</span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Klarna</div>
                  <div className="text-sm text-gray-500">
                    {paymentAvailability.klarna.methods.includes('pay_later') && 'Pague depois, '}
                    {paymentAvailability.klarna.methods.includes('pay_in_installments') && 'Pague em prestações'}
                  </div>
                </div>
              </label>
            )}
          </div>
        </div>
      )}

      {/* Resumo do Pagamento */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Total a pagar:</span>
          <span className="text-xl font-bold text-gray-900">
            {new Intl.NumberFormat('pt-PT', {
              style: 'currency',
              currency: currency
            }).format(amount)}
          </span>
        </div>
        <div className="text-sm text-gray-500 mt-1">{description}</div>
      </div>

      {/* Formulário de Pagamento */}
      {clientSecret && (
        <Elements stripe={stripePromise} options={elementsOptions}>
          <PaymentForm
            selectedMethod={selectedMethod}
            onSuccess={onSuccess}
            onError={onError}
            returnUrl={returnUrl}
          />
        </Elements>
      )}
    </div>
  )
}

// Componente interno para o formulário de pagamento
function PaymentForm({
  selectedMethod,
  onSuccess,
  onError,
  returnUrl
}: {
  selectedMethod: string
  onSuccess?: (paymentIntent: any) => void
  onError?: (error: string) => void
  returnUrl?: string
}) {
  const stripe = useStripe()
  const elements = useElements()
  const [processing, setProcessing] = useState(false)
  const [message, setMessage] = useState<string>('')

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setProcessing(true)
    setMessage('')

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: returnUrl || `${window.location.origin}/payment/success`
        },
        redirect: selectedMethod === 'card' ? 'if_required' : 'always'
      })

      if (error) {
        setMessage(error.message || 'Erro no pagamento')
        onError?.(error.message || 'Erro no pagamento')
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        setMessage('Pagamento processado com sucesso!')
        onSuccess?.(paymentIntent)
      }

    } catch (error) {
      console.error('Erro ao processar pagamento:', error)
      setMessage('Erro inesperado no pagamento')
      onError?.('Erro inesperado no pagamento')
    } finally {
      setProcessing(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Element */}
      <div className="p-4 border border-gray-200 rounded-lg">
        <PaymentElement />
      </div>

      {/* Mensagem de status */}
      {message && (
        <div className={`flex items-center p-3 rounded-lg ${
          message.includes('sucesso') 
            ? 'bg-green-50 text-green-800 border border-green-200'
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.includes('sucesso') ? (
            <CheckCircle className="w-5 h-5 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-2" />
          )}
          {message}
        </div>
      )}

      {/* Botão de pagamento */}
      <button
        type="submit"
        disabled={!stripe || processing}
        className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {processing ? (
          <>
            <Loader2 className="w-5 h-5 animate-spin mr-2" />
            Processando...
          </>
        ) : (
          <>
            <CreditCard className="w-5 h-5 mr-2" />
            {selectedMethod === 'card' && 'Pagar Agora'}
            {selectedMethod === 'multibanco' && 'Gerar Referência'}
            {selectedMethod === 'klarna' && 'Continuar com Klarna'}
          </>
        )}
      </button>

      {/* Informações de segurança */}
      <div className="text-center text-xs text-gray-500">
        <div className="flex items-center justify-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          Pagamento seguro processado pelo Stripe
        </div>
      </div>
    </form>
  )
}
