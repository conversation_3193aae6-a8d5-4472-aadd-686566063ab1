import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import Stripe from 'stripe'

export interface PaymentRequest {
  amount: number
  currency: string
  description: string
  metadata: Record<string, string>
  customerEmail?: string
  successUrl: string
  cancelUrl: string
}

export interface PaymentResponse {
  success: boolean
  paymentMethod: 'card' | 'multibanco'
  checkoutUrl?: string
  redirectUrl?: string
  paymentIntent?: {
    id: string
    status: string
    client_secret?: string
  }
  multibanco?: {
    entity: string
    reference: string
    amount: number
    expiryDate: Date
  }
  error?: string
}

export interface PendingPayment {
  id: string
  type: 'subscription' | 'marketplace' | 'repair'
  relatedId: string
  amount: number
  currency: string
  status: 'PENDING' | 'COMPLETED' | 'FAILED'
  stripePaymentIntentId?: string
  metadata: Record<string, any>
}

/**
 * Sistema Unificado de Pagamentos V3
 * Suporta subscrições, marketplace e reparações
 * Implementa corretamente Multibanco via Stripe
 */
export class UnifiedPaymentSystem {
  private stripe: Stripe

  constructor(stripeSecretKey?: string) {
    this.stripe = createStripeInstance(stripeSecretKey) as any
  }

  /**
   * Criar pagamento com cartão usando Stripe Checkout
   */
  async createCardPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const checkoutSession = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'payment',
        line_items: [
          {
            price_data: {
              currency: request.currency.toLowerCase(),
              product_data: {
                name: request.description,
                description: request.description
              },
              unit_amount: Math.round(request.amount * 100)
            },
            quantity: 1
          }
        ],
        success_url: request.successUrl,
        cancel_url: request.cancelUrl,
        customer_email: request.customerEmail,
        metadata: request.metadata
      })

      return {
        success: true,
        paymentMethod: 'card',
        checkoutUrl: checkoutSession.url || undefined,
        paymentIntent: {
          id: checkoutSession.payment_intent as string || checkoutSession.id,
          status: 'requires_payment_method'
        }
      }
    } catch (error) {
      console.error('Erro ao criar pagamento com cartão:', error)
      return {
        success: false,
        paymentMethod: 'card',
        error: 'Erro ao criar sessão de pagamento'
      }
    }
  }

  /**
   * Criar pagamento com Multibanco usando PaymentIntent
   * Segue documentação oficial do Stripe
   */
  async createMultibancoPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Criar PaymentIntent com Multibanco
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(request.amount * 100),
        currency: request.currency.toLowerCase(),
        payment_method_types: ['multibanco'],
        metadata: {
          ...request.metadata,
          type: 'multibanco_payment'
        }
      })

      // Confirmar PaymentIntent para gerar referência Multibanco
      const confirmedPaymentIntent = await this.stripe.paymentIntents.confirm(paymentIntent.id, {
        payment_method: {
          type: 'multibanco'
        },
        return_url: request.successUrl
      })

      console.log('PaymentIntent Multibanco confirmado:', {
        id: confirmedPaymentIntent.id,
        status: confirmedPaymentIntent.status,
        next_action: !!confirmedPaymentIntent.next_action
      })

      // Extrair detalhes Multibanco
      let multibancoDetails = null
      if (confirmedPaymentIntent.next_action?.multibanco_display_details) {
        const details = confirmedPaymentIntent.next_action.multibanco_display_details
        multibancoDetails = {
          entity: details.entity || '11249',
          reference: details.reference || '',
          amount: request.amount,
          expiryDate: details.expires_at 
            ? new Date(details.expires_at * 1000) 
            : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
        }

        console.log('✅ Referência Multibanco gerada via Stripe:', {
          entity: details.entity,
          reference: details.reference?.substring(0, 6) + '***'
        })
      }

      return {
        success: true,
        paymentMethod: 'multibanco',
        paymentIntent: {
          id: confirmedPaymentIntent.id,
          status: confirmedPaymentIntent.status,
          client_secret: confirmedPaymentIntent.client_secret || undefined
        },
        multibanco: multibancoDetails || undefined,
        redirectUrl: request.successUrl
      }

    } catch (error) {
      console.error('Erro ao criar pagamento Multibanco:', error)
      return {
        success: false,
        paymentMethod: 'multibanco',
        error: 'Erro ao gerar referência Multibanco'
      }
    }
  }

  /**
   * Criar pagamento pendente na base de dados
   */
  async createPendingPayment(
    type: 'subscription' | 'marketplace' | 'repair',
    relatedId: string,
    amount: number,
    currency: string,
    metadata: Record<string, any> = {}
  ): Promise<PendingPayment> {
    
    // Determinar tabela baseado no tipo
    let pendingPayment: any

    switch (type) {
      case 'subscription':
        pendingPayment = await prisma.subscriptionPayment.create({
          data: {
            subscriptionId: relatedId,
            amount,
            currency,
            status: 'PENDING',
            periodStart: metadata.periodStart || new Date(),
            periodEnd: metadata.periodEnd || new Date()
          }
        })
        break

      case 'marketplace':
        pendingPayment = await prisma.payment.create({
          data: {
            orderId: relatedId,
            amount,
            method: 'pending',
            status: 'PENDING',
            platformFee: metadata.platformFee || 0,
            shopAmount: metadata.shopAmount || amount
          }
        })
        break

      case 'repair':
        pendingPayment = await prisma.payment.create({
          data: {
            repairId: relatedId,
            amount,
            method: 'pending',
            status: 'PENDING',
            platformFee: metadata.platformFee || amount * 0.05,
            shopAmount: metadata.shopAmount || amount * 0.95
          }
        })
        break

      default:
        throw new Error(`Tipo de pagamento não suportado: ${type}`)
    }

    return {
      id: pendingPayment.id,
      type,
      relatedId,
      amount,
      currency,
      status: 'PENDING',
      metadata
    }
  }

  /**
   * Atualizar pagamento pendente com PaymentIntent ID
   */
  async updatePendingPayment(
    paymentId: string,
    type: 'subscription' | 'marketplace' | 'repair',
    stripePaymentIntentId: string
  ): Promise<void> {
    switch (type) {
      case 'subscription':
        await prisma.subscriptionPayment.update({
          where: { id: paymentId },
          data: { stripePaymentIntentId }
        })
        break

      case 'marketplace':
      case 'repair':
        await prisma.payment.update({
          where: { id: paymentId },
          data: { stripeSessionId: stripePaymentIntentId }
        })
        break
    }
  }

  /**
   * Salvar referência Multibanco na base de dados
   */
  async saveMultibancoReference(
    orderId: string,
    entity: string,
    reference: string,
    amount: number,
    customerEmail?: string,
    expiryDate?: Date,
    stripePaymentIntentId?: string
  ): Promise<void> {
    await prisma.multibancoReference.create({
      data: {
        orderId,
        entity,
        reference: reference.replace(/\s/g, ''),
        amount: Math.round(amount * 100),
        customerEmail,
        status: 'PENDING',
        expiryDate: expiryDate || new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        stripePaymentIntentId
      }
    })
  }

  /**
   * Processar pagamento completo (cartão ou Multibanco)
   */
  async processPayment(
    paymentMethod: 'card' | 'multibanco',
    request: PaymentRequest,
    pendingPayment: PendingPayment
  ): Promise<PaymentResponse> {
    let result: PaymentResponse

    if (paymentMethod === 'card') {
      result = await this.createCardPayment(request)
    } else {
      result = await this.createMultibancoPayment(request)
    }

    if (result.success && result.paymentIntent) {
      // Atualizar pagamento pendente com PaymentIntent ID
      await this.updatePendingPayment(
        pendingPayment.id,
        pendingPayment.type,
        result.paymentIntent.id
      )

      // Se Multibanco, salvar referência
      if (paymentMethod === 'multibanco' && result.multibanco) {
        await this.saveMultibancoReference(
          pendingPayment.relatedId,
          result.multibanco.entity,
          result.multibanco.reference,
          result.multibanco.amount,
          request.customerEmail,
          result.multibanco.expiryDate,
          result.paymentIntent.id
        )
      }
    }

    return result
  }
}

/**
 * Factory function para criar instância do sistema unificado
 */
export async function createUnifiedPaymentSystem(customStripeKey?: string): Promise<UnifiedPaymentSystem> {
  // Buscar chave do Stripe se não fornecida
  if (!customStripeKey) {
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })
    customStripeKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
  }

  if (!customStripeKey) {
    throw new Error('Stripe não configurado')
  }

  return new UnifiedPaymentSystem(customStripeKey)
}
