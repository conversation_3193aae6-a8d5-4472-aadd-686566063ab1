'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Edit, 
  Shield, 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Wrench,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff
} from 'lucide-react'

interface Employee {
  id: string
  userId: string
  name: string
  email: string
  phone?: string
  position?: string
  isActive: boolean
  permissions: any
  createdAt: string
  user: {
    id: string
    email: string
    name: string
    isVerified: boolean
    createdAt: string
  }
  assignedRepairs: Array<{
    id: string
    status: string
    customerName: string
    deviceModel: {
      name: string
    }
    createdAt: string
  }>
  stats: {
    totalAssignedRepairs: number
    totalStatusUpdates: number
  }
}

export default function EmployeeDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [employee, setEmployee] = useState<Employee | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchEmployeeDetails()
  }, [params.id])

  const fetchEmployeeDetails = async () => {
    try {
      const response = await fetch(`/api/lojista/employees/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setEmployee(data.employee)
      } else {
        router.push('/lojista/empregados')
      }
    } catch (error) {
      console.error('Erro ao carregar empregado:', error)
      router.push('/lojista/empregados')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'bg-green-100 text-green-800'
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800'
      case 'PENDING_PAYMENT': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'Concluído'
      case 'IN_PROGRESS': return 'Em Progresso'
      case 'PENDING_PAYMENT': return 'Aguarda Pagamento'
      case 'CONFIRMED': return 'Confirmado'
      default: return status
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!employee) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Empregado não encontrado</h1>
          <Link href="/lojista/empregados" className="text-primary-600 hover:text-primary-700">
            Voltar à lista de empregados
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Link
            href="/lojista/empregados"
            className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{employee.name}</h1>
            <p className="text-gray-600 mt-2">
              {employee.position || 'Empregado'} • {employee.isActive ? 'Ativo' : 'Inativo'}
            </p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link
            href={`/lojista/empregados/${employee.id}/editar`}
            className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Edit className="w-4 h-4 mr-2" />
            Editar
          </Link>
          <Link
            href={`/lojista/empregados/${employee.id}/permissoes`}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <Shield className="w-4 h-4 mr-2" />
            Gerir Permissões
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Informações Básicas */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h2>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <User className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-500">Nome</p>
                  <p className="font-medium text-gray-900">{employee.name}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Mail className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <p className="font-medium text-gray-900">{employee.email}</p>
                </div>
              </div>

              {employee.phone && (
                <div className="flex items-center">
                  <Phone className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Telefone</p>
                    <p className="font-medium text-gray-900">{employee.phone}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-500">Criado em</p>
                  <p className="font-medium text-gray-900">
                    {new Date(employee.createdAt).toLocaleDateString('pt-PT')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                {employee.isActive ? (
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500 mr-3" />
                )}
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p className={`font-medium ${employee.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {employee.isActive ? 'Ativo' : 'Inativo'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Estatísticas */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Reparações Atribuídas</span>
                <span className="font-semibold text-gray-900">{employee.stats.totalAssignedRepairs}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Atualizações de Status</span>
                <span className="font-semibold text-gray-900">{employee.stats.totalStatusUpdates}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Reparações Atribuídas */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Reparações Atribuídas ({employee.assignedRepairs.length})
            </h2>
            
            {employee.assignedRepairs.length === 0 ? (
              <div className="text-center py-8">
                <Wrench className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma reparação atribuída</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Este empregado ainda não tem reparações atribuídas.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {employee.assignedRepairs.map((repair) => (
                  <div key={repair.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="font-medium text-gray-900">
                            {repair.deviceModel.name}
                          </h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(repair.status)}`}>
                            {getStatusLabel(repair.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          Cliente: {repair.customerName}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(repair.createdAt).toLocaleDateString('pt-PT')}
                        </p>
                      </div>
                      <Link
                        href={`/lojista/reparacoes/${repair.id}`}
                        className="text-primary-600 hover:text-primary-700 p-2 rounded-lg hover:bg-primary-50 transition-colors"
                        title="Ver detalhes"
                      >
                        <Eye className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
