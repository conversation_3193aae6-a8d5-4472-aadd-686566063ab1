const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkShopData() {
  try {
    console.log('🔍 Verificando dados do lojista...\n');
    
    const shop = await prisma.user.findFirst({
      where: {
        role: 'REPAIR_SHOP'
      },
      include: {
        profile: true
      }
    });
    
    if (!shop) {
      console.log('❌ Nenhum lojista encontrado');
      return;
    }
    
    console.log('🏪 DADOS DO LOJISTA:');
    console.log('Nome:', shop.name);
    console.log('Email:', shop.email);
    
    if (shop.profile) {
      console.log('\n📍 DADOS DE LOCALIZAÇÃO:');
      console.log('Empresa:', shop.profile.companyName || 'N/A');
      console.log('Morada (street):', shop.profile.street || 'N/A');
      console.log('Cidade:', shop.profile.city || 'N/A');
      console.log('Código Postal:', shop.profile.postalCode || 'N/A');
      console.log('Distrito:', shop.profile.district || 'N/A');
      console.log('Endereço completo:', shop.profile.address || 'N/A');
      
      console.log('\n🌍 COORDENADAS GPS:');
      console.log('Latitude:', shop.profile.latitude || 'N/A');
      console.log('Longitude:', shop.profile.longitude || 'N/A');
      
      console.log('\n⚙️ CONFIGURAÇÕES:');
      console.log('Raio de serviço:', shop.profile.serviceRadius || 10, 'km');
      
      // Verificar como o sistema calcula distância
      console.log('\n🧮 COMO O SISTEMA CALCULA DISTÂNCIA:');
      
      if (shop.profile.latitude && shop.profile.longitude) {
        console.log('✅ MÉTODO 1: Distance Matrix API (Google) - MAIS PRECISO');
        console.log('   - Cliente: Geocoding do código postal → coordenadas');
        console.log('   - Lojista: Coordenadas já salvas (' + shop.profile.latitude + ', ' + shop.profile.longitude + ')');
        console.log('   - Cálculo: Google Distance Matrix API (distância real de condução)');
        console.log('   - Resultado: Distância exata + tempo de viagem');
      } else if (shop.profile.postalCode) {
        console.log('⚠️ MÉTODO 2: Códigos Postais (Estimativa) - MENOS PRECISO');
        console.log('   - Cliente: Código postal');
        console.log('   - Lojista: Código postal (' + shop.profile.postalCode + ')');
        console.log('   - Cálculo: Estimativa baseada em distritos portugueses');
        console.log('   - Resultado: Distância aproximada (sem tempo de viagem)');
      } else {
        console.log('❌ MÉTODO 3: Fallback (Muito impreciso)');
        console.log('   - Usa estimativas por cidade ou valores padrão');
        console.log('   - Resultado: Distância mockada/aleatória');
      }
      
      // Verificar se pode fazer geocoding
      console.log('\n🗺️ POSSIBILIDADE DE GEOCODING:');
      
      const addressParts = [
        shop.profile.street,
        shop.profile.city,
        shop.profile.postalCode,
        'Portugal'
      ].filter(Boolean);
      
      const fullAddress = addressParts.join(', ');
      console.log('Endereço completo:', fullAddress);
      
      if (addressParts.length >= 3) {
        console.log('✅ Endereço COMPLETO - Geocoding será muito preciso');
      } else if (addressParts.length >= 2) {
        console.log('⚠️ Endereço PARCIAL - Geocoding pode funcionar');
      } else {
        console.log('❌ Endereço INSUFICIENTE - Geocoding falhará');
      }
      
      if (!shop.profile.latitude || !shop.profile.longitude) {
        console.log('\n💡 RECOMENDAÇÃO:');
        console.log('1. Lojista deve completar o perfil com morada completa');
        console.log('2. Executar geocoding: GET /api/admin/geocode-shops');
        console.log('3. Sistema passará a usar distâncias reais do Google');
      }
      
    } else {
      console.log('❌ Perfil não encontrado');
    }
    
    // Verificar quantos lojistas têm coordenadas vs só endereço
    console.log('\n📊 ESTATÍSTICAS GERAIS:');
    
    const totalShops = await prisma.user.count({
      where: { role: 'REPAIR_SHOP' }
    });
    
    const shopsWithCoords = await prisma.user.count({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          AND: [
            { latitude: { not: null } },
            { longitude: { not: null } }
          ]
        }
      }
    });
    
    const shopsWithPostalCode = await prisma.user.count({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          postalCode: { not: null }
        }
      }
    });
    
    console.log('Total de lojistas:', totalShops);
    console.log('Com coordenadas GPS:', shopsWithCoords, '(distâncias reais)');
    console.log('Só com código postal:', shopsWithPostalCode - shopsWithCoords, '(estimativas)');
    console.log('Sem dados suficientes:', totalShops - shopsWithPostalCode, '(fallback)');
    
    console.log('\n🎯 RESUMO:');
    if (shopsWithCoords === totalShops) {
      console.log('✅ Todos os lojistas têm coordenadas - Sistema 100% preciso');
    } else if (shopsWithPostalCode === totalShops) {
      console.log('⚠️ Alguns lojistas só têm código postal - Sistema parcialmente preciso');
      console.log('💡 Execute geocoding para melhorar precisão');
    } else {
      console.log('❌ Alguns lojistas têm dados insuficientes');
      console.log('💡 Lojistas precisam completar perfis');
    }
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkShopData();
