import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createUnifiedPaymentSystem } from '@/lib/payments/unified-payment-system'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { approvedPrice, paymentMethod } = await request.json()

    // Verificar se a reparação pertence ao cliente
    const repair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        customerId: session.user.id
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    const originalPrice = repair.estimatedPrice ? Number(repair.estimatedPrice) : 0
    const newPrice = Number(approvedPrice)
    const difference = newPrice - originalPrice

    // Atualizar a reparação
    await prisma.repair.update({
      where: {
        id: params.id
      },
      data: {
        estimatedPrice: newPrice,
        finalPrice: newPrice
      }
    })

    // Se há diferença de preço, processar pagamento adicional
    if (difference > 0) {
      // Criar sistema de pagamento unificado
      const paymentSystem = await createUnifiedPaymentSystem()

      // Criar pagamento pendente
      const pendingPayment = await paymentSystem.createPendingPayment(
        'repair',
        params.id,
        difference,
        'EUR',
        {
          type: 'price_difference',
          originalPrice,
          newPrice,
          platformFee: difference * 0.05,
          shopAmount: difference * 0.95
        }
      )

      // Preparar request de pagamento
      const paymentRequest = {
        amount: difference,
        currency: 'EUR',
        description: `Diferença de Preço - Reparação #${params.id.slice(-8)}`,
        metadata: {
          type: 'repair_price_difference',
          repairId: params.id,
          originalPrice: originalPrice.toString(),
          newPrice: newPrice.toString(),
          paymentId: pendingPayment.id,
          userId: session.user.id
        },
        customerEmail: session.user.email || undefined,
        successUrl: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}?payment=success&payment_intent={PAYMENT_INTENT_ID}`,
        cancelUrl: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}?payment=cancelled`
      }

      // Processar pagamento com método padrão (cartão)
      const result = await paymentSystem.processPayment(
        paymentMethod as 'card' | 'multibanco' || 'card',
        paymentRequest,
        pendingPayment
      )

      if (!result.success) {
        return NextResponse.json(
          { message: result.error || 'Erro ao processar pagamento' },
          { status: 500 }
        )
      }

      if (paymentMethod === 'multibanco' && result.multibanco) {
        return NextResponse.json({
          message: 'Preço aprovado. Referência Multibanco gerada.',
          paymentMethod: 'multibanco',
          multibanco: result.multibanco,
          paymentIntent: result.paymentIntent
        })
      } else {
        return NextResponse.json({
          message: 'Preço aprovado. Redirecionando para pagamento...',
          paymentMethod: 'card',
          paymentUrl: result.checkoutUrl
        })
      }
    } else if (difference < 0) {
      // Crédito para o cliente (implementar sistema de créditos)
      return NextResponse.json({
        message: `Preço aprovado. Você receberá um crédito de €${Math.abs(difference).toFixed(2)}.`
      })
    } else {
      // Sem diferença de preço
      return NextResponse.json({
        message: 'Preço aprovado com sucesso!'
      })
    }

  } catch (error) {
    console.error('Erro ao aprovar preço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
