'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Crown, CreditCard, AlertTriangle, CheckCircle, Calendar, Euro } from 'lucide-react'

interface Subscription {
  id: string
  status: string
  billingCycle: string
  plan: {
    id: string
    name: string
    monthlyPrice: number
    yearlyPrice: number
    features: string[]
  }
  payments: Array<{
    id: string
    status: string
    amount: number
    createdAt: string
  }>
}

export default function GestaoSubscricaoV2Page() {
  const { data: session } = useSession()
  const router = useRouter()
  
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSubscription()
  }, [])

  const fetchSubscription = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/lojista/subscription')
      
      if (response.ok) {
        const data = await response.json()
        
        console.log('📊 Dados recebidos da API:', {
          subscription: !!data.subscription,
          stripeData: !!data.stripeData,
          invoicesCount: data.invoices?.length || 0
        })
        
        setSubscription(data.subscription)
      } else {
        const errorData = await response.json()
        setError(errorData.message || 'Erro ao carregar subscrição')
      }
    } catch (error) {
      console.error('Erro ao buscar subscrição:', error)
      setError('Erro de conexão')
    } finally {
      setIsLoading(false)
    }
  }

  const getCurrentPrice = () => {
    try {
      if (!subscription?.plan) {
        console.warn('getCurrentPrice: subscription ou plan não definido')
        return 0
      }
      
      return subscription.billingCycle === 'MONTHLY'
        ? subscription.plan.monthlyPrice
        : subscription.plan.yearlyPrice
    } catch (error) {
      console.error('Erro em getCurrentPrice:', error)
      return 0
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando dados da subscrição...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong className="font-bold">❌ Erro:</strong>
            <span className="block sm:inline mt-2">{error}</span>
          </div>
          <div className="mt-4 space-x-2">
            <button 
              onClick={fetchSubscription}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Tentar Novamente
            </button>
            <button 
              onClick={() => router.push('/lojista')}
              className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
            >
              Voltar ao Dashboard
            </button>
          </div>
        </div>
      </div>
    )
  }

  // No subscription state
  if (!subscription) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Gestão de Subscrição</h1>
            <Link href="/lojista" className="text-blue-600 hover:text-blue-800">
              <ArrowLeft className="w-5 h-5 inline mr-1" />
              Voltar
            </Link>
          </div>

          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Nenhuma Subscrição Ativa</h2>
            <p className="text-gray-600 mb-6">
              Subscreva um plano para desbloquear funcionalidades avançadas
            </p>
            <Link 
              href="/lojista/upgrade"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Crown className="w-5 h-5 mr-2" />
              Ver Planos
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Main subscription view
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestão de Subscrição</h1>
            <p className="text-gray-600">Gerir a sua subscrição e pagamentos</p>
          </div>
          <div className="flex space-x-2">
            <Link 
              href="/lojista/upgrade"
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700"
            >
              <Crown className="w-4 h-4 mr-2" />
              Upgrade
            </Link>
            <Link 
              href="/lojista"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Link>
          </div>
        </div>

        {/* Subscription Status Alert */}
        {subscription.status === 'INCOMPLETE' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
              <div>
                <h3 className="font-medium text-yellow-800 mb-1">Pagamento Pendente</h3>
                <p className="text-sm text-yellow-700">
                  A sua subscrição será ativada automaticamente após a confirmação do pagamento.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Current Plan Card */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Crown className="w-8 h-8 text-yellow-500 mr-3" />
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  {subscription.plan?.name || 'Plano não definido'}
                </h2>
                <p className="text-gray-600">Plano atual</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                €{getCurrentPrice()}/mês
              </div>
              <div className="text-sm text-gray-600">
                Status: <span className={`font-medium ${
                  subscription.status === 'ACTIVE' ? 'text-green-600' : 
                  subscription.status === 'INCOMPLETE' ? 'text-yellow-600' : 
                  'text-red-600'
                }`}>
                  {subscription.status}
                </span>
              </div>
            </div>
          </div>

          {/* Plan Features */}
          {subscription.plan?.features && subscription.plan.features.length > 0 && (
            <div className="border-t pt-4">
              <h3 className="font-medium text-gray-900 mb-3">Funcionalidades incluídas:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {subscription.plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Recent Payments */}
        {subscription.payments && subscription.payments.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Pagamentos Recentes</h3>
            <div className="space-y-3">
              {subscription.payments.slice(0, 5).map((payment) => (
                <div key={payment.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}
                      </div>
                      <div className="text-xs text-gray-600">
                        Status: {payment.status}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Euro className="w-4 h-4 text-gray-400 mr-1" />
                    <span className="font-medium text-gray-900">
                      {payment.amount.toFixed(2)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
