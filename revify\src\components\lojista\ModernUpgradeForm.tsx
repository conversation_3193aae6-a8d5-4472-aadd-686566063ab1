'use client'

import { useState } from 'react'
import UnifiedPaymentForm from '@/components/payments/UnifiedPaymentForm'
import { 
  Crown, 
  Check, 
  X, 
  Zap, 
  Shield, 
  Star,
  ArrowRight,
  Calendar
} from 'lucide-react'

interface Plan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  isPopular?: boolean
  color: string
}

interface ModernUpgradeFormProps {
  plans: Plan[]
  currentPlan?: Plan
  onSuccess?: (result: any) => void
  onCancel?: () => void
  isOpen: boolean
}

export default function ModernUpgradeForm({
  plans,
  currentPlan,
  onSuccess,
  onCancel,
  isOpen
}: ModernUpgradeFormProps) {
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null)
  const [billingCycle, setBillingCycle] = useState<'MONTHLY' | 'YEARLY'>('MONTHLY')
  const [showPayment, setShowPayment] = useState(false)

  if (!isOpen) return null

  const handlePlanSelect = (plan: Plan) => {
    setSelectedPlan(plan)
    setShowPayment(true)
  }

  const handlePaymentSuccess = (result: any) => {
    console.log('Upgrade bem-sucedido:', result)
    onSuccess?.(result)
  }

  const handlePaymentError = (error: string) => {
    console.error('Erro no upgrade:', error)
    alert(`Erro no upgrade: ${error}`)
  }

  const handleBack = () => {
    if (showPayment) {
      setShowPayment(false)
      setSelectedPlan(null)
    } else {
      onCancel?.()
    }
  }

  const getPrice = (plan: Plan) => {
    return billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice
  }

  const getYearlySavings = (plan: Plan) => {
    const monthlyTotal = plan.monthlyPrice * 12
    const yearlyPrice = plan.yearlyPrice
    return monthlyTotal - yearlyPrice
  }

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'premium':
        return <Crown className="w-6 h-6" />
      case 'pro':
        return <Zap className="w-6 h-6" />
      case 'enterprise':
        return <Shield className="w-6 h-6" />
      default:
        return <Star className="w-6 h-6" />
    }
  }

  // Se estiver no modo de pagamento
  if (showPayment && selectedPlan) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Upgrade para {selectedPlan.name}
                </h2>
                <p className="text-gray-600">
                  Finalize o seu upgrade para desbloquear novas funcionalidades
                </p>
              </div>
              <button
                onClick={handleBack}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Resumo do Plano */}
            <div className={`bg-gradient-to-r ${selectedPlan.color} rounded-lg p-6 mb-6 text-white`}>
              <div className="flex items-center mb-4">
                {getPlanIcon(selectedPlan.name)}
                <div className="ml-3">
                  <h3 className="text-xl font-bold">{selectedPlan.name}</h3>
                  <p className="opacity-90">{selectedPlan.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-3xl font-bold">
                    €{getPrice(selectedPlan).toFixed(2)}
                  </div>
                  <div className="opacity-90">
                    {billingCycle === 'MONTHLY' ? 'por mês' : 'por ano'}
                  </div>
                  {billingCycle === 'YEARLY' && (
                    <div className="text-sm opacity-75 mt-1">
                      Poupa €{getYearlySavings(selectedPlan).toFixed(2)} por ano
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-sm opacity-90 mb-2">Funcionalidades incluídas:</div>
                  <div className="space-y-1">
                    {selectedPlan.features.slice(0, 3).map((feature, index) => (
                      <div key={index} className="flex items-center text-sm">
                        <Check className="w-4 h-4 mr-2" />
                        {feature}
                      </div>
                    ))}
                    {selectedPlan.features.length > 3 && (
                      <div className="text-sm opacity-75">
                        +{selectedPlan.features.length - 3} mais funcionalidades
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Formulário de Pagamento */}
            <UnifiedPaymentForm
              amount={getPrice(selectedPlan)}
              currency="EUR"
              description={`Upgrade para ${selectedPlan.name} - ${billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`}
              metadata={{
                type: 'subscription',
                planId: selectedPlan.id,
                planName: selectedPlan.name,
                billingCycle,
                upgradeFrom: currentPlan?.id || 'none'
              }}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              onCancel={handleBack}
              returnUrl={`${window.location.origin}/lojista/subscricao/success`}
              appearance={{
                theme: 'stripe' as const,
                variables: {
                  colorPrimary: '#3b82f6',
                  colorBackground: '#ffffff',
                  colorText: '#1f2937',
                  colorDanger: '#ef4444',
                  fontFamily: 'system-ui, sans-serif',
                  spacingUnit: '4px',
                  borderRadius: '8px'
                }
              }}
            />
          </div>
        </div>
      </div>
    )
  }

  // Seleção de planos
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900">
                Escolha o Seu Plano
              </h2>
              <p className="text-gray-600 mt-2">
                Desbloqueie funcionalidades avançadas para o seu negócio
              </p>
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Toggle de Ciclo de Faturação */}
          <div className="flex items-center justify-center mb-8">
            <div className="bg-gray-100 rounded-lg p-1 flex">
              <button
                onClick={() => setBillingCycle('MONTHLY')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'MONTHLY'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Calendar className="w-4 h-4 inline mr-2" />
                Mensal
              </button>
              <button
                onClick={() => setBillingCycle('YEARLY')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'YEARLY'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Calendar className="w-4 h-4 inline mr-2" />
                Anual
                <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                  Poupe até 20%
                </span>
              </button>
            </div>
          </div>

          {/* Grid de Planos */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`relative bg-white border-2 rounded-lg p-6 transition-all hover:shadow-lg ${
                  plan.isPopular 
                    ? 'border-blue-500 shadow-lg' 
                    : 'border-gray-200 hover:border-gray-300'
                } ${
                  currentPlan?.id === plan.id 
                    ? 'opacity-50 cursor-not-allowed' 
                    : 'cursor-pointer'
                }`}
                onClick={() => currentPlan?.id !== plan.id && handlePlanSelect(plan)}
              >
                {/* Badge Popular */}
                {plan.isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                      MAIS POPULAR
                    </span>
                  </div>
                )}

                {/* Badge Plano Atual */}
                {currentPlan?.id === plan.id && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                      PLANO ATUAL
                    </span>
                  </div>
                )}

                {/* Header do Plano */}
                <div className="text-center mb-6">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${plan.color} text-white mb-4`}>
                    {getPlanIcon(plan.name)}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {plan.description}
                  </p>
                </div>

                {/* Preço */}
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-gray-900">
                    €{getPrice(plan).toFixed(2)}
                  </div>
                  <div className="text-gray-600">
                    {billingCycle === 'MONTHLY' ? 'por mês' : 'por ano'}
                  </div>
                  {billingCycle === 'YEARLY' && (
                    <div className="text-sm text-green-600 mt-1">
                      Poupa €{getYearlySavings(plan).toFixed(2)} por ano
                    </div>
                  )}
                </div>

                {/* Funcionalidades */}
                <div className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Botão */}
                <button
                  disabled={currentPlan?.id === plan.id}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                    currentPlan?.id === plan.id
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : plan.isPopular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-900 text-white hover:bg-gray-800'
                  }`}
                >
                  {currentPlan?.id === plan.id ? (
                    'Plano Atual'
                  ) : (
                    <>
                      Escolher {plan.name}
                      <ArrowRight className="w-4 h-4 inline ml-2" />
                    </>
                  )}
                </button>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>
              Todos os planos incluem suporte técnico e atualizações gratuitas.
              <br />
              Pode cancelar ou alterar o seu plano a qualquer momento.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
