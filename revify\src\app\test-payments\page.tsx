'use client'

import { useState } from 'react'
import PaymentExample from '@/components/payments/PaymentExample'
import { ArrowLeft } from 'lucide-react'

export default function TestPaymentsPage() {
  const [currentTest, setCurrentTest] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<any[]>([])

  const testScenarios = [
    {
      id: 'repair-payment',
      name: 'Pagamento de Reparação',
      description: 'Simular pagamento de uma reparação de €45.99',
      amount: 45.99,
      type: 'repair',
      metadata: {
        type: 'repair',
        repairId: 'repair_test_123',
        description: 'Reparação de smartphone'
      }
    },
    {
      id: 'marketplace-order',
      name: 'Encomenda Marketplace',
      description: 'Simular compra no marketplace de €129.50',
      amount: 129.50,
      type: 'marketplace',
      metadata: {
        type: 'marketplace',
        orderId: 'order_test_456',
        lojistId: 'lojist_test_789',
        lojistName: 'Loja Teste'
      }
    },
    {
      id: 'subscription-payment',
      name: 'Pagamento de Subscrição',
      description: 'Simular pagamento mensal de subscrição de €29.99',
      amount: 29.99,
      type: 'subscription',
      metadata: {
        type: 'subscription',
        subscriptionId: 'sub_test_101',
        plan: 'premium_monthly'
      }
    },
    {
      id: 'small-payment',
      name: 'Pagamento Pequeno',
      description: 'Testar pagamento de valor baixo €5.00',
      amount: 5.00,
      type: 'addon',
      metadata: {
        type: 'addon',
        description: 'Addon de funcionalidade'
      }
    },
    {
      id: 'large-payment',
      name: 'Pagamento Grande',
      description: 'Testar pagamento de valor alto €999.99',
      amount: 999.99,
      type: 'upgrade',
      metadata: {
        type: 'upgrade',
        description: 'Upgrade para plano enterprise'
      }
    }
  ]

  const handleTestComplete = (scenarioId: string, result: any) => {
    console.log(`Teste ${scenarioId} concluído:`, result)
    
    setTestResults(prev => [...prev, {
      scenarioId,
      timestamp: new Date().toISOString(),
      result,
      success: result.type === 'payment_succeeded' || result.type === 'multibanco_reference'
    }])

    // Voltar para lista de testes
    setCurrentTest(null)
  }

  const handleTestCancel = () => {
    setCurrentTest(null)
  }

  const clearResults = () => {
    setTestResults([])
  }

  // Se um teste está ativo, mostrar o componente de pagamento
  if (currentTest) {
    const scenario = testScenarios.find(s => s.id === currentTest)
    if (!scenario) return null

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto p-6">
          <div className="mb-6">
            <button
              onClick={handleTestCancel}
              className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar aos Testes
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              {scenario.name}
            </h1>
            <p className="text-gray-600">{scenario.description}</p>
          </div>

          <PaymentExample
            amount={scenario.amount}
            currency="EUR"
            description={scenario.description}
            metadata={scenario.metadata}
            customerEmail="<EMAIL>"
            customerName="Cliente Teste"
            onComplete={(result) => handleTestComplete(scenario.id, result)}
            onCancel={handleTestCancel}
          />
        </div>
      </div>
    )
  }

  // Lista de testes disponíveis
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 Testes de Integração Stripe
          </h1>
          <p className="text-gray-600">
            Teste todos os cenários de pagamento da plataforma Revify
          </p>
        </div>

        {/* Informações importantes */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">
            ℹ️ Informações de Teste
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h3 className="font-medium mb-2">Cartões de Teste:</h3>
              <ul className="space-y-1">
                <li><code>4242424242424242</code> - Sempre funciona</li>
                <li><code>4000000000003220</code> - Requer 3D Secure</li>
                <li><code>4000000000000002</code> - Sempre falha</li>
                <li><code>4000000000009995</code> - Saldo insuficiente</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Dados de Teste:</h3>
              <ul className="space-y-1">
                <li>Expiração: <code>12/34</code></li>
                <li>CVC: <code>123</code></li>
                <li>CEP: <code>12345</code></li>
                <li>Email: <code><EMAIL></code></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Resultados dos testes */}
        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                📊 Resultados dos Testes
              </h2>
              <button
                onClick={clearResults}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Limpar
              </button>
            </div>
            
            <div className="space-y-3">
              {testResults.map((result, index) => {
                const scenario = testScenarios.find(s => s.id === result.scenarioId)
                return (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg ${
                      result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                    }`}
                  >
                    <div>
                      <span className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                        {scenario?.name}
                      </span>
                      <span className="text-sm text-gray-500 ml-2">
                        {new Date(result.timestamp).toLocaleTimeString('pt-PT')}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className={`text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                        {result.success ? '✅ Sucesso' : '❌ Falha'}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Lista de cenários de teste */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testScenarios.map((scenario) => (
            <div
              key={scenario.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {scenario.name}
                </h3>
                <p className="text-gray-600 text-sm mb-3">
                  {scenario.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-blue-600">
                    {new Intl.NumberFormat('pt-PT', {
                      style: 'currency',
                      currency: 'EUR'
                    }).format(scenario.amount)}
                  </span>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {scenario.type}
                  </span>
                </div>
              </div>

              <button
                onClick={() => setCurrentTest(scenario.id)}
                className="w-full px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                Iniciar Teste
              </button>

              {/* Mostrar resultado anterior se existir */}
              {testResults.find(r => r.scenarioId === scenario.id) && (
                <div className="mt-3 text-center">
                  <span className="text-xs text-green-600">
                    ✅ Testado com sucesso
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Testes adicionais */}
        <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            🔧 Testes Técnicos
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => window.open('/admin/pagamentos', '_blank')}
              className="flex items-center justify-center px-4 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors"
            >
              📊 Abrir Admin de Pagamentos
            </button>
            
            <button
              onClick={async () => {
                try {
                  const response = await fetch('/api/admin/payments/reconcile', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'all' })
                  })
                  const result = await response.json()
                  alert(`Reconciliação: ${result.message}`)
                } catch (error) {
                  alert(`Erro: ${error.message}`)
                }
              }}
              className="flex items-center justify-center px-4 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
            >
              🔄 Executar Reconciliação
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            💡 Dica: Use o Chrome DevTools para monitorizar as chamadas de API durante os testes
          </p>
        </div>
      </div>
    </div>
  )
}
