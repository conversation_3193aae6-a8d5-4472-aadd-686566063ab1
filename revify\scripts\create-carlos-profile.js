const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createCarlosProfile() {
  try {
    console.log('👤 Criando perfil para Carlos...')

    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!user) {
      console.log('❌ Utilizador não encontrado!')
      return
    }

    // Verificar se já tem perfil
    const existingProfile = await prisma.profile.findUnique({
      where: { userId: user.id }
    })

    if (existingProfile) {
      console.log('✅ Perfil já existe!')
      return
    }

    // Criar perfil
    const profile = await prisma.profile.create({
      data: {
        userId: user.id,
        phone: '+*********** 678',
        address: 'Lisboa, Portugal',
        city: 'Lisboa',
        country: 'Portugal',
        description: 'Super Administrador da plataforma Revify',
        companyName: 'SellRocket',
        emailNotifications: true,
        smsNotifications: false
      }
    })

    console.log('✅ Perfil criado com sucesso!')
    console.log('🏢 Empresa:', profile.companyName)
    console.log('📱 Telefone:', profile.phone)
    console.log('📍 Morada:', profile.address)
    console.log('🏙️ Cidade:', profile.city)
    console.log('📝 Descrição:', profile.description)

  } catch (error) {
    console.error('❌ Erro ao criar perfil:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createCarlosProfile()
