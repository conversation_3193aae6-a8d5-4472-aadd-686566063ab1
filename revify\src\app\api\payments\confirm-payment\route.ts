import { NextRequest, NextResponse } from 'next/server'
import { createStripeInstance } from '@/lib/stripe'
import { updatePaymentHistoryStatus } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      paymentIntentId, 
      paymentMethodId,
      returnUrl,
      paymentMethodData
    } = body

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment Intent ID é obrigatório' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance()

    console.log('🔄 Confirmando PaymentIntent:', paymentIntentId)

    // Buscar PaymentIntent atual
    const currentPaymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
    
    if (currentPaymentIntent.status === 'succeeded') {
      return NextResponse.json({
        success: true,
        paymentIntent: {
          id: currentPaymentIntent.id,
          status: currentPaymentIntent.status,
          client_secret: currentPaymentIntent.client_secret
        },
        message: 'Pagamento já foi processado com sucesso'
      })
    }

    // Preparar dados de confirmação
    const confirmData: any = {}

    if (paymentMethodId) {
      confirmData.payment_method = paymentMethodId
    } else if (paymentMethodData) {
      confirmData.payment_method = paymentMethodData
    }

    if (returnUrl) {
      confirmData.return_url = returnUrl
    }

    // Confirmar PaymentIntent
    const confirmedPaymentIntent = await stripe.paymentIntents.confirm(
      paymentIntentId,
      confirmData
    )

    console.log('✅ PaymentIntent confirmado:', {
      id: confirmedPaymentIntent.id,
      status: confirmedPaymentIntent.status
    })

    // Atualizar histórico baseado no status
    if (confirmedPaymentIntent.status === 'succeeded') {
      await updatePaymentHistoryStatus(paymentIntentId, 'SUCCEEDED')
    } else if (confirmedPaymentIntent.status === 'processing') {
      await updatePaymentHistoryStatus(paymentIntentId, 'PROCESSING')
    } else if (confirmedPaymentIntent.status === 'requires_action') {
      await updatePaymentHistoryStatus(paymentIntentId, 'PENDING')
    }

    const response: any = {
      success: true,
      paymentIntent: {
        id: confirmedPaymentIntent.id,
        status: confirmedPaymentIntent.status,
        client_secret: confirmedPaymentIntent.client_secret
      }
    }

    // Lidar com ações necessárias
    if (confirmedPaymentIntent.next_action) {
      const nextAction = confirmedPaymentIntent.next_action

      // Redirecionamento (Klarna, 3D Secure, etc.)
      if (nextAction.redirect_to_url) {
        response.requiresAction = true
        response.redirectUrl = nextAction.redirect_to_url.url
      }

      // Multibanco - mostrar detalhes
      if (nextAction.multibanco_display_details) {
        const multibancoDetails = nextAction.multibanco_display_details
        response.multibanco = {
          entity: multibancoDetails.entity,
          reference: multibancoDetails.reference,
          amount: multibancoDetails.amount_remaining,
          expires_at: multibancoDetails.expires_at
        }
      }

      // OXXO (se suportado no futuro)
      if (nextAction.oxxo_display_details) {
        response.oxxo = nextAction.oxxo_display_details
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Erro ao confirmar pagamento:', error)

    // Atualizar histórico como falhado se possível
    if (request.body && JSON.parse(await request.text()).paymentIntentId) {
      try {
        await updatePaymentHistoryStatus(
          JSON.parse(await request.text()).paymentIntentId, 
          'FAILED', 
          error.message
        )
      } catch (historyError) {
        console.error('Erro ao atualizar histórico:', historyError)
      }
    }

    // Erros específicos do Stripe
    if (error.type === 'StripeCardError') {
      return NextResponse.json(
        { 
          error: 'Erro no cartão', 
          details: error.message,
          decline_code: error.decline_code 
        },
        { status: 400 }
      )
    }

    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json(
        { error: 'Pedido inválido', details: error.message },
        { status: 400 }
      )
    }

    if (error.code === 'payment_intent_authentication_failure') {
      return NextResponse.json(
        { error: 'Falha na autenticação', details: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Endpoint para verificar status de um PaymentIntent
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const paymentIntentId = searchParams.get('payment_intent_id')

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment Intent ID é obrigatório' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance()

    // Buscar PaymentIntent
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    const response: any = {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      created: paymentIntent.created,
      metadata: paymentIntent.metadata
    }

    // Adicionar detalhes específicos baseado no status
    if (paymentIntent.status === 'requires_action' && paymentIntent.next_action) {
      const nextAction = paymentIntent.next_action

      if (nextAction.redirect_to_url) {
        response.requiresAction = true
        response.redirectUrl = nextAction.redirect_to_url.url
      }

      if (nextAction.multibanco_display_details) {
        response.multibanco = {
          entity: nextAction.multibanco_display_details.entity,
          reference: nextAction.multibanco_display_details.reference,
          amount: nextAction.multibanco_display_details.amount_remaining,
          expires_at: nextAction.multibanco_display_details.expires_at
        }
      }
    }

    // Adicionar informações do método de pagamento se disponível
    if (paymentIntent.payment_method) {
      try {
        const paymentMethod = await stripe.paymentMethods.retrieve(paymentIntent.payment_method as string)
        response.paymentMethod = {
          type: paymentMethod.type,
          details: getPaymentMethodDetails(paymentMethod)
        }
      } catch (pmError) {
        console.warn('Não foi possível buscar método de pagamento:', pmError)
      }
    }

    // Adicionar informações de erro se houver
    if (paymentIntent.last_payment_error) {
      response.lastError = {
        message: paymentIntent.last_payment_error.message,
        type: paymentIntent.last_payment_error.type,
        code: paymentIntent.last_payment_error.code,
        decline_code: paymentIntent.last_payment_error.decline_code
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Erro ao buscar PaymentIntent:', error)

    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json(
        { error: 'PaymentIntent não encontrado', details: error.message },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

/**
 * Extrair detalhes relevantes do método de pagamento
 */
function getPaymentMethodDetails(paymentMethod: any): any {
  switch (paymentMethod.type) {
    case 'card':
      return {
        brand: paymentMethod.card?.brand,
        last4: paymentMethod.card?.last4,
        exp_month: paymentMethod.card?.exp_month,
        exp_year: paymentMethod.card?.exp_year,
        country: paymentMethod.card?.country
      }

    case 'multibanco':
      return {
        entity: paymentMethod.multibanco?.entity,
        reference: paymentMethod.multibanco?.reference
      }

    case 'klarna':
      return {
        country: paymentMethod.klarna?.country,
        preferred_locale: paymentMethod.klarna?.preferred_locale
      }

    default:
      return {}
  }
}
