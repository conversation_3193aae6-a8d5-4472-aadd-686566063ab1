'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { 
  CheckCircle, 
  Crown, 
  Calendar, 
  CreditCard, 
  ArrowRight,
  Home,
  Loader2
} from 'lucide-react'

export default function SubscriptionSuccessPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [paymentData, setPaymentData] = useState<any>(null)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    if (!session) return

    const paymentIntentId = searchParams.get('payment_intent')
    const sessionId = searchParams.get('session_id')

    if (paymentIntentId) {
      verifyPaymentIntent(paymentIntentId)
    } else if (sessionId) {
      verifyCheckoutSession(sessionId)
    } else {
      setError('Parâmetros de pagamento não encontrados')
      setLoading(false)
    }
  }, [session, searchParams])

  const verifyPaymentIntent = async (paymentIntentId: string) => {
    try {
      const response = await fetch(`/api/lojista/subscription/pay-modern?payment_intent_id=${paymentIntentId}`)
      const data = await response.json()

      if (response.ok) {
        setPaymentData(data)
      } else {
        setError(data.message || 'Erro ao verificar pagamento')
      }
    } catch (error) {
      console.error('Erro ao verificar PaymentIntent:', error)
      setError('Erro ao verificar pagamento')
    } finally {
      setLoading(false)
    }
  }

  const verifyCheckoutSession = async (sessionId: string) => {
    try {
      // Para sessões de checkout, podemos buscar dados da subscrição diretamente
      const response = await fetch('/api/lojista/subscription')
      const data = await response.json()

      if (response.ok && data.subscription) {
        setPaymentData({
          subscriptionPayment: {
            subscription: data.subscription
          }
        })
      } else {
        setError('Erro ao carregar dados da subscrição')
      }
    } catch (error) {
      console.error('Erro ao verificar sessão:', error)
      setError('Erro ao verificar pagamento')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Verificando pagamento...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h1 className="text-xl font-bold text-red-800 mb-2">
              Erro na Verificação
            </h1>
            <p className="text-red-600 mb-4">{error}</p>
            <Link
              href="/lojista/subscricao"
              className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              <ArrowRight className="w-4 h-4 mr-2" />
              Voltar à Subscrição
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const subscription = paymentData?.subscriptionPayment?.subscription
  const paymentIntent = paymentData?.paymentIntent

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto p-6">
        {/* Header de Sucesso */}
        <div className="text-center mb-8">
          <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Pagamento Concluído!
          </h1>
          <p className="text-gray-600">
            A sua subscrição foi renovada com sucesso
          </p>
        </div>

        {/* Detalhes da Subscrição */}
        {subscription && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center mb-4">
              <Crown className="w-8 h-8 text-yellow-500 mr-3" />
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  {subscription.plan?.name || 'Plano Premium'}
                </h2>
                <p className="text-gray-600">
                  {subscription.billingCycle === 'MONTHLY' ? 'Plano Mensal' : 'Plano Anual'}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <CreditCard className="w-5 h-5 text-gray-500 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Valor Pago</p>
                  <p className="font-semibold text-gray-900">
                    {subscription.billingCycle === 'MONTHLY' 
                      ? `€${subscription.plan?.monthlyPrice?.toFixed(2) || '0.00'}`
                      : `€${subscription.plan?.yearlyPrice?.toFixed(2) || '0.00'}`
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-gray-500 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Próxima Renovação</p>
                  <p className="font-semibold text-gray-900">
                    {subscription.currentPeriodEnd 
                      ? new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')
                      : 'A calcular...'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Detalhes do Pagamento */}
        {paymentIntent && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-900 mb-2">
              Detalhes do Pagamento
            </h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p>ID do Pagamento: <code className="bg-blue-100 px-1 rounded">{paymentIntent.id}</code></p>
              <p>Status: <span className="font-medium">{paymentIntent.status}</span></p>
              {paymentIntent.paymentMethod && (
                <p>Método: <span className="font-medium">{paymentIntent.paymentMethod.type}</span></p>
              )}
            </div>
          </div>
        )}

        {/* Próximos Passos */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            O que acontece agora?
          </h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="bg-green-100 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5">
                <span className="text-green-600 text-sm font-bold">1</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Subscrição Ativada</p>
                <p className="text-sm text-gray-600">
                  A sua subscrição está agora ativa e todas as funcionalidades estão disponíveis.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5">
                <span className="text-blue-600 text-sm font-bold">2</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Acesso Completo</p>
                <p className="text-sm text-gray-600">
                  Pode agora usar todas as funcionalidades do seu plano sem limitações.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-purple-100 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5">
                <span className="text-purple-600 text-sm font-bold">3</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Renovação Automática</p>
                <p className="text-sm text-gray-600">
                  A sua subscrição será renovada automaticamente na próxima data de vencimento.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Ações */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/lojista/subscricao"
            className="flex-1 flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Crown className="w-5 h-5 mr-2" />
            Gerir Subscrição
          </Link>

          <Link
            href="/lojista"
            className="flex-1 flex items-center justify-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Home className="w-5 h-5 mr-2" />
            Voltar ao Dashboard
          </Link>
        </div>

        {/* Suporte */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Tem alguma questão sobre a sua subscrição?{' '}
            <Link href="/suporte" className="text-blue-600 hover:text-blue-700">
              Contacte o nosso suporte
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
