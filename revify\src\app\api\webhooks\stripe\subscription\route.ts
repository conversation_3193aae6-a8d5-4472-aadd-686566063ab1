import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { PrismaClient } from '@prisma/client'
import { createStripeInstance } from '@/lib/stripe-config'

const prisma = new PrismaClient()

// Mapear status do Stripe para status do Prisma
function mapStripeStatusToPrisma(stripeStatus: string): 'ACTIVE' | 'PAST_DUE' | 'CANCELED' | 'INCOMPLETE' | 'INCOMPLETE_EXPIRED' | 'TRIALING' | 'UNPAID' {
  switch (stripeStatus.toLowerCase()) {
    case 'active':
      return 'ACTIVE'
    case 'past_due':
      return 'PAST_DUE'
    case 'canceled':
    case 'cancelled':
      return 'CANCELED'
    case 'incomplete':
      return 'INCOMPLETE'
    case 'incomplete_expired':
      return 'INCOMPLETE_EXPIRED'
    case 'trialing':
      return 'TRIALING'
    case 'unpaid':
      return 'UNPAID'
    default:
      return 'INCOMPLETE'
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 Webhook Stripe recebido')
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    console.log('📊 Webhook details:', {
      hasSignature: !!signature,
      bodyLength: body.length,
      timestamp: new Date().toISOString()
    })

    if (!signature) {
      return NextResponse.json(
        { message: 'Missing stripe signature' },
        { status: 400 }
      )
    }

    // Buscar webhook secret das configurações
    const webhookSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeWebhookSecret' }
    })

    const webhookSecret = webhookSecretSetting?.value || process.env.STRIPE_WEBHOOK_SECRET

    if (!webhookSecret) {
      return NextResponse.json(
        { message: 'Webhook secret not configured' },
        { status: 400 }
      )
    }

    // Buscar chave do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      return NextResponse.json(
        { message: 'Stripe not configured' },
        { status: 400 }
      )
    }

    const stripe = createStripeInstance(stripeSecretKey)

    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
      console.log('✅ Webhook signature verified successfully')
      console.log('📋 Event type:', event.type)
      console.log('🆔 Event ID:', event.id)
    } catch (err: any) {
      console.error('❌ Webhook signature verification failed:', err.message)
      return NextResponse.json(
        { message: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Processar eventos de subscrição
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object)
        break

      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object)
        break

      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { message: 'Webhook error' },
      { status: 500 }
    )
  }
}

async function handleSubscriptionCreated(subscription: any) {
  try {
    console.log('🎉 Processing subscription.created:', subscription.id)
    const userId = subscription.metadata?.userId
    const planId = subscription.metadata?.planId
    const billingCycle = subscription.metadata?.billingCycle

    console.log('📊 Subscription metadata:', {
      userId,
      planId,
      billingCycle,
      status: subscription.status,
      customerId: subscription.customer
    })

    if (!userId || !planId) {
      console.error('❌ Missing metadata in subscription:', subscription.id)
      return
    }

    // Criar ou atualizar subscrição
    await prisma.subscription.upsert({
      where: { userId },
      create: {
        userId,
        planId,
        status: 'ACTIVE',
        billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: subscription.customer
      },
      update: {
        status: 'ACTIVE',
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: subscription.customer,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000)
      }
    })

    console.log(`Subscription created for user ${userId}`)
  } catch (error) {
    console.error('Error handling subscription created:', error)
  }
}

async function handleSubscriptionUpdated(subscription: any) {
  try {
    const stripeSubscriptionId = subscription.id

    const updatedSubscriptions = await prisma.subscription.updateMany({
      where: { stripeSubscriptionId },
      data: {
        status: mapStripeStatusToPrisma(subscription.status),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    // Se a subscrição foi ativada, completar referrals pendentes
    if (subscription.status === 'active' && updatedSubscriptions.count > 0) {
      const dbSubscription = await prisma.subscription.findFirst({
        where: { stripeSubscriptionId },
        include: { user: true }
      })

      if (dbSubscription && dbSubscription.user.role === 'REPAIR_SHOP') {
        try {
          // Usar o trigger específico para subscrições
          await fetch(`${process.env.NEXTAUTH_URL}/api/referral/trigger/subscription`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              userId: dbSubscription.userId
            })
          })
          console.log(`🎉 Referral trigger executado para lojista: ${dbSubscription.userId}`)
        } catch (referralError) {
          console.error('❌ Erro ao executar trigger de referral:', referralError)
        }
      }
    }

    console.log(`Subscription updated: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling subscription updated:', error)
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  try {
    const stripeSubscriptionId = subscription.id

    await prisma.subscription.updateMany({
      where: { stripeSubscriptionId },
      data: {
        status: 'CANCELED',
        canceledAt: new Date()
      }
    })

    console.log(`Subscription canceled: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling subscription deleted:', error)
  }
}

async function handlePaymentSucceeded(invoice: any) {
  try {
    const stripeSubscriptionId = invoice.subscription

    if (!stripeSubscriptionId) return

    // Buscar subscrição
    const subscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId }
    })

    if (!subscription) {
      console.error('Subscription not found for invoice:', invoice.id)
      return
    }

    // Criar registro de pagamento
    await prisma.subscriptionPayment.create({
      data: {
        subscriptionId: subscription.id,
        amount: invoice.amount_paid / 100, // Converter de centavos
        currency: invoice.currency.toUpperCase(),
        status: 'COMPLETED',
        stripePaymentIntentId: invoice.payment_intent,
        stripeInvoiceId: invoice.id,
        periodStart: new Date(invoice.period_start * 1000),
        periodEnd: new Date(invoice.period_end * 1000)
      }
    })

    // Atualizar status da subscrição se necessário
    if (subscription.status !== 'ACTIVE') {
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: { status: 'ACTIVE' }
      })
    }

    console.log(`Payment succeeded for subscription: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling payment succeeded:', error)
  }
}

async function handlePaymentFailed(invoice: any) {
  try {
    const stripeSubscriptionId = invoice.subscription

    if (!stripeSubscriptionId) return

    // Buscar subscrição
    const subscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId }
    })

    if (!subscription) {
      console.error('Subscription not found for invoice:', invoice.id)
      return
    }

    // Criar registro de pagamento falhado
    await prisma.subscriptionPayment.create({
      data: {
        subscriptionId: subscription.id,
        amount: invoice.amount_due / 100,
        currency: invoice.currency.toUpperCase(),
        status: 'FAILED',
        stripeInvoiceId: invoice.id,
        periodStart: new Date(invoice.period_start * 1000),
        periodEnd: new Date(invoice.period_end * 1000)
      }
    })

    // Atualizar status da subscrição
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { status: 'PAST_DUE' }
    })

    console.log(`Payment failed for subscription: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling payment failed:', error)
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: any) {
  try {
    console.log('🎉 Processing payment_intent.succeeded:', paymentIntent.id)

    // Buscar pagamento pendente associado a este PaymentIntent
    const pendingPayment = await prisma.subscriptionPayment.findFirst({
      where: {
        stripePaymentIntentId: paymentIntent.id,
        status: 'PENDING'
      },
      include: {
        subscription: {
          include: { plan: true }
        }
      }
    })

    if (pendingPayment) {
      // Atualizar pagamento como completado
      await prisma.subscriptionPayment.update({
        where: { id: pendingPayment.id },
        data: {
          status: 'COMPLETED',
          paidAt: new Date()
        }
      })

      // Ativar subscrição
      await prisma.subscription.update({
        where: { id: pendingPayment.subscriptionId },
        data: {
          status: 'ACTIVE',
          stripeCustomerId: paymentIntent.customer
        }
      })

      // Atualizar referência Multibanco se existir
      await prisma.multibancoReference.updateMany({
        where: {
          stripePaymentIntentId: paymentIntent.id
        },
        data: {
          status: 'PAID',
          paidAt: new Date()
        }
      })

      console.log(`✅ Subscription activated via PaymentIntent ${paymentIntent.id}`)
      return
    }

    // Fallback: Se não existe pagamento pendente, pode ser um pagamento Multibanco antigo
    if (paymentIntent.metadata?.userId && paymentIntent.metadata?.planId) {
      const userId = paymentIntent.metadata.userId
      const planId = paymentIntent.metadata.planId
      const billingCycle = paymentIntent.metadata.billingCycle || 'MONTHLY'

      // Criar ou atualizar subscrição
      const subscription = await prisma.subscription.upsert({
        where: { userId },
        create: {
          userId,
          planId,
          status: 'ACTIVE',
          billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + (billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000),
          stripeCustomerId: paymentIntent.customer
        },
        update: {
          status: 'ACTIVE',
          stripeCustomerId: paymentIntent.customer,
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + (billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000)
        }
      })

      // Criar registro de pagamento
      await prisma.subscriptionPayment.create({
        data: {
          subscriptionId: subscription.id,
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency.toUpperCase(),
          status: 'COMPLETED',
          stripePaymentIntentId: paymentIntent.id,
          periodStart: new Date(),
          periodEnd: new Date(Date.now() + (billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000),
          paidAt: new Date()
        }
      })

      console.log(`✅ Subscription activated for user ${userId} via PaymentIntent (fallback)`)
    }

    // Handle Multibanco payments specifically
    if (paymentIntent.metadata?.type === 'multibanco_reference') {
      console.log('🏦 Multibanco payment detected, updating reference status')

      // Update Multibanco reference status
      await prisma.multibancoReference.updateMany({
        where: {
          stripePaymentIntentId: paymentIntent.id
        },
        data: {
          status: 'PAID',
          paidAt: new Date()
        }
      })

      console.log(`✅ Multibanco reference updated for PaymentIntent ${paymentIntent.id}`)
    }

  } catch (error) {
    console.error('❌ Error handling payment_intent.succeeded:', error)
  }
}

async function handleCheckoutSessionCompleted(session: any) {
  try {
    console.log('🎉 Processing checkout.session.completed:', session.id)

    if (session.mode === 'subscription' && session.metadata?.userId) {
      const userId = session.metadata.userId
      const planId = session.metadata.planId
      const billingCycle = session.metadata.billingCycle || 'MONTHLY'

      // Verificar se já existe uma subscrição para este usuário
      const existingSubscription = await prisma.subscription.findFirst({
        where: {
          userId,
          status: {
            in: ['ACTIVE', 'INCOMPLETE', 'PAST_DUE', 'TRIALING', 'UNPAID']
          }
        }
      })

      let subscription
      if (existingSubscription) {
        // Atualizar subscrição existente (upgrade/downgrade)
        subscription = await prisma.subscription.update({
          where: { id: existingSubscription.id },
          data: {
            planId,
            status: 'ACTIVE',
            billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + (billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000),
            stripeSubscriptionId: session.subscription,
            stripeCustomerId: session.customer
          }
        })

        // Marcar pagamentos pendentes como completados
        await prisma.subscriptionPayment.updateMany({
          where: {
            subscriptionId: existingSubscription.id,
            status: 'PENDING'
          },
          data: {
            status: 'COMPLETED',
            stripePaymentIntentId: session.payment_intent,
            paidAt: new Date()
          }
        })

        console.log(`✅ Subscription updated for user ${userId} via checkout session - Plan: ${planId}`)
      } else {
        // Criar nova subscrição
        subscription = await prisma.subscription.create({
          data: {
            userId,
            planId,
            status: 'ACTIVE',
            billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + (billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000),
            stripeSubscriptionId: session.subscription,
            stripeCustomerId: session.customer
          }
        })
        console.log(`✅ Subscription created for user ${userId} via checkout session - Plan: ${planId}`)
      }

      // Criar registro de pagamento
      await prisma.subscriptionPayment.create({
        data: {
          subscriptionId: subscription.id,
          amount: session.amount_total / 100,
          currency: session.currency.toUpperCase(),
          status: 'COMPLETED',
          stripePaymentIntentId: session.payment_intent,
          periodStart: new Date(),
          periodEnd: new Date(Date.now() + (billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000),
          paidAt: new Date()
        }
      })
    }

  } catch (error) {
    console.error('❌ Error handling checkout.session.completed:', error)
  }
}
