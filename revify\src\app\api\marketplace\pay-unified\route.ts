import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const { orderIds, paymentMethod = 'card' } = await request.json()

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { message: 'IDs das encomendas são obrigatórios' },
        { status: 400 }
      )
    }

    if (!['card', 'multibanco', 'klarna'].includes(paymentMethod)) {
      return NextResponse.json(
        { message: 'Método de pagamento inválido. Use "card", "multibanco" ou "klarna"' },
        { status: 400 }
      )
    }

    // Buscar encomendas
    const orders = await prisma.order.findMany({
      where: {
        id: { in: orderIds },
        customerId: session.user.id,
        status: { in: ['PENDING', 'PENDING_PAYMENT'] }
      },
      include: {
        marketplaceOrderItems: {
          include: {
            product: true
          }
        }
      }
    })

    if (orders.length === 0) {
      return NextResponse.json(
        { message: 'Nenhuma encomenda válida encontrada' },
        { status: 404 }
      )
    }

    // Calcular total
    const totalAmount = orders.reduce((sum, order) => sum + Number(order.total), 0)
    const description = `Marketplace - ${orders.length} encomenda(s)`

    // Usar a API unificada de pagamentos V2
    const paymentIntentResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/create-payment-intent-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: totalAmount,
        currency: 'EUR',
        paymentMethod,
        metadata: {
          type: 'marketplace',
          orderIds: orderIds.join(','),
          description,
          customerEmail: session.user.email,
          customerName: session.user.name,
          lojistId: session.user.id,
          lojistName: session.user.name,
          platformFee: totalAmount * 0.05,
          shopAmount: totalAmount * 0.95
        },
        customerEmail: session.user.email,
        customerName: session.user.name,
        returnUrl: paymentMethod === 'card' ? undefined : `${process.env.NEXTAUTH_URL}/marketplace/success`,
        confirmationMethod: 'automatic'
      })
    })

    const paymentIntentData = await paymentIntentResponse.json()

    if (!paymentIntentResponse.ok) {
      console.error('Erro ao criar PaymentIntent para marketplace:', paymentIntentData)
      return NextResponse.json(
        { 
          message: paymentIntentData.error || 'Erro ao criar pagamento',
          details: paymentIntentData.details 
        },
        { status: 400 }
      )
    }

    // Atualizar encomendas com método de pagamento
    await prisma.order.updateMany({
      where: { id: { in: orderIds } },
      data: { 
        paymentMethod: paymentMethod.toUpperCase(),
        status: paymentMethod === 'multibanco' ? 'PENDING_PAYMENT' : 'PENDING'
      }
    })

    // Se for Multibanco e tiver referência
    if (paymentMethod === 'multibanco' && paymentIntentData.multibanco) {
      return NextResponse.json({
        success: true,
        type: 'multibanco_reference',
        multibanco: paymentIntentData.multibanco,
        paymentIntent: paymentIntentData.paymentIntent,
        orders: orders.map(order => ({
          id: order.id,
          total: Number(order.total)
        })),
        redirectUrl: `/marketplace/success?multibanco=true&entity=${paymentIntentData.multibanco.entity}&reference=${paymentIntentData.multibanco.reference}&amount=${totalAmount}&orders=${orderIds.join(',')}`
      })
    }

    // Para cartão ou Klarna, retornar dados do PaymentIntent
    return NextResponse.json({
      success: true,
      type: 'payment_intent',
      paymentIntent: paymentIntentData.paymentIntent,
      clientSecret: paymentIntentData.paymentIntent.client_secret,
      orders: orders.map(order => ({
        id: order.id,
        total: Number(order.total)
      }))
    })

  } catch (error) {
    console.error('Erro ao processar pagamento unificado do marketplace:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}