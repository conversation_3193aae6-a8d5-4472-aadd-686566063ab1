'use client'

import { useState, useEffect } from 'react'
import SimplePaymentForm from '@/components/payments/SimplePaymentForm'
import MultibancoReference from '@/components/payments/MultibancoReference'
import { 
  X, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface DirectPaymentModalProps {
  title: string
  description: string
  amount: number
  currency?: string
  paymentMethod: 'card' | 'multibanco' | 'klarna'
  onSuccess?: () => void
  onCancel?: () => void
  isOpen: boolean
  paymentEndpoint: string
  paymentData?: any
}

export default function DirectPaymentModal({
  title,
  description,
  amount,
  currency = 'EUR',
  paymentMethod,
  onSuccess,
  onCancel,
  isOpen,
  paymentEndpoint,
  paymentData = {}
}: DirectPaymentModalProps) {
  const [paymentResult, setPaymentResult] = useState<any>(null)
  const [showMultibancoRef, setShowMultibancoRef] = useState(false)
  const [clientSecret, setClientSecret] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [initialized, setInitialized] = useState(false)

  // Inicializar pagamento automaticamente quando modal abre
  useEffect(() => {
    if (isOpen && !initialized) {
      initializePayment()
      setInitialized(true)
    }
    
    if (!isOpen) {
      // Reset quando modal fecha
      setInitialized(false)
      setClientSecret('')
      setPaymentResult(null)
      setShowMultibancoRef(false)
      setError('')
    }
  }, [isOpen])

  const initializePayment = async () => {
    setLoading(true)
    setError('')

    try {
      console.log('🔄 Inicializando pagamento direto:', {
        method: paymentMethod,
        amount,
        endpoint: paymentEndpoint
      })

      const response = await fetch(paymentEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...paymentData,
          paymentMethod
        })
      })

      const data = await response.json()
      console.log('📥 Resposta do pagamento:', data)

      if (response.ok) {
        if (data.type === 'multibanco_reference' && data.multibanco) {
          setPaymentResult(data)
          setShowMultibancoRef(true)
        } else if (data.type === 'payment_intent' && data.clientSecret) {
          setClientSecret(data.clientSecret)
        } else if (data.success && data.paymentIntent?.client_secret) {
          // Formato alternativo de resposta
          setClientSecret(data.paymentIntent.client_secret)
        } else {
          setError('Resposta de pagamento inválida')
          console.error('Resposta inválida:', data)
        }
      } else {
        setError(data.message || 'Erro ao inicializar pagamento')
        console.error('Erro na resposta:', data)
      }
    } catch (error) {
      console.error('Erro ao inicializar pagamento:', error)
      setError('Erro ao conectar com o servidor')
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentSuccess = (result: any) => {
    console.log('✅ Pagamento bem-sucedido:', result)
    setPaymentResult(result)

    // Fechar modal e chamar callback de sucesso
    setTimeout(() => {
      onSuccess?.()
    }, 1500)
  }

  const handlePaymentError = (error: string) => {
    console.error('❌ Erro no pagamento:', error)
    setError(error)
  }

  if (!isOpen) return null

  // Se mostrar referência Multibanco
  if (showMultibancoRef && paymentResult?.multibanco) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Referência Multibanco Gerada
              </h2>
              <button
                onClick={onCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="font-medium text-blue-900">{title}</div>
                <div className="text-sm text-blue-700 mt-1">{description}</div>
              </div>
            </div>

            <MultibancoReference
              entity={paymentResult.multibanco.entity}
              reference={paymentResult.multibanco.reference}
              amount={amount}
              currency={currency}
              expiresAt={paymentResult.multibanco.expires_at}
              description={description}
              onCopy={() => console.log('Dados copiados')}
            />

            <div className="mt-6 flex justify-center">
              <button
                onClick={() => onSuccess?.()}
                className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700"
              >
                Continuar
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Se pagamento foi bem-sucedido
  if (paymentResult && paymentResult.type === 'payment_succeeded') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-md w-full p-6 text-center">
          <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Pagamento Concluído
          </h2>
          <p className="text-gray-600 mb-4">
            O seu pagamento foi processado com sucesso
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="text-sm text-gray-600 mb-1">Descrição:</div>
            <div className="font-bold text-gray-900">{description}</div>
            <div className="text-sm text-gray-600 mt-2">Valor pago:</div>
            <div className="text-xl font-bold text-green-600">
              {new Intl.NumberFormat('pt-PT', {
                style: 'currency',
                currency: currency
              }).format(amount)}
            </div>
          </div>

          <button
            onClick={() => onSuccess?.()}
            className="w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700"
          >
            Continuar
          </button>
        </div>
      </div>
    )
  }

  // Modal principal
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {title}
              </h2>
              <p className="text-gray-600">
                {description}
              </p>
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Resumo do pagamento */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-lg font-medium text-gray-900">Total a pagar:</span>
              <span className="text-2xl font-bold text-gray-900">
                {new Intl.NumberFormat('pt-PT', {
                  style: 'currency',
                  currency: currency
                }).format(amount)}
              </span>
            </div>
            <div className="text-sm text-gray-600 mt-2">
              Método: {paymentMethod === 'card' ? 'Cartão de Crédito' : 
                       paymentMethod === 'multibanco' ? 'Multibanco' : 'Klarna'}
            </div>
          </div>

          {/* Loading */}
          {loading && (
            <div className="flex items-center justify-center p-8">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
                <p className="text-gray-600">A inicializar pagamento...</p>
              </div>
            </div>
          )}

          {/* Error */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-center">
                <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Erro no Pagamento</h3>
                  <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
              </div>
              <button
                onClick={() => {
                  setError('')
                  setInitialized(false)
                  initializePayment()
                }}
                className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Tentar Novamente
              </button>
            </div>
          )}

          {/* Formulário de pagamento com cartão/Klarna */}
          {clientSecret && !loading && !error && (
            <div className="mt-6">
              <SimplePaymentForm
                clientSecret={clientSecret}
                amount={amount}
                currency={currency}
                description={description}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={onCancel}
                returnUrl={`${window.location.origin}/payment/success`}
                appearance={{
                  theme: 'stripe' as const,
                  variables: {
                    colorPrimary: '#3b82f6',
                    colorBackground: '#ffffff',
                    colorText: '#1f2937',
                    colorDanger: '#ef4444',
                    fontFamily: 'system-ui, sans-serif',
                    spacingUnit: '4px',
                    borderRadius: '8px'
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}