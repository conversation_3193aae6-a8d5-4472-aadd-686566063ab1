import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { subscriptionId, paymentMethod = 'card' } = await request.json()

    if (!subscriptionId) {
      return NextResponse.json(
        { message: 'ID da subscrição é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar subscrição
    const subscription = await prisma.subscription.findFirst({
      where: {
        id: subscriptionId,
        userId: session.user.id
      },
      include: {
        plan: true,
        payments: {
          where: { status: 'PENDING' },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Subscrição não encontrada' },
        { status: 404 }
      )
    }

    const stripe = await createStripeInstance()

    // Calcular valor
    const amount = subscription.billingCycle === 'MONTHLY' 
      ? subscription.plan.monthlyPrice 
      : subscription.plan.yearlyPrice

    // Criar PaymentIntent usando a nova API
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/create-payment-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount,
        currency: 'EUR',
        paymentMethod,
        metadata: {
          type: 'subscription',
          subscriptionId: subscription.id,
          planId: subscription.planId,
          planName: subscription.plan.name,
          billingCycle: subscription.billingCycle,
          userId: session.user.id,
          customerEmail: session.user.email,
          customerName: session.user.name,
          paymentId: subscription.payments[0]?.id
        },
        customerEmail: session.user.email,
        customerName: session.user.name,
        returnUrl: `${process.env.NEXTAUTH_URL}/lojista/subscricao/success`
      })
    })

    const paymentData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { message: paymentData.error || 'Erro ao criar pagamento' },
        { status: 400 }
      )
    }

    // Se for Multibanco, retornar dados da referência
    if (paymentMethod === 'multibanco' && paymentData.multibanco) {
      // Atualizar pagamento existente ou criar novo com dados Multibanco
      if (subscription.payments[0]) {
        await prisma.subscriptionPayment.update({
          where: { id: subscription.payments[0].id },
          data: {
            stripePaymentIntentId: paymentData.paymentIntent.id,
            multibancoEntity: paymentData.multibanco.entity,
            multibancoReference: paymentData.multibanco.reference,
            status: 'PENDING'
          }
        })
      } else {
        // Criar novo pagamento
        const periodStart = new Date()
        const periodEnd = new Date()
        if (subscription.billingCycle === 'MONTHLY') {
          periodEnd.setMonth(periodEnd.getMonth() + 1)
        } else {
          periodEnd.setFullYear(periodEnd.getFullYear() + 1)
        }

        await prisma.subscriptionPayment.create({
          data: {
            subscriptionId: subscription.id,
            amount,
            currency: 'EUR',
            status: 'PENDING',
            stripePaymentIntentId: paymentData.paymentIntent.id,
            multibancoEntity: paymentData.multibanco.entity,
            multibancoReference: paymentData.multibanco.reference,
            periodStart,
            periodEnd
          }
        })
      }

      return NextResponse.json({
        success: true,
        paymentIntent: paymentData.paymentIntent,
        multibanco: paymentData.multibanco,
        type: 'multibanco_reference'
      })
    }

    // Para cartão e Klarna, retornar dados do PaymentIntent
    return NextResponse.json({
      success: true,
      paymentIntent: paymentData.paymentIntent,
      redirectUrl: paymentData.redirectUrl,
      type: paymentMethod === 'klarna' ? 'klarna_redirect' : 'card_payment'
    })

  } catch (error) {
    console.error('❌ Erro no pagamento moderno de subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Endpoint para confirmar pagamento
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { paymentIntentId } = await request.json()

    if (!paymentIntentId) {
      return NextResponse.json(
        { message: 'Payment Intent ID é obrigatório' },
        { status: 400 }
      )
    }

    // Confirmar pagamento usando a nova API
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/confirm-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        paymentIntentId,
        returnUrl: `${process.env.NEXTAUTH_URL}/lojista/subscricao/success`
      })
    })

    const confirmData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { message: confirmData.error || 'Erro ao confirmar pagamento' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      paymentIntent: confirmData.paymentIntent,
      requiresAction: confirmData.requiresAction,
      redirectUrl: confirmData.redirectUrl,
      multibanco: confirmData.multibanco
    })

  } catch (error) {
    console.error('❌ Erro ao confirmar pagamento de subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Endpoint para verificar status do pagamento
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const paymentIntentId = searchParams.get('payment_intent_id')

    if (!paymentIntentId) {
      return NextResponse.json(
        { message: 'Payment Intent ID é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar status usando a nova API
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/confirm-payment?payment_intent_id=${paymentIntentId}`)
    const statusData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { message: statusData.error || 'Erro ao verificar status' },
        { status: 400 }
      )
    }

    // Buscar dados locais da subscrição
    const subscriptionPayment = await prisma.subscriptionPayment.findFirst({
      where: {
        stripePaymentIntentId: paymentIntentId
      },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      paymentIntent: statusData,
      subscriptionPayment,
      localData: !!subscriptionPayment
    })

  } catch (error) {
    console.error('❌ Erro ao verificar status do pagamento:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
