import { NextRequest, NextResponse } from 'next/server'
import { createStripeInstance } from '@/lib/stripe'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      amount, 
      currency = 'eur', 
      paymentMethod, 
      metadata = {},
      customerEmail,
      customerName,
      returnUrl,
      confirmationMethod = 'automatic'
    } = body

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valor inválido' },
        { status: 400 }
      )
    }

    if (!paymentMethod || !['card', 'multibanco', 'klarna'].includes(paymentMethod)) {
      return NextResponse.json(
        { error: 'Método de pagamento inválido' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance()

    // Configurar Payment Intent baseado no método
    const paymentIntentData: any = {
      amount: Math.round(amount * 100), // Converter para centavos
      currency: currency.toLowerCase(),
      metadata: {
        ...metadata,
        paymentMethod,
        customerEmail: customerEmail || '',
        customerName: customerName || ''
      }
    }

    // Configurar método de pagamento específico - SEM automatic_payment_methods
    switch (paymentMethod) {
      case 'card':
        paymentIntentData.payment_method_types = ['card']
        // Não definir confirmation_method para cartões - usar padrão do Stripe
        break

      case 'multibanco':
        paymentIntentData.payment_method_types = ['multibanco']
        // Para Multibanco, usar confirmation_method = 'automatic' quando confirmamos imediatamente
        paymentIntentData.confirmation_method = returnUrl ? 'automatic' : 'manual'
        break

      case 'klarna':
        paymentIntentData.payment_method_types = ['klarna']
        // Para Klarna, usar confirmation_method = 'automatic' quando confirmamos imediatamente
        paymentIntentData.confirmation_method = returnUrl ? 'automatic' : 'manual'
        
        // Klarna requer informações adicionais
        if (customerEmail) {
          paymentIntentData.receipt_email = customerEmail
        }
        
        // Configurações específicas do Klarna
        paymentIntentData.payment_method_options = {
          klarna: {
            preferred_locale: 'pt-PT',
            capture_method: 'automatic'
          }
        }
        break
    }

    // Para Multibanco e Klarna, adicionar return_url e confirm se necessário
    if (returnUrl && (paymentMethod === 'multibanco' || paymentMethod === 'klarna')) {
      paymentIntentData.return_url = returnUrl
      // Se temos return_url, precisamos confirmar imediatamente
      paymentIntentData.confirm = true
      paymentIntentData.payment_method = {
        type: paymentMethod
      }
    }

    // Remover campos undefined antes de enviar para o Stripe
    const cleanPaymentIntentData = Object.fromEntries(
      Object.entries(paymentIntentData).filter(([_, value]) => value !== undefined)
    )

    console.log('🔄 Criando PaymentIntent:', {
      amount: cleanPaymentIntentData.amount,
      currency: cleanPaymentIntentData.currency,
      paymentMethod,
      hasReturnUrl: !!cleanPaymentIntentData.return_url,
      metadata: cleanPaymentIntentData.metadata
    })

    // Criar PaymentIntent
    const paymentIntent = await stripe.paymentIntents.create(cleanPaymentIntentData)

    // Registar no histórico como PENDING
    await recordPaymentHistory({
      stripePaymentIntentId: paymentIntent.id,
      amount: amount,
      currency: currency.toUpperCase(),
      status: 'PENDING',
      paymentType: metadata.type === 'subscription' ? 'SUBSCRIPTION' : 
                   metadata.type === 'marketplace' ? 'MARKETPLACE' : 
                   metadata.type === 'repair' ? 'REPAIR' : 'REPAIR',
      paymentMethodType: paymentMethod,
      description: metadata.description || `Pagamento via ${paymentMethod}`,
      customerEmail,
      customerName,
      lojistId: metadata.lojistId,
      lojistName: metadata.lojistName,
      subscriptionId: metadata.subscriptionId,
      orderId: metadata.orderId,
      repairId: metadata.repairId,
      metadata
    })

    // Resposta baseada no método de pagamento
    const response: any = {
      success: true,
      paymentIntent: {
        id: paymentIntent.id,
        client_secret: paymentIntent.client_secret,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency
      },
      paymentMethod
    }

    // Extrair dados específicos do método de pagamento se já confirmado
    if (paymentMethod === 'multibanco' && paymentIntent.next_action?.multibanco_display_details) {
      const multibancoDetails = paymentIntent.next_action.multibanco_display_details
      response.multibanco = {
        entity: multibancoDetails.entity,
        reference: multibancoDetails.reference,
        amount: multibancoDetails.amount_remaining,
        expires_at: multibancoDetails.expires_at
      }
    }

    // Se Klarna requer redirecionamento
    if (paymentMethod === 'klarna' && paymentIntent.next_action?.redirect_to_url) {
      response.redirectUrl = paymentIntent.next_action.redirect_to_url.url
    }

    console.log('✅ PaymentIntent criado com sucesso:', paymentIntent.id)
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Erro ao criar PaymentIntent:', error)
    
    // Erro específico do Stripe
    if (error.type === 'StripeCardError') {
      return NextResponse.json(
        { error: 'Erro no cartão', details: error.message },
        { status: 400 }
      )
    }

    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json(
        { error: 'Pedido inválido', details: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Endpoint para verificar disponibilidade do Klarna
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const country = searchParams.get('country') || 'PT'
    const currency = searchParams.get('currency') || 'EUR'
    const amount = parseFloat(searchParams.get('amount') || '0')

    // Verificar se Klarna está disponível para o país/moeda
    const klarnaAvailability = checkKlarnaAvailability(country, currency, amount)

    return NextResponse.json({
      klarna: klarnaAvailability,
      multibanco: country === 'PT' && currency === 'EUR',
      card: true // Cartão sempre disponível
    })

  } catch (error) {
    console.error('Erro ao verificar disponibilidade:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

/**
 * Verificar disponibilidade do Klarna baseado no país, moeda e valor
 */
function checkKlarnaAvailability(country: string, currency: string, amount: number): {
  available: boolean
  methods: string[]
  minAmount?: number
  maxAmount?: number
} {
  // Países suportados pelo Klarna
  const supportedCountries = ['AT', 'BE', 'DK', 'FI', 'FR', 'DE', 'IT', 'NL', 'NO', 'ES', 'SE', 'GB', 'US', 'AU']
  
  // Moedas suportadas
  const supportedCurrencies = ['EUR', 'USD', 'GBP', 'SEK', 'NOK', 'DKK']

  if (!supportedCountries.includes(country.toUpperCase()) || 
      !supportedCurrencies.includes(currency.toUpperCase())) {
    return {
      available: false,
      methods: []
    }
  }

  // Limites de valor (variam por país)
  const limits = {
    EUR: { min: 1, max: 10000 },
    USD: { min: 1, max: 10000 },
    GBP: { min: 1, max: 8000 },
    SEK: { min: 1, max: 100000 },
    NOK: { min: 1, max: 100000 },
    DKK: { min: 1, max: 75000 }
  }

  const currencyLimits = limits[currency.toUpperCase()] || limits.EUR
  const isAmountValid = amount >= currencyLimits.min && amount <= currencyLimits.max

  if (!isAmountValid) {
    return {
      available: false,
      methods: [],
      minAmount: currencyLimits.min,
      maxAmount: currencyLimits.max
    }
  }

  // Métodos disponíveis baseados no país
  let availableMethods = ['pay_now']
  
  // Pay Later disponível na maioria dos países
  if (['AT', 'BE', 'DK', 'FI', 'FR', 'DE', 'IT', 'NL', 'NO', 'ES', 'SE', 'GB'].includes(country.toUpperCase())) {
    availableMethods.push('pay_later')
  }

  // Pay in 3/4 disponível em alguns países
  if (['DE', 'AT', 'NL', 'BE', 'IT', 'ES', 'FR', 'GB'].includes(country.toUpperCase())) {
    availableMethods.push('pay_in_installments')
  }

  return {
    available: true,
    methods: availableMethods,
    minAmount: currencyLimits.min,
    maxAmount: currencyLimits.max
  }
}
