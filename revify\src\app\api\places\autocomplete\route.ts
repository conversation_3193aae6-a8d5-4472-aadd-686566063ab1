import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Função para obter a API key do Google Maps das configurações
async function getGoogleMapsApiKey(): Promise<string | null> {
  try {
    const setting = await prisma.systemSettings.findUnique({
      where: { key: 'googleMapsApiKey' }
    })
    return setting?.value || null
  } catch (error) {
    console.error('Erro ao buscar API key do Google Maps:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const input = searchParams.get('input')
    const types = searchParams.get('types') || 'address'
    const componentRestrictions = searchParams.get('components') || 'country:pt'

    if (!input || input.length < 2) {
      return NextResponse.json({
        predictions: []
      })
    }

    const apiKey = await getGoogleMapsApiKey()
    if (!apiKey) {
      return NextResponse.json(
        { message: 'Google Maps API key não configurada' },
        { status: 500 }
      )
    }

    // Construir URL da Places API
    const encodedInput = encodeURIComponent(input)
    const url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodedInput}&types=${types}&components=${componentRestrictions}&key=${apiKey}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'OK') {
      // Processar e formatar as sugestões
      const predictions = data.predictions.map((prediction: any) => ({
        place_id: prediction.place_id,
        description: prediction.description,
        main_text: prediction.structured_formatting?.main_text || '',
        secondary_text: prediction.structured_formatting?.secondary_text || '',
        types: prediction.types
      }))

      return NextResponse.json({
        predictions: predictions
      })
    } else if (data.status === 'ZERO_RESULTS') {
      return NextResponse.json({
        predictions: []
      })
    } else {
      console.error('Places API error:', data.status, data.error_message)
      return NextResponse.json(
        { message: `Erro na Places API: ${data.status}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Erro na API de autocomplete:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
