import { NextRequest, NextResponse } from 'next/server'
import { createStripeInstance } from '@/lib/stripe'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      amount, 
      currency = 'eur', 
      paymentMethod, 
      metadata = {},
      customerEmail,
      customerName,
      returnUrl
    } = body

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valor inválido' },
        { status: 400 }
      )
    }

    if (!paymentMethod || !['card', 'multibanco', 'klarna'].includes(paymentMethod)) {
      return NextResponse.json(
        { error: 'Método de pagamento inválido' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance()

    console.log('🔄 Criando PaymentIntent V2:', {
      amount,
      currency,
      paymentMethod,
      hasReturnUrl: !!returnUrl
    })

    let paymentIntent

    if (paymentMethod === 'card') {
      // Para cartão, criar PaymentIntent simples
      paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency: currency.toLowerCase(),
        payment_method_types: ['card'],
        metadata: {
          ...metadata,
          paymentMethod,
          customerEmail: customerEmail || '',
          customerName: customerName || ''
        }
      })

    } else if (paymentMethod === 'multibanco') {
      // Para Multibanco, criar e confirmar imediatamente
      paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency: currency.toLowerCase(),
        payment_method_types: ['multibanco'],
        confirmation_method: 'automatic',
        confirm: true,
        payment_method: {
          type: 'multibanco'
        },
        return_url: returnUrl || `${process.env.NEXTAUTH_URL}/payment/success`,
        metadata: {
          ...metadata,
          paymentMethod,
          customerEmail: customerEmail || '',
          customerName: customerName || ''
        }
      })

    } else if (paymentMethod === 'klarna') {
      // Para Klarna, criar e confirmar imediatamente
      paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency: currency.toLowerCase(),
        payment_method_types: ['klarna'],
        confirmation_method: 'automatic',
        confirm: true,
        payment_method: {
          type: 'klarna'
        },
        return_url: returnUrl || `${process.env.NEXTAUTH_URL}/payment/success`,
        receipt_email: customerEmail,
        payment_method_options: {
          klarna: {
            preferred_locale: 'pt-PT',
            capture_method: 'automatic'
          }
        },
        metadata: {
          ...metadata,
          paymentMethod,
          customerEmail: customerEmail || '',
          customerName: customerName || ''
        }
      })
    }

    // Registar no histórico como PENDING
    await recordPaymentHistory({
      stripePaymentIntentId: paymentIntent.id,
      amount: amount,
      currency: currency.toUpperCase(),
      status: 'PENDING',
      paymentType: metadata.type === 'subscription' ? 'SUBSCRIPTION' : 
                   metadata.type === 'marketplace' ? 'MARKETPLACE' : 
                   metadata.type === 'repair' ? 'REPAIR' : 'REPAIR',
      paymentMethodType: paymentMethod,
      description: metadata.description || `Pagamento via ${paymentMethod}`,
      customerEmail,
      customerName,
      lojistId: metadata.lojistId,
      lojistName: metadata.lojistName,
      subscriptionId: metadata.subscriptionId,
      orderId: metadata.orderId,
      repairId: metadata.repairId,
      metadata
    })

    // Resposta baseada no método de pagamento
    const response: any = {
      success: true,
      paymentIntent: {
        id: paymentIntent.id,
        client_secret: paymentIntent.client_secret,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency
      },
      paymentMethod
    }

    // Extrair dados específicos do método de pagamento
    if (paymentMethod === 'multibanco' && paymentIntent.next_action?.multibanco_display_details) {
      const multibancoDetails = paymentIntent.next_action.multibanco_display_details
      response.multibanco = {
        entity: multibancoDetails.entity,
        reference: multibancoDetails.reference,
        amount: multibancoDetails.amount_remaining,
        expires_at: multibancoDetails.expires_at
      }
      response.type = 'multibanco_reference'
    } else if (paymentMethod === 'card') {
      response.type = 'payment_intent'
    }

    // Se Klarna requer redirecionamento
    if (paymentMethod === 'klarna' && paymentIntent.next_action?.redirect_to_url) {
      response.redirectUrl = paymentIntent.next_action.redirect_to_url.url
    }

    console.log('✅ PaymentIntent V2 criado com sucesso:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      hasMultibanco: !!response.multibanco,
      hasRedirectUrl: !!response.redirectUrl
    })

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Erro ao criar PaymentIntent V2:', error)
    
    // Erro específico do Stripe
    if (error.type === 'StripeCardError') {
      return NextResponse.json(
        { error: 'Erro no cartão', details: error.message },
        { status: 400 }
      )
    }

    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json(
        { error: 'Pedido inválido', details: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}