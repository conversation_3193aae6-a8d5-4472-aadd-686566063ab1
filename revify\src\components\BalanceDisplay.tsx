'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Wallet, Info, AlertCircle } from 'lucide-react'

interface BalanceData {
  available: number
  pending: number
  totalEarned: number
}

interface BalanceDisplayProps {
  showWarning?: boolean
  className?: string
}

export default function BalanceDisplay({ showWarning = true, className = '' }: BalanceDisplayProps) {
  const { data: session } = useSession()
  const [balance, setBalance] = useState<BalanceData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false)

  useEffect(() => {
    if (session) {
      fetchBalance()
      checkSubscriptionStatus()
    }
  }, [session])

  const fetchBalance = async () => {
    try {
      const response = await fetch('/api/referral/dashboard')
      if (response.ok) {
        const data = await response.json()
        setBalance(data.balance)
      }
    } catch (error) {
      console.error('Erro ao buscar saldo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/lojista/subscription/status')
      if (response.ok) {
        const data = await response.json()
        setHasActiveSubscription(data.hasActiveSubscription)
      }
    } catch (error) {
      console.error('Erro ao verificar subscrição:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (!balance) {
    return null
  }

  const canUseBalance = session?.user.role === 'REPAIR_SHOP' ? hasActiveSubscription : true

  return (
    <div className={`bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Wallet className="w-5 h-5 text-green-600" />
          <h3 className="font-semibold text-gray-900">Saldo de Referrals</h3>
        </div>
        <Info className="w-4 h-4 text-gray-400" />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Disponível:</span>
          <span className="font-bold text-green-600 text-lg">
            €{balance.available.toFixed(2)}
          </span>
        </div>

        {balance.pending > 0 && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Pendente:</span>
            <span className="font-medium text-amber-600">
              €{balance.pending.toFixed(2)}
            </span>
          </div>
        )}

        <div className="flex justify-between items-center pt-2 border-t border-green-200">
          <span className="text-sm text-gray-600">Total Ganho:</span>
          <span className="font-medium text-gray-900">
            €{balance.totalEarned.toFixed(2)}
          </span>
        </div>
      </div>

      {/* Avisos específicos por tipo de usuário */}
      {showWarning && session?.user.role !== 'EMPLOYEE' && (
        <div className="mt-4 space-y-2">
          {session?.user.role === 'REPAIR_SHOP' && (
            <div className={`flex items-start space-x-2 p-3 rounded-lg ${
              hasActiveSubscription 
                ? 'bg-blue-50 border border-blue-200' 
                : 'bg-amber-50 border border-amber-200'
            }`}>
              <AlertCircle className={`w-4 h-4 mt-0.5 ${
                hasActiveSubscription ? 'text-blue-500' : 'text-amber-500'
              }`} />
              <div className="text-xs">
                {hasActiveSubscription ? (
                  <p className="text-blue-700">
                    <strong>Saldo disponível para pagamento de subscrições.</strong>
                    <br />
                    Pode usar este saldo para pagar ou abater no valor da sua subscrição.
                  </p>
                ) : (
                  <p className="text-amber-700">
                    <strong>Subscrição necessária para usar o saldo.</strong>
                    <br />
                    O saldo só pode ser utilizado com uma subscrição ativa.
                  </p>
                )}
              </div>
            </div>
          )}

          {session?.user.role === 'CUSTOMER' && balance.available > 0 && (
            <div className="flex items-start space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <Info className="w-4 h-4 mt-0.5 text-green-500" />
              <div className="text-xs text-green-700">
                <p>
                  <strong>Saldo disponível para compras e reparações.</strong>
                  <br />
                  Use este saldo no marketplace ou para pagar reparações.
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
