import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { reconcileAllPayments, reconcileSpecificPayment, fixExistingPaymentTypes } from '@/lib/payments/reconciliation'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || (session.user.role !== 'ADMIN' && !session.user.isSuperAdmin)) {
      console.log('❌ Acesso negado à reconciliação - Session:', {
        exists: !!session,
        role: session?.user?.role,
        isSuperAdmin: session?.user?.isSuperAdmin
      })
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { type, paymentId } = body

    console.log('🔄 Iniciando reconciliação:', { type, paymentId })

    if (type === 'specific' && paymentId) {
      // Reconciliar pagamento específico
      const result = await reconcileSpecificPayment(paymentId)
      
      return NextResponse.json({
        success: result.success,
        message: result.message,
        data: result.updatedData
      })
    }

    if (type === 'all' || !type) {
      // Primeiro, corrigir tipos de pagamentos existentes
      console.log('🔧 Corrigindo tipos de pagamentos existentes...')
      const fixResult = await fixExistingPaymentTypes()
      console.log('✅ Correção de tipos concluída:', fixResult)

      // Depois, fazer reconciliação completa
      const result = await reconcileAllPayments()

      return NextResponse.json({
        success: result.success,
        message: result.summary,
        fixedTypes: fixResult.correctedCount,
        details: {
          reconciledCount: result.reconciledCount,
          newPaymentsFound: result.newPaymentsFound,
          updatedPayments: result.updatedPayments,
          errorCount: result.errorCount,
          errors: result.errors.slice(0, 10) // Limitar erros mostrados
        }
      })
    }

    return NextResponse.json(
      { message: 'Tipo de reconciliação inválido' },
      { status: 400 }
    )

  } catch (error) {
    console.error('❌ Erro na API de reconciliação:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'Erro interno do servidor',
        error: error.message 
      },
      { status: 500 }
    )
  }
}

// Endpoint para verificar status da reconciliação
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || (session.user.role !== 'ADMIN' && !session.user.isSuperAdmin)) {
      console.log('❌ Acesso negado à reconciliação GET - Session:', {
        exists: !!session,
        role: session?.user?.role,
        isSuperAdmin: session?.user?.isSuperAdmin
      })
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'stats') {
      // Estatísticas de reconciliação
      const { prisma } = await import('@/lib/prisma')
      
      const [
        totalPayments,
        reconciledPayments,
        pendingReconciliation,
        recentReconciliation
      ] = await Promise.all([
        prisma.paymentHistory.count(),
        prisma.paymentHistory.count({
          where: { reconciledAt: { not: null } }
        }),
        prisma.paymentHistory.count({
          where: {
            OR: [
              { status: 'PENDING' },
              { status: 'PROCESSING' },
              { reconciledAt: null }
            ]
          }
        }),
        prisma.paymentHistory.count({
          where: {
            reconciledAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
            }
          }
        })
      ])

      return NextResponse.json({
        totalPayments,
        reconciledPayments,
        pendingReconciliation,
        recentReconciliation,
        reconciliationRate: totalPayments > 0 ? (reconciledPayments / totalPayments * 100).toFixed(1) : '0'
      })
    }

    if (action === 'pending') {
      // Lista de pagamentos pendentes de reconciliação
      const { prisma } = await import('@/lib/prisma')
      
      const pendingPayments = await prisma.paymentHistory.findMany({
        where: {
          OR: [
            { status: 'PENDING' },
            { status: 'PROCESSING' },
            { reconciledAt: null },
            {
              AND: [
                { reconciledAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } },
                { status: { in: ['PENDING', 'PROCESSING'] } }
              ]
            }
          ]
        },
        select: {
          id: true,
          stripePaymentIntentId: true,
          stripeInvoiceId: true,
          amount: true,
          currency: true,
          status: true,
          paymentType: true,
          customerEmail: true,
          createdAt: true,
          reconciledAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: 50
      })

      return NextResponse.json({
        pendingPayments,
        count: pendingPayments.length
      })
    }

    return NextResponse.json(
      { message: 'Ação não reconhecida' },
      { status: 400 }
    )

  } catch (error) {
    console.error('❌ Erro ao buscar dados de reconciliação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
