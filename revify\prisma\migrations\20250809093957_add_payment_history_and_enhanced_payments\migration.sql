-- Create<PERSON><PERSON>
CREATE TYPE "public"."PaymentHistoryStatus" AS ENUM ('PENDING', 'PROCESSING', 'SUCCEEDED', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED');

-- C<PERSON><PERSON>num
CREATE TYPE "public"."PaymentType" AS ENUM ('SUBSCRIPTION', 'MARKETPLACE', 'REPAIR', 'ADDON', 'UPGRADE');

-- AlterEnum
ALTER TYPE "public"."UserRole" ADD VALUE 'EMPLOYEE';

-- AlterTable
ALTER TABLE "public"."payments" ADD COLUMN     "failureReason" TEXT,
ADD COLUMN     "paymentMethodDetails" JSONB,
ADD COLUMN     "paymentMethodType" TEXT,
ADD COLUMN     "stripeCustomerId" TEXT,
ADD COLUMN     "stripeInvoiceId" TEXT;

-- AlterTable
ALTER TABLE "public"."repairs" ADD COLUMN     "assignedEmployeeId" TEXT;

-- AlterTable
ALTER TABLE "public"."subscription_payments" ADD COLUMN     "failureReason" TEXT,
ADD COLUMN     "paymentMethodDetails" JSONB,
ADD COLUMN     "paymentMethodType" TEXT,
ADD COLUMN     "stripeCustomerId" TEXT;

-- CreateTable
CREATE TABLE "public"."employees" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "repairShopId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "position" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "permissions" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "employees_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."repair_status_history" (
    "id" TEXT NOT NULL,
    "repairId" TEXT NOT NULL,
    "status" "public"."RepairStatus" NOT NULL,
    "notes" TEXT,
    "updatedBy" TEXT,
    "updatedByType" TEXT,
    "employeeId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "repair_status_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."payment_history" (
    "id" TEXT NOT NULL,
    "stripePaymentIntentId" TEXT,
    "stripeInvoiceId" TEXT,
    "stripeCustomerId" TEXT,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'EUR',
    "status" "public"."PaymentHistoryStatus" NOT NULL,
    "paymentType" "public"."PaymentType" NOT NULL,
    "paymentMethodType" TEXT,
    "paymentMethodDetails" JSONB,
    "description" TEXT,
    "customerEmail" TEXT,
    "customerName" TEXT,
    "lojistId" TEXT,
    "lojistName" TEXT,
    "subscriptionId" TEXT,
    "orderId" TEXT,
    "repairId" TEXT,
    "platformFee" DECIMAL(10,2),
    "netAmount" DECIMAL(10,2),
    "failureReason" TEXT,
    "webhookProcessedAt" TIMESTAMP(3),
    "reconciledAt" TIMESTAMP(3),
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "employees_userId_key" ON "public"."employees"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "employees_email_key" ON "public"."employees"("email");

-- CreateIndex
CREATE UNIQUE INDEX "payment_history_stripePaymentIntentId_key" ON "public"."payment_history"("stripePaymentIntentId");

-- CreateIndex
CREATE INDEX "payment_history_status_idx" ON "public"."payment_history"("status");

-- CreateIndex
CREATE INDEX "payment_history_paymentType_idx" ON "public"."payment_history"("paymentType");

-- CreateIndex
CREATE INDEX "payment_history_paymentMethodType_idx" ON "public"."payment_history"("paymentMethodType");

-- CreateIndex
CREATE INDEX "payment_history_lojistId_idx" ON "public"."payment_history"("lojistId");

-- CreateIndex
CREATE INDEX "payment_history_createdAt_idx" ON "public"."payment_history"("createdAt");

-- AddForeignKey
ALTER TABLE "public"."repairs" ADD CONSTRAINT "repairs_assignedEmployeeId_fkey" FOREIGN KEY ("assignedEmployeeId") REFERENCES "public"."employees"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."employees" ADD CONSTRAINT "employees_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."employees" ADD CONSTRAINT "employees_repairShopId_fkey" FOREIGN KEY ("repairShopId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."repair_status_history" ADD CONSTRAINT "repair_status_history_repairId_fkey" FOREIGN KEY ("repairId") REFERENCES "public"."repairs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."repair_status_history" ADD CONSTRAINT "repair_status_history_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "public"."employees"("id") ON DELETE SET NULL ON UPDATE CASCADE;
