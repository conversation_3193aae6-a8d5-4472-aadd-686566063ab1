'use client'

import { useState } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import { 
  CreditCard, 
  Loader2, 
  CheckCircle, 
  XCircle,
  AlertCircle
} from 'lucide-react'

// Carregar Stripe com verificação
const getStripePromise = () => {
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  if (!publishableKey) {
    console.error('❌ NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY não configurada')
    return null
  }
  console.log('✅ Carregando Stripe com chave:', publishableKey.substring(0, 20) + '...')
  return loadStripe(publishableKey)
}

const stripePromise = getStripePromise()

interface SimplePaymentFormProps {
  clientSecret: string
  amount: number
  currency?: string
  description: string
  onSuccess?: (paymentIntent: any) => void
  onError?: (error: string) => void
  onCancel?: () => void
  returnUrl?: string
  appearance?: any
}

export default function SimplePaymentForm({
  clientSecret,
  amount,
  currency = 'EUR',
  description,
  onSuccess,
  onError,
  onCancel,
  returnUrl,
  appearance
}: SimplePaymentFormProps) {
  if (!clientSecret) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <XCircle className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-red-800 font-medium">Erro no Pagamento</h3>
            <p className="text-red-600 text-sm mt-1">Client secret não fornecido</p>
          </div>
        </div>
        {onCancel && (
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Voltar
          </button>
        )}
      </div>
    )
  }

  if (!stripePromise) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <XCircle className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-red-800 font-medium">Erro de Configuração</h3>
            <p className="text-red-600 text-sm mt-1">Stripe não configurado corretamente</p>
          </div>
        </div>
        {onCancel && (
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Voltar
          </button>
        )}
      </div>
    )
  }

  // Configurações do Elements
  const elementsOptions = {
    clientSecret,
    appearance: appearance || {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#3b82f6',
        colorBackground: '#ffffff',
        colorText: '#1f2937',
        colorDanger: '#ef4444',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px'
      }
    }
  }

  return (
    <div className="max-w-md mx-auto">
      {/* Resumo do Pagamento */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Total a pagar:</span>
          <span className="text-xl font-bold text-gray-900">
            {new Intl.NumberFormat('pt-PT', {
              style: 'currency',
              currency: currency
            }).format(amount)}
          </span>
        </div>
        <div className="text-sm text-gray-500 mt-1">{description}</div>
      </div>

      {/* Formulário de Pagamento */}
      <Elements stripe={stripePromise} options={elementsOptions}>
        <PaymentForm
          onSuccess={onSuccess}
          onError={onError}
          returnUrl={returnUrl}
        />
      </Elements>
    </div>
  )
}

// Componente interno para o formulário de pagamento
function PaymentForm({
  onSuccess,
  onError,
  returnUrl
}: {
  onSuccess?: (paymentIntent: any) => void
  onError?: (error: string) => void
  returnUrl?: string
}) {
  const stripe = useStripe()
  const elements = useElements()
  const [processing, setProcessing] = useState(false)
  const [message, setMessage] = useState<string>('')

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setProcessing(true)
    setMessage('')

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: returnUrl || `${window.location.origin}/payment/success`
        },
        redirect: 'if_required'
      })

      if (error) {
        setMessage(error.message || 'Erro no pagamento')
        onError?.(error.message || 'Erro no pagamento')
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        setMessage('Pagamento processado com sucesso!')
        onSuccess?.({
          type: 'payment_succeeded',
          paymentIntent
        })
      }

    } catch (error) {
      console.error('Erro ao processar pagamento:', error)
      setMessage('Erro inesperado no pagamento')
      onError?.('Erro inesperado no pagamento')
    } finally {
      setProcessing(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Element */}
      <div className="p-4 border border-gray-200 rounded-lg">
        <PaymentElement />
      </div>

      {/* Mensagem de status */}
      {message && (
        <div className={`flex items-center p-3 rounded-lg ${
          message.includes('sucesso') 
            ? 'bg-green-50 text-green-800 border border-green-200'
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.includes('sucesso') ? (
            <CheckCircle className="w-5 h-5 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-2" />
          )}
          {message}
        </div>
      )}

      {/* Botão de pagamento */}
      <button
        type="submit"
        disabled={!stripe || processing}
        className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {processing ? (
          <>
            <Loader2 className="w-5 h-5 animate-spin mr-2" />
            Processando...
          </>
        ) : (
          <>
            <CreditCard className="w-5 h-5 mr-2" />
            Pagar Agora
          </>
        )}
      </button>

      {/* Informações de segurança */}
      <div className="text-center text-xs text-gray-500">
        <div className="flex items-center justify-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          Pagamento seguro processado pelo Stripe
        </div>
      </div>
    </form>
  )
}