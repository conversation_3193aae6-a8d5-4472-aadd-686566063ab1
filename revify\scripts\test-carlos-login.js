const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testCarlosLogin() {
  try {
    console.log('🔐 Testando login do Carlos...')

    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        profile: true
      }
    })

    if (!user) {
      console.log('❌ Utilizador não encontrado!')
      return
    }

    console.log('✅ Utilizador encontrado:')
    console.log('📧 Email:', user.email)
    console.log('👤 Nome:', user.name)
    console.log('🔑 Role:', user.role)
    console.log('👑 Super Admin:', user.isSuperAdmin)
    console.log('✅ Verificado:', user.isVerified)

    // Testar password
    const testPassword = 'Teste123123_'
    const isPasswordValid = await bcrypt.compare(testPassword, user.password)
    console.log('🔐 Password válida:', isPasswordValid ? '✅' : '❌')

    if (!isPasswordValid) {
      console.log('❌ Password incorreta! Vou corrigir...')
      
      // Corrigir password
      const hashedPassword = await bcrypt.hash(testPassword, 12)
      await prisma.user.update({
        where: { id: user.id },
        data: { password: hashedPassword }
      })
      
      console.log('✅ Password corrigida!')
    }

    // Verificar se tem todas as permissões necessárias
    console.log('\n🔍 Verificando permissões:')
    console.log('Role é ADMIN:', user.role === 'ADMIN' ? '✅' : '❌')
    console.log('É Super Admin:', user.isSuperAdmin ? '✅' : '❌')
    console.log('Está verificado:', user.isVerified ? '✅' : '❌')

    // Simular dados de sessão NextAuth
    const sessionData = {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isSuperAdmin: user.isSuperAdmin
      }
    }

    console.log('\n📋 Dados de sessão que serão criados:')
    console.log(JSON.stringify(sessionData, null, 2))

    console.log('\n🚀 Para testar o login:')
    console.log('1. Ir para: /auth/signin')
    console.log('2. Email: <EMAIL>')
    console.log('3. Password: Teste123123_')
    console.log('4. Deve redirecionar para: /admin')

  } catch (error) {
    console.error('❌ Erro ao testar login:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testCarlosLogin()
