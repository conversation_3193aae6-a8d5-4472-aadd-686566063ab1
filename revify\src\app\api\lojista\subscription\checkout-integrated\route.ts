import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeIntegratedSystem } from '@/lib/stripe/integrated-system'

// Mapear status do Stripe para status do Prisma
function mapStripeStatusToPrisma(stripeStatus: string): 'ACTIVE' | 'PAST_DUE' | 'CANCELED' | 'INCOMPLETE' | 'INCOMPLETE_EXPIRED' | 'TRIALING' | 'UNPAID' {
  switch (stripeStatus.toLowerCase()) {
    case 'active':
      return 'ACTIVE'
    case 'past_due':
      return 'PAST_DUE'
    case 'canceled':
    case 'cancelled':
      return 'CANCELED'
    case 'incomplete':
      return 'INCOMPLETE'
    case 'incomplete_expired':
      return 'INCOMPLETE_EXPIRED'
    case 'trialing':
      return 'TRIALING'
    case 'unpaid':
      return 'UNPAID'
    default:
      return 'INCOMPLETE'
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { planId, billingCycle, paymentMethod } = await request.json()

    if (!planId || !billingCycle || !paymentMethod) {
      return NextResponse.json(
        { message: 'Dados obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Buscar plano
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId }
    })

    if (!plan) {
      return NextResponse.json(
        { message: 'Plano não encontrado' },
        { status: 404 }
      )
    }

    console.log('📋 Plano encontrado:', {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      monthlyPrice: plan.monthlyPrice,
      yearlyPrice: plan.yearlyPrice
    })

    // Calcular preço
    const price = billingCycle === 'MONTHLY' ? Number(plan.monthlyPrice) : Number(plan.yearlyPrice)

    if (!price || price <= 0) {
      return NextResponse.json(
        { message: 'Preço do plano inválido' },
        { status: 400 }
      )
    }

    // Criar sistema integrado Stripe
    const stripeSystem = await createStripeIntegratedSystem()

    // Buscar ou criar cliente no Stripe
    const stripeCustomer = await stripeSystem.getOrCreateCustomer(
      session.user.id,
      session.user.email || '',
      session.user.name || undefined
    )

    // Cancelar subscrições ativas existentes
    const existingSubscriptions = await stripeSystem.getCustomerSubscriptions(stripeCustomer.id)
    for (const sub of existingSubscriptions) {
      if (sub.status === 'active' || sub.status === 'trialing') {
        await stripeSystem.cancelSubscription(sub.id, false) // Cancelar imediatamente
      }
    }

    // Buscar ou criar preço no Stripe
    let stripePriceId = plan.stripePriceId
    if (!stripePriceId) {
      console.log('🔄 Criando preço no Stripe para plano:', plan.name)

      // Preparar dados do produto (sem description - não suportado pelo Stripe)
      const productData = {
        name: plan.name || `Plano ${billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`
      }

      console.log('📦 Dados do produto para Stripe:', productData)

      // Criar preço no Stripe se não existir
      const stripePrice = await stripeSystem.stripe.prices.create({
        unit_amount: Math.round(price * 100),
        currency: 'eur',
        recurring: {
          interval: billingCycle === 'MONTHLY' ? 'month' : 'year'
        },
        product_data: productData,
        metadata: {
          planId: plan.id,
          billingCycle
        }
      })

      stripePriceId = stripePrice.id

      console.log('✅ Preço criado no Stripe:', {
        id: stripePriceId,
        amount: stripePrice.unit_amount,
        currency: stripePrice.currency
      })

      // Atualizar plano com o preço do Stripe
      await prisma.subscriptionPlan.update({
        where: { id: planId },
        data: { stripePriceId }
      })
    }

    // Criar subscrição no Stripe
    const { subscription: stripeSubscription, paymentIntent, setupIntent } = await stripeSystem.createSubscription(
      stripeCustomer.id,
      stripePriceId,
      paymentMethod as 'card' | 'multibanco' | 'klarna',
      {
        userId: session.user.id,
        planId,
        billingCycle,
        type: 'subscription_payment'
      }
    )

    // Criar ou atualizar subscrição na base de dados
    const currentPeriodStart = new Date(stripeSubscription.current_period_start * 1000)
    const currentPeriodEnd = new Date(stripeSubscription.current_period_end * 1000)

    const subscription = await prisma.subscription.upsert({
      where: { userId: session.user.id },
      create: {
        userId: session.user.id,
        planId,
        status: mapStripeStatusToPrisma(stripeSubscription.status),
        billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
        currentPeriodStart,
        currentPeriodEnd,
        stripeSubscriptionId: stripeSubscription.id,
        stripeCustomerId: stripeCustomer.id,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
      },
      update: {
        planId,
        status: mapStripeStatusToPrisma(stripeSubscription.status),
        billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
        currentPeriodStart,
        currentPeriodEnd,
        stripeSubscriptionId: stripeSubscription.id,
        stripeCustomerId: stripeCustomer.id,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
      }
    })

    // Criar registro de pagamento
    let paymentRecord = null
    if (paymentIntent) {
      paymentRecord = await prisma.subscriptionPayment.create({
        data: {
          subscriptionId: subscription.id,
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency.toUpperCase(),
          status: paymentIntent.status === 'succeeded' ? 'COMPLETED' : 'PENDING',
          stripePaymentIntentId: paymentIntent.id,
          periodStart: currentPeriodStart,
          periodEnd: currentPeriodEnd,
          paidAt: paymentIntent.status === 'succeeded' ? new Date() : null
        }
      })

      // Registrar no histórico de pagamentos para aparecer no dashboard admin
      const { recordPaymentHistory } = await import('@/lib/payments/payment-history')
      await recordPaymentHistory({
        stripePaymentIntentId: paymentIntent.id,
        stripeCustomerId: stripeCustomer.id,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency.toUpperCase(),
        status: paymentIntent.status === 'succeeded' ? 'SUCCEEDED' : 'PENDING',
        paymentType: 'SUBSCRIPTION',
        paymentMethodType: paymentMethod,
        description: `Subscrição ${plan.name} - ${billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`,
        customerEmail: session.user.email,
        customerName: session.user.name,
        lojistId: session.user.id,
        lojistName: session.user.name,
        subscriptionId: subscription.id,
        platformFee: (paymentIntent.amount / 100) * 0.05,
        netAmount: (paymentIntent.amount / 100) * 0.95,
        metadata: {
          planId,
          billingCycle,
          type: 'subscription_payment'
        }
      })
    }

    console.log('✅ Subscrição criada:', {
      subscriptionId: subscription.id,
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      paymentIntentId: paymentIntent?.id
    })

    // Processar baseado no método de pagamento
    if (paymentMethod === 'card') {
      // Para cartão, retornar client_secret para confirmação no frontend
      return NextResponse.json({
        success: true,
        paymentMethod: 'card',
        subscription: {
          id: subscription.id,
          stripeSubscriptionId: stripeSubscription.id,
          status: stripeSubscription.status
        },
        paymentIntent: paymentIntent ? {
          id: paymentIntent.id,
          client_secret: paymentIntent.client_secret,
          status: paymentIntent.status
        } : null,
        setupIntent: setupIntent ? {
          id: setupIntent.id,
          client_secret: setupIntent.client_secret,
          status: setupIntent.status
        } : null
      })

    } else if (paymentMethod === 'multibanco') {
      // Para Multibanco, criar PaymentIntent específico e confirmar para gerar referência REAL
      if (paymentIntent) {
        console.log('🔄 Confirmando PaymentIntent para Multibanco:', paymentIntent.id)

        const confirmedPaymentIntent = await stripeSystem.confirmPaymentIntent(
          paymentIntent.id,
          'multibanco',
          `${process.env.NEXTAUTH_URL}/lojista/subscription/success?payment_intent=${paymentIntent.id}`
        )

        console.log('✅ PaymentIntent confirmado:', {
          id: confirmedPaymentIntent.id,
          status: confirmedPaymentIntent.status,
          next_action: !!confirmedPaymentIntent.next_action
        })

        // Extrair detalhes Multibanco REAIS do Stripe
        const multibancoDetails = stripeSystem.extractMultibancoDetails(confirmedPaymentIntent)

        if (multibancoDetails) {
          console.log('✅ Detalhes Multibanco REAIS extraídos do Stripe:', {
            entity: multibancoDetails.entity,
            reference: multibancoDetails.reference.substring(0, 6) + '***',
            amount: multibancoDetails.amount,
            expiryDate: multibancoDetails.expiryDate
          })

          // Salvar referência REAL na base de dados
          await prisma.multibancoReference.create({
            data: {
              orderId: subscription.id,
              entity: multibancoDetails.entity,
              reference: multibancoDetails.reference.replace(/\s/g, ''),
              amount: Math.round(multibancoDetails.amount * 100),
              customerEmail: session.user.email,
              status: 'PENDING',
              expiryDate: multibancoDetails.expiryDate,
              stripePaymentIntentId: confirmedPaymentIntent.id
            }
          })

          return NextResponse.json({
            success: true,
            paymentMethod: 'multibanco',
            subscription: {
              id: subscription.id,
              stripeSubscriptionId: stripeSubscription.id,
              status: stripeSubscription.status
            },
            paymentIntent: {
              id: confirmedPaymentIntent.id,
              status: confirmedPaymentIntent.status
            },
            multibanco: multibancoDetails,
            redirectUrl: `/lojista/subscription/success?subscription_id=${subscription.id}&multibanco=true`
          })
        } else {
          console.error('❌ Não foi possível extrair detalhes Multibanco do Stripe')
          return NextResponse.json(
            { message: 'Erro ao gerar referência Multibanco' },
            { status: 500 }
          )
        }
      } else {
        console.error('❌ PaymentIntent não encontrado para Multibanco')
        return NextResponse.json(
          { message: 'Erro: PaymentIntent não encontrado' },
          { status: 500 }
        )
      }

    } else if (paymentMethod === 'klarna') {
      // Para Klarna, confirmar PaymentIntent
      if (paymentIntent) {
        const confirmedPaymentIntent = await stripeSystem.confirmPaymentIntent(
          paymentIntent.id,
          'klarna',
          `${process.env.NEXTAUTH_URL}/lojista/subscription/success?payment_intent=${paymentIntent.id}`
        )

        return NextResponse.json({
          success: true,
          paymentMethod: 'klarna',
          subscription: {
            id: subscription.id,
            stripeSubscriptionId: stripeSubscription.id,
            status: stripeSubscription.status
          },
          paymentIntent: {
            id: confirmedPaymentIntent.id,
            status: confirmedPaymentIntent.status,
            next_action: confirmedPaymentIntent.next_action
          },
          redirectUrl: confirmedPaymentIntent.next_action?.redirect_to_url?.url || 
                      `/lojista/subscription/success?subscription_id=${subscription.id}&klarna=true`
        })
      }
    }

    return NextResponse.json({
      success: true,
      subscription: {
        id: subscription.id,
        stripeSubscriptionId: stripeSubscription.id,
        status: stripeSubscription.status
      }
    })

  } catch (error) {
    console.error('Erro no checkout integrado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
