const fs = require('fs')
const path = require('path')

// Função para procurar por problemas de JavaScript
function findJSProblems(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    const problems = []

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Procurar por setSelectedDevice não definido
      if (line.includes('setSelectedDevice') && !line.includes('const') && !line.includes('let') && !line.includes('useState')) {
        problems.push({
          line: lineNum,
          issue: 'setSelectedDevice usado sem definição',
          content: line.trim()
        })
      }

      // Procurar por variáveis 'ev' problemáticas
      if (line.includes('const ev') || line.includes('let ev') || line.includes('var ev')) {
        // Verificar se há uso antes da declaração
        const beforeDeclaration = lines.slice(0, index)
        const hasUsageBefore = beforeDeclaration.some(prevLine => 
          prevLine.includes('ev.') || prevLine.includes('ev[') || prevLine.includes('ev ')
        )
        
        if (hasUsageBefore) {
          problems.push({
            line: lineNum,
            issue: 'Variável ev usada antes da declaração',
            content: line.trim()
          })
        }
      }

      // Procurar por problemas de hoisting
      if (line.includes('ev') && !line.includes('event') && !line.includes('prevent') && !line.includes('every')) {
        problems.push({
          line: lineNum,
          issue: 'Possível problema com variável ev',
          content: line.trim()
        })
      }
    })

    return problems
  } catch (error) {
    return [{ issue: `Erro ao ler arquivo: ${error.message}` }]
  }
}

// Arquivos para verificar
const filesToCheck = [
  'src/app/cliente/reparacoes/nova-v2/page.tsx',
  'src/components/AIDiagnosticInput.tsx',
  'src/components/ui/AddressAutocomplete.tsx'
]

console.log('🔍 Procurando problemas JavaScript...\n')

filesToCheck.forEach(file => {
  const fullPath = path.join(__dirname, '..', file)
  
  if (fs.existsSync(fullPath)) {
    console.log(`📁 Verificando: ${file}`)
    const problems = findJSProblems(fullPath)
    
    if (problems.length > 0) {
      console.log('❌ Problemas encontrados:')
      problems.forEach(problem => {
        console.log(`  Linha ${problem.line}: ${problem.issue}`)
        console.log(`    ${problem.content}`)
      })
    } else {
      console.log('✅ Nenhum problema encontrado')
    }
    console.log('')
  } else {
    console.log(`❌ Arquivo não encontrado: ${file}\n`)
  }
})

// Procurar por imports problemáticos
console.log('🔍 Verificando imports...')
const novaReparacaoPath = path.join(__dirname, '..', 'src/app/cliente/reparacoes/nova-v2/page.tsx')

if (fs.existsSync(novaReparacaoPath)) {
  const content = fs.readFileSync(novaReparacaoPath, 'utf8')
  const imports = content.match(/import.*from.*/g) || []
  
  console.log('📦 Imports encontrados:')
  imports.forEach(imp => {
    console.log(`  ${imp}`)
  })
  
  // Verificar se há componentes que podem ter setSelectedDevice
  const suspiciousImports = imports.filter(imp => 
    imp.includes('Diagnostic') || 
    imp.includes('Device') || 
    imp.includes('Select')
  )
  
  if (suspiciousImports.length > 0) {
    console.log('\n⚠️ Imports suspeitos:')
    suspiciousImports.forEach(imp => {
      console.log(`  ${imp}`)
    })
  }
}

console.log('\n🎯 Recomendações:')
console.log('1. Verificar se todos os useState estão definidos')
console.log('2. Verificar se não há conflitos de nomes de variáveis')
console.log('3. Verificar se componentes importados não têm erros')
console.log('4. Reiniciar o servidor de desenvolvimento')
