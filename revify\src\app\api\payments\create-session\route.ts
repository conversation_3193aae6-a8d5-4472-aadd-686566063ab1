import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        {
          message: 'É necessário fazer login para confirmar a reparação',
          requiresAuth: true
        },
        { status: 401 }
      )
    }

    const {
      repairData,
      amount,
      shopId,
      deliveryMethod,
      paymentMethod = 'card'
    } = await request.json()

    // Validações
    if (!repairData || !amount || !shopId) {
      return NextResponse.json(
        { message: 'Dados incompletos' },
        { status: 400 }
      )
    }

    // Buscar dados da loja
    const repairShop = await prisma.user.findUnique({
      where: { id: shopId },
      include: { profile: true }
    })

    if (!repairShop) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Criar reparação pendente na base de dados
    const repair = await prisma.repair.create({
      data: {
        customerId: session.user.id,
        repairShopId: shopId,
        deviceModelId: repairData.deviceId,
        problemTypeId: repairData.problemTypeId,
        description: repairData.description,
        status: 'PENDING_PAYMENT',
        estimatedPrice: amount,
        deliveryMethod: deliveryMethod,
        pickupAddress: repairData.pickupAddress || null,
        deliveryAddress: repairData.deliveryAddress || null,
        customerName: repairData.customerName,
        customerPhone: repairData.customerPhone,
        customerNif: repairData.customerNif || null
      }
    })

    // Configurar métodos de pagamento
    let paymentMethodTypes = ['card']
    if (paymentMethod === 'multibanco') {
      paymentMethodTypes = ['multibanco']
    } else if (paymentMethod === 'klarna') {
      paymentMethodTypes = ['klarna']
    } else if (paymentMethod === 'all') {
      paymentMethodTypes = ['card', 'multibanco', 'klarna']
    }

    // Buscar chave do Stripe das configurações do admin
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey || stripeSecretKey.includes('placeholder') || stripeSecretKey.includes('xyz')) {
      return NextResponse.json(
        { message: 'Stripe não configurado. Configure as chaves do Stripe no admin.' },
        { status: 400 }
      )
    }

    // Criar instância do Stripe com chave das configurações
    const stripe = await createStripeInstance(stripeSecretKey)

    console.log('🔧 Criando sessão Stripe com:', {
      paymentMethodTypes,
      paymentMethod,
      amount,
      customerEmail: session.user.email
    })

    // Criar sessão de pagamento Stripe
    const stripeSession = await stripe.checkout.sessions.create({
      payment_method_types: paymentMethodTypes,
      mode: 'payment',
      customer_email: session.user.email || undefined,
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: `Reparação - ${repairData.problemType}`,
              description: `${repairData.deviceName} - ${repairShop.profile?.companyName || repairShop.name}`,
              images: repairData.problemImages || []
            },
            unit_amount: Math.round(amount * 100), // Stripe usa centavos
          },
          quantity: 1,
        },
      ],
      metadata: {
        repairId: repair.id,
        customerId: session.user.id,
        shopId: shopId,
        type: 'repair_payment',
        paymentMethod
      },
      success_url: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${repair.id}/sucesso?session_id={CHECKOUT_SESSION_ID}&payment_method=${paymentMethod}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/nova-v2?step=5&error=payment_cancelled`,
      payment_intent_data: {
        ...(repairShop.profile?.stripeAccountId && repairShop.profile.stripeAccountId.trim().length > 0 ? {
          application_fee_amount: Math.round(amount * 100 * 0.05), // 5% de comissão para a plataforma
          transfer_data: {
            destination: repairShop.profile.stripeAccountId,
          },
        } : {}),
        metadata: {
          repairId: repair.id,
          escrow: 'true' // Marcar como pagamento em escrow
        }
      }
    })

    // Atualizar reparação com ID da sessão Stripe
    await prisma.repair.update({
      where: { id: repair.id },
      data: {
        stripeSessionId: stripeSession.id
      }
    })

    return NextResponse.json({
      sessionId: stripeSession.id,
      url: stripeSession.url,
      repairId: repair.id
    })

  } catch (error) {
    console.error('Erro ao criar sessão de pagamento:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
