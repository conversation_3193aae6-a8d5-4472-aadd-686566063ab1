import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeIntegratedSystem } from '@/lib/stripe/integrated-system'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

// Mapear status do Stripe para status do Prisma
function mapStripeStatusToPrisma(stripeStatus: string): 'ACTIVE' | 'PAST_DUE' | 'CANCELED' | 'INCOMPLETE' | 'INCOMPLETE_EXPIRED' | 'TRIALING' | 'UNPAID' {
  switch (stripeStatus.toLowerCase()) {
    case 'active':
      return 'ACTIVE'
    case 'past_due':
      return 'PAST_DUE'
    case 'canceled':
    case 'cancelled':
      return 'CANCELED'
    case 'incomplete':
      return 'INCOMPLETE'
    case 'incomplete_expired':
      return 'INCOMPLETE_EXPIRED'
    case 'trialing':
      return 'TRIALING'
    case 'unpaid':
      return 'UNPAID'
    default:
      return 'INCOMPLETE'
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar subscrição do usuário (incluindo pendentes)
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: session.user.id,
        status: {
          in: ['ACTIVE', 'INCOMPLETE', 'INCOMPLETE_EXPIRED', 'PAST_DUE', 'TRIALING', 'UNPAID']
        }
      },
      include: {
        plan: true,
        payments: {
          orderBy: {
            createdAt: 'desc'
          }
          // Remover take: 10 para mostrar todos os pagamentos
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!subscription) {
      return NextResponse.json({
        subscription: null,
        stripeData: null,
        invoices: [],
        message: 'Nenhuma subscrição encontrada'
      })
    }

    // Buscar dados do Stripe se tiver stripeCustomerId
    let stripeData = null
    let invoices: any[] = []

    if (subscription.stripeCustomerId) {
      try {
        const stripeSystem = await createStripeIntegratedSystem()

        // Buscar subscrições do Stripe
        const stripeSubscriptions = await stripeSystem.getCustomerSubscriptions(subscription.stripeCustomerId)

        // Buscar faturas do Stripe
        const stripeInvoices = await stripeSystem.getCustomerInvoices(subscription.stripeCustomerId)

        // Encontrar subscrição ativa no Stripe
        const activeStripeSubscription = stripeSubscriptions.find(sub =>
          sub.id === subscription.stripeSubscriptionId ||
          sub.status === 'active' ||
          sub.status === 'trialing' ||
          sub.status === 'incomplete'
        )

        if (activeStripeSubscription) {
          stripeData = {
            id: activeStripeSubscription.id,
            status: activeStripeSubscription.status,
            current_period_start: activeStripeSubscription.current_period_start,
            current_period_end: activeStripeSubscription.current_period_end,
            cancel_at_period_end: activeStripeSubscription.cancel_at_period_end,
            canceled_at: activeStripeSubscription.canceled_at,
            latest_invoice: activeStripeSubscription.latest_invoice
          }

          // Atualizar dados locais com dados do Stripe se diferentes
          const mappedStatus = mapStripeStatusToPrisma(activeStripeSubscription.status)
          if (subscription.status !== mappedStatus) {
            await prisma.subscription.update({
              where: { id: subscription.id },
              data: {
                status: mappedStatus,
                currentPeriodStart: new Date(activeStripeSubscription.current_period_start * 1000),
                currentPeriodEnd: new Date(activeStripeSubscription.current_period_end * 1000),
                cancelAtPeriodEnd: activeStripeSubscription.cancel_at_period_end,
                canceledAt: activeStripeSubscription.canceled_at ? new Date(activeStripeSubscription.canceled_at * 1000) : null
              }
            })
          }
        }

        // Processar faturas do Stripe
        invoices = stripeInvoices.map(invoice => ({
          id: invoice.id,
          status: invoice.status,
          amount_due: invoice.amount_due / 100,
          amount_paid: invoice.amount_paid / 100,
          currency: invoice.currency.toUpperCase(),
          created: invoice.created,
          due_date: invoice.due_date,
          hosted_invoice_url: invoice.hosted_invoice_url,
          invoice_pdf: invoice.invoice_pdf,
          payment_intent: invoice.payment_intent,
          subscription: invoice.subscription
        }))

        console.log('✅ Dados do Stripe sincronizados:', {
          subscriptionId: subscription.id,
          stripeSubscriptionId: activeStripeSubscription?.id,
          invoicesCount: invoices.length
        })

      } catch (stripeError) {
        console.error('⚠️ Erro ao buscar dados do Stripe:', stripeError)
        // Continuar sem dados do Stripe se houver erro
      }
    }

    return NextResponse.json({
      subscription: {
        id: subscription.id,
        status: subscription.status,
        billingCycle: subscription.billingCycle,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        stripeCustomerId: subscription.stripeCustomerId,
        plan: {
          id: subscription.plan.id,
          name: subscription.plan.name,
          description: subscription.plan.description,
          features: subscription.plan.features,
          monthlyPrice: Number(subscription.plan.monthlyPrice),
          yearlyPrice: Number(subscription.plan.yearlyPrice)
        },
        payments: subscription.payments.map(payment => ({
          id: payment.id,
          amount: Number(payment.amount),
          currency: payment.currency,
          status: payment.status,
          paidAt: payment.paidAt,
          periodStart: payment.periodStart,
          periodEnd: payment.periodEnd,
          multibancoEntity: payment.multibancoEntity,
          multibancoReference: payment.multibancoReference,
          createdAt: payment.createdAt
        }))
      },
      stripeData,
      invoices
    })

  } catch (error) {
    console.error('Erro ao buscar subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
