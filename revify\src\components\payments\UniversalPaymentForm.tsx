'use client'

import { useState } from 'react'
import SimplePaymentForm from '@/components/payments/SimplePaymentForm'
import MultibancoReference from '@/components/payments/MultibancoReference'
import { 
  CreditCard, 
  Smartphone,
  X, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface UniversalPaymentFormProps {
  title: string
  description: string
  amount: number
  currency?: string
  onSuccess?: () => void
  onCancel?: () => void
  isOpen: boolean
  paymentEndpoint: string // URL da API de pagamento específica
  paymentData?: any // Dados específicos para enviar à API
}

export default function UniversalPaymentForm({
  title,
  description,
  amount,
  currency = 'EUR',
  onSuccess,
  onCancel,
  isOpen,
  paymentEndpoint,
  paymentData = {}
}: UniversalPaymentFormProps) {
  const [paymentResult, setPaymentResult] = useState<any>(null)
  const [showMultibancoRef, setShowMultibancoRef] = useState(false)
  const [clientSecret, setClientSecret] = useState<string>('')
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'multibanco' | 'klarna'>('card')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  if (!isOpen) return null

  const initializePayment = async (method: 'card' | 'multibanco' | 'klarna') => {
    setLoading(true)
    setError('')
    setPaymentMethod(method)

    try {
      const response = await fetch(paymentEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...paymentData,
          paymentMethod: method
        })
      })

      const data = await response.json()

      if (response.ok) {
        if (data.type === 'multibanco_reference' && data.multibanco) {
          setPaymentResult(data)
          setShowMultibancoRef(true)
        } else if (data.type === 'payment_intent' && data.clientSecret) {
          setClientSecret(data.clientSecret)
        } else {
          setError('Tipo de resposta não reconhecido')
        }
      } else {
        setError(data.message || 'Erro ao inicializar pagamento')
      }
    } catch (error) {
      console.error('Erro ao inicializar pagamento:', error)
      setError('Erro ao inicializar pagamento')
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentSuccess = (result: any) => {
    console.log('Pagamento bem-sucedido:', result)
    setPaymentResult(result)

    // Se for referência Multibanco, mostrar os dados
    if (result.type === 'multibanco_reference' && result.multibanco) {
      setShowMultibancoRef(true)
    } else {
      // Para outros tipos, fechar modal e recarregar
      setTimeout(() => {
        onSuccess?.()
      }, 2000)
    }
  }

  const handlePaymentError = (error: string) => {
    console.error('Erro no pagamento:', error)
    setError(error)
  }

  // Se mostrar referência Multibanco
  if (showMultibancoRef && paymentResult?.multibanco) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Referência Multibanco Gerada
              </h2>
              <button
                onClick={onCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-center">
                  <Smartphone className="w-5 h-5 text-blue-600 mr-2" />
                  <span className="font-medium text-blue-900">
                    {title}
                  </span>
                </div>
                <div className="text-sm text-blue-700 mt-1">
                  {description}
                </div>
              </div>
            </div>

            <MultibancoReference
              entity={paymentResult.multibanco.entity}
              reference={paymentResult.multibanco.reference}
              amount={amount}
              currency={currency}
              expiresAt={paymentResult.multibanco.expires_at}
              description={description}
              onCopy={() => console.log('Dados copiados')}
            />

            <div className="mt-6 flex justify-center">
              <button
                onClick={() => {
                  onSuccess?.()
                }}
                className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700"
              >
                Continuar
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Se pagamento foi bem-sucedido (não Multibanco)
  if (paymentResult && paymentResult.type === 'payment_succeeded') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-md w-full p-6 text-center">
          <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Pagamento Concluído
          </h2>
          <p className="text-gray-600 mb-4">
            O seu pagamento foi processado com sucesso
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="text-sm text-gray-600 mb-1">Descrição:</div>
            <div className="font-bold text-gray-900">{description}</div>
            <div className="text-sm text-gray-600 mt-2">Valor pago:</div>
            <div className="text-xl font-bold text-green-600">
              {new Intl.NumberFormat('pt-PT', {
                style: 'currency',
                currency: currency
              }).format(amount)}
            </div>
          </div>

          <button
            onClick={() => onSuccess?.()}
            className="w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700"
          >
            Continuar
          </button>
        </div>
      </div>
    )
  }

  // Formulário de pagamento principal
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {title}
              </h2>
              <p className="text-gray-600">
                {description}
              </p>
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Resumo do pagamento */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-lg font-medium text-gray-900">Total a pagar:</span>
              <span className="text-2xl font-bold text-gray-900">
                {new Intl.NumberFormat('pt-PT', {
                  style: 'currency',
                  currency: currency
                }).format(amount)}
              </span>
            </div>
          </div>

          {/* Seleção de método de pagamento */}
          {!clientSecret && !showMultibancoRef && !loading && !error && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Escolha o método de pagamento
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => initializePayment('card')}
                  className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
                >
                  <CreditCard className="w-6 h-6 text-blue-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium text-gray-900">Cartão</div>
                    <div className="text-sm text-gray-500">Visa, Mastercard</div>
                  </div>
                </button>

                <button
                  onClick={() => initializePayment('multibanco')}
                  className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors"
                >
                  <div className="w-6 h-6 bg-green-600 rounded mr-3 flex items-center justify-center">
                    <span className="text-white text-xs font-bold">MB</span>
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-gray-900">Multibanco</div>
                    <div className="text-sm text-gray-500">Referência MB</div>
                  </div>
                </button>

                <button
                  onClick={() => initializePayment('klarna')}
                  className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg hover:border-pink-500 hover:bg-pink-50 transition-colors"
                >
                  <div className="w-6 h-6 bg-pink-500 rounded mr-3 flex items-center justify-center">
                    <span className="text-white text-xs font-bold">K</span>
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-gray-900">Klarna</div>
                    <div className="text-sm text-gray-500">Pague depois</div>
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Loading */}
          {loading && (
            <div className="flex items-center justify-center p-8">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
                <p className="text-gray-600">A inicializar pagamento...</p>
              </div>
            </div>
          )}

          {/* Error */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-center">
                <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Erro no Pagamento</h3>
                  <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
              </div>
              <button
                onClick={() => {
                  setError('')
                  setClientSecret('')
                }}
                className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Tentar Novamente
              </button>
            </div>
          )}

          {/* Formulário de pagamento com cartão/Klarna */}
          {clientSecret && (paymentMethod === 'card' || paymentMethod === 'klarna') && (
            <SimplePaymentForm
              clientSecret={clientSecret}
              amount={amount}
              currency={currency}
              description={description}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              onCancel={onCancel}
              returnUrl={`${window.location.origin}/payment/success`}
              appearance={{
                theme: 'stripe' as const,
                variables: {
                  colorPrimary: '#3b82f6',
                  colorBackground: '#ffffff',
                  colorText: '#1f2937',
                  colorDanger: '#ef4444',
                  fontFamily: 'system-ui, sans-serif',
                  spacingUnit: '4px',
                  borderRadius: '8px'
                }
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}