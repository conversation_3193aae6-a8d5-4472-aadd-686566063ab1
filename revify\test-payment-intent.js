// Teste simples para verificar se a API de PaymentIntent está funcionando
const testPaymentIntent = async () => {
  const baseUrl = 'http://localhost:3000'
  
  // Teste 1: Cartão (sem return_url)
  console.log('🧪 Testando PaymentIntent com Cartão...')
  try {
    const cardResponse = await fetch(`${baseUrl}/api/payments/create-payment-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: 10,
        currency: 'EUR',
        paymentMethod: 'card',
        metadata: {
          type: 'test',
          description: 'Teste de pagamento com cartão'
        },
        customerEmail: '<EMAIL>',
        customerName: 'Teste User'
      })
    })
    
    const cardData = await cardResponse.json()
    console.log('✅ Cartão:', cardResponse.ok ? 'SUCESSO' : 'ERRO', cardData)
  } catch (error) {
    console.log('❌ Erro no teste de cartão:', error.message)
  }

  // Teste 2: Multibanco (com return_url)
  console.log('\n🧪 Testando PaymentIntent com Multibanco...')
  try {
    const mbResponse = await fetch(`${baseUrl}/api/payments/create-payment-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: 10,
        currency: 'EUR',
        paymentMethod: 'multibanco',
        metadata: {
          type: 'test',
          description: 'Teste de pagamento com Multibanco'
        },
        customerEmail: '<EMAIL>',
        customerName: 'Teste User',
        returnUrl: 'http://localhost:3000/test/success'
      })
    })
    
    const mbData = await mbResponse.json()
    console.log('✅ Multibanco:', mbResponse.ok ? 'SUCESSO' : 'ERRO', mbData)
  } catch (error) {
    console.log('❌ Erro no teste de Multibanco:', error.message)
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testPaymentIntent()
}

module.exports = { testPaymentIntent }