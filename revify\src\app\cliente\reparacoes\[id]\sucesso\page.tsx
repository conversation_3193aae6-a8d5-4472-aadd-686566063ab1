'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useParams } from 'next/navigation'
import Link from 'next/link'
import { <PERSON>Circle, Wrench, ArrowLeft, Copy, Clock, Phone } from 'lucide-react'

export default function RepairSuccessPage() {
  const searchParams = useSearchParams()
  const params = useParams()
  const [repairDetails, setRepairDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [copied, setCopied] = useState(false)

  const sessionId = searchParams.get('session_id')
  const paymentMethod = searchParams.get('payment_method') || 'card'
  const isMultibanco = paymentMethod === 'multibanco'
  const repairId = params.id as string

  const [multibancoData, setMultibancoData] = useState<any>(null)

  useEffect(() => {
    fetchRepairDetails()
    if (isMultibanco) {
      fetchMultibancoData()
    }
  }, [repairId, isMultibanco])

  const fetchRepairDetails = async () => {
    try {
      const response = await fetch(`/api/cliente/repairs/${repairId}`)
      if (response.ok) {
        const data = await response.json()
        setRepairDetails(data)
      }
    } catch (error) {
      console.error('Erro ao buscar detalhes da reparação:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchMultibancoData = async () => {
    try {
      console.log('🔍 Buscando dados Multibanco para reparação:', repairId)

      // Primeiro tentar buscar pela API de reparações
      const response = await fetch(`/api/repairs/${repairId}`)
      if (response.ok) {
        const data = await response.json()
        console.log('📊 Dados da reparação:', data)

        if (data.paymentInfo && data.paymentInfo.method === 'multibanco') {
          console.log('✅ Dados Multibanco encontrados na reparação')
          setMultibancoData({
            entity: data.paymentInfo.entity,
            reference: data.paymentInfo.reference,
            amount: typeof data.paymentInfo.amount === 'number'
              ? data.paymentInfo.amount.toFixed(2)
              : parseFloat(data.paymentInfo.amount || '0').toFixed(2)
          })
          return
        }
      }

      // Se não encontrou na reparação, tentar buscar diretamente pela referência Multibanco
      console.log('🔍 Tentando buscar referência Multibanco diretamente...')
      const mbResponse = await fetch(`/api/payments/multibanco/generate-reference?order_id=${repairId}`)
      if (mbResponse.ok) {
        const mbData = await mbResponse.json()
        console.log('📊 Dados Multibanco diretos:', mbData)

        if (mbData.success && mbData.reference) {
          console.log('✅ Referência Multibanco encontrada')
          setMultibancoData({
            entity: mbData.reference.entity,
            reference: mbData.reference.reference,
            amount: typeof mbData.reference.amount === 'number'
              ? mbData.reference.amount.toFixed(2)
              : parseFloat(mbData.reference.amount || '0').toFixed(2)
          })
        }
      }
    } catch (error) {
      console.error('❌ Erro ao buscar dados Multibanco:', error)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
          
          {/* Header with gradient */}
          <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-8 py-12 text-center text-white">
            <div className="mx-auto flex items-center justify-center w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm mb-6">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>

            <h1 className="text-3xl font-bold mb-4">
              {isMultibanco ? 'Referência Multibanco Gerada!' : 'Reparação Agendada!'}
            </h1>

            <p className="text-green-100 text-lg">
              {isMultibanco
                ? 'Use os dados abaixo para efetuar o pagamento'
                : 'O seu pagamento foi processado com sucesso'
              }
            </p>
          </div>

          {/* Content */}
          <div className="px-8 py-8">
            
            {/* Multibanco Reference */}
            {isMultibanco && multibancoData && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                  Dados para Pagamento Multibanco
                </h2>

                <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                    <div>
                      <p className="text-sm text-gray-600 mb-2">Entidade</p>
                      <div className="flex items-center justify-center space-x-2">
                        <p className="text-3xl font-bold text-gray-900">{multibancoData.entity}</p>
                        <button
                          onClick={() => copyToClipboard(multibancoData.entity)}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          <Copy className="w-4 h-4 text-gray-500" />
                        </button>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-2">Referência</p>
                      <div className="flex items-center justify-center space-x-2">
                        <p className="text-3xl font-bold text-gray-900">{multibancoData.reference}</p>
                        <button
                          onClick={() => copyToClipboard(multibancoData.reference.replace(/\s/g, ''))}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          <Copy className="w-4 h-4 text-gray-500" />
                        </button>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-2">Valor</p>
                      <div className="flex items-center justify-center space-x-2">
                        <p className="text-3xl font-bold text-gray-900">€{multibancoData.amount}</p>
                        <button
                          onClick={() => copyToClipboard(multibancoData.amount)}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          <Copy className="w-4 h-4 text-gray-500" />
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 text-center">
                    <p className="text-sm text-red-700 mb-2">
                      <strong>Importante:</strong> Guarde esta referência para efetuar o pagamento.
                    </p>
                    <p className="text-xs text-gray-600">
                      O pagamento pode demorar até 24h a ser processado.
                    </p>
                  </div>
                  
                  {copied && (
                    <div className="mt-4 text-center">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Copiado!
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Repair Details */}
            {repairDetails && (
              <div className="bg-gray-50 rounded-xl p-6 mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Detalhes da Reparação
                </h2>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Número de Referência:</span>
                    <span className="font-medium">{repairDetails.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Dispositivo:</span>
                    <span className="font-medium">{repairDetails.deviceModel?.name || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Problema:</span>
                    <span className="font-medium">{repairDetails.problemType?.name || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Valor:</span>
                    <span className="font-medium">
                      €{typeof repairDetails.estimatedPrice === 'number'
                        ? repairDetails.estimatedPrice.toFixed(2)
                        : parseFloat(repairDetails.estimatedPrice || '0').toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {isMultibanco ? 'Aguardando Pagamento' : 'Pagamento Confirmado'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Next Steps */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Clock className="w-5 h-5 mr-2 text-blue-600" />
                Próximos Passos
              </h3>
              
              <div className="space-y-3">
                {isMultibanco ? (
                  <>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
                      <p className="text-gray-700">Efetue o pagamento usando os dados Multibanco acima</p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
                      <p className="text-gray-700">Aguarde a confirmação do pagamento (até 24h)</p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
                      <p className="text-gray-700">O técnico entrará em contacto para agendar a recolha</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">✓</div>
                      <p className="text-gray-700">Pagamento confirmado e reparação agendada</p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
                      <p className="text-gray-700">O técnico entrará em contacto nas próximas 2 horas</p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
                      <p className="text-gray-700">Acompanhe o progresso na área de cliente</p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Contact Info */}
            <div className="bg-gray-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Precisa de Ajuda?
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-1" />
                  <span>+351 123 456 789</span>
                </div>
                <div className="flex items-center">
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href={`/cliente/reparacoes/${repairId}`}
                className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors"
              >
                <Wrench className="w-5 h-5 mr-2" />
                Ver Reparação
              </Link>
              <Link
                href="/cliente/reparacoes"
                className="flex-1 inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Minhas Reparações
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
