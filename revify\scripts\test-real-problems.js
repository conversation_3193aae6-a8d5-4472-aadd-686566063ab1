const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testRealProblems() {
  console.log('🔍 TESTANDO PROBLEMAS REAIS...\n')

  try {
    // 1. Testar se a reconciliação está funcionando
    console.log('1️⃣ TESTANDO RECONCILIAÇÃO')
    
    const payments = await prisma.paymentHistory.findMany({
      where: {
        amount: { lte: 10 }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`📊 Pagamentos com valor ≤ €10: ${payments.length}`)
    
    if (payments.length > 0) {
      payments.forEach(payment => {
        console.log(`   ${payment.createdAt.toISOString().split('T')[0]} - €${payment.amount} - Tipo: ${payment.paymentType} - Status: ${payment.status}`)
      })
      
      const wrongTypes = payments.filter(p => p.paymentType === 'REPAIR' && p.amount <= 10)
      if (wrongTypes.length > 0) {
        console.log(`❌ ${wrongTypes.length} pagamentos ainda com tipo REPAIR incorreto`)
      } else {
        console.log('✅ Tipos de pagamento corretos')
      }
    }

    // 2. Testar se o sistema de subscrições está funcionando
    console.log('\n2️⃣ TESTANDO SISTEMA DE SUBSCRIÇÕES')
    
    const subscriptions = await prisma.subscription.findMany({
      include: {
        plan: true,
        user: {
          select: { email: true, name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    })

    console.log(`📋 Subscrições encontradas: ${subscriptions.length}`)
    
    if (subscriptions.length > 0) {
      subscriptions.forEach(sub => {
        console.log(`   ${sub.user.email} - ${sub.plan.name} - Status: ${sub.status}`)
        console.log(`   Stripe ID: ${sub.stripeSubscriptionId || 'N/A'}`)
      })
    } else {
      console.log('⚠️ Nenhuma subscrição encontrada')
    }

    // 3. Verificar se há planos com stripePriceId
    console.log('\n3️⃣ TESTANDO PLANOS COM STRIPE PRICE ID')
    
    const plans = await prisma.subscriptionPlan.findMany()
    console.log(`📦 Planos encontrados: ${plans.length}`)
    
    plans.forEach(plan => {
      console.log(`   ${plan.name}: €${plan.monthlyPrice}/mês`)
      console.log(`   Stripe Price ID: ${plan.stripePriceId || 'NÃO DEFINIDO'}`)
    })

    // 4. Verificar referências Multibanco
    console.log('\n4️⃣ TESTANDO REFERÊNCIAS MULTIBANCO')
    
    const multibancoRefs = await prisma.multibancoReference.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`🏦 Referências Multibanco: ${multibancoRefs.length}`)
    
    if (multibancoRefs.length > 0) {
      multibancoRefs.forEach(ref => {
        console.log(`   Entidade: ${ref.entity} | Referência: ${ref.reference.substring(0, 6)}*** | €${ref.amount/100} | Status: ${ref.status}`)
      })
    }

    // 5. Verificar se há erros na base de dados
    console.log('\n5️⃣ VERIFICANDO INTEGRIDADE DA BASE DE DADOS')
    
    // Verificar se há subscrições sem stripeSubscriptionId
    const subsWithoutStripe = await prisma.subscription.count({
      where: {
        stripeSubscriptionId: null
      }
    })
    
    if (subsWithoutStripe > 0) {
      console.log(`❌ ${subsWithoutStripe} subscrições sem Stripe ID`)
    } else {
      console.log('✅ Todas as subscrições têm Stripe ID')
    }

    // Verificar se há pagamentos órfãos
    const orphanPayments = await prisma.paymentHistory.count({
      where: {
        stripePaymentIntentId: null
      }
    })
    
    if (orphanPayments > 0) {
      console.log(`❌ ${orphanPayments} pagamentos sem Stripe Payment Intent ID`)
    } else {
      console.log('✅ Todos os pagamentos têm Stripe Payment Intent ID')
    }

    console.log('\n📋 RESUMO DOS PROBLEMAS REAIS:')
    console.log('=' .repeat(50))
    
    // Verificar problemas específicos mencionados
    const problemsFound = []
    
    if (wrongTypes && wrongTypes.length > 0) {
      problemsFound.push(`${wrongTypes.length} pagamentos com tipo incorreto`)
    }
    
    if (subsWithoutStripe > 0) {
      problemsFound.push(`${subsWithoutStripe} subscrições sem Stripe ID`)
    }
    
    if (orphanPayments > 0) {
      problemsFound.push(`${orphanPayments} pagamentos órfãos`)
    }

    if (problemsFound.length > 0) {
      console.log('❌ PROBLEMAS ENCONTRADOS:')
      problemsFound.forEach(problem => {
        console.log(`   - ${problem}`)
      })
    } else {
      console.log('✅ NENHUM PROBLEMA ENCONTRADO NA BASE DE DADOS')
    }

    console.log('\n🎯 PRÓXIMOS PASSOS:')
    console.log('1. Testar manualmente a reconciliação em /admin/pagamentos')
    console.log('2. Testar criação de nova subscrição')
    console.log('3. Verificar se o erro JavaScript persiste')
    console.log('4. Verificar logs do servidor em tempo real')

  } catch (error) {
    console.error('❌ Erro ao testar problemas:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testRealProblems()
