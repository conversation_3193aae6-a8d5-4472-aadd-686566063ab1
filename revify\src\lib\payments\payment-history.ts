import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'
import Strip<PERSON> from 'stripe'

export interface PaymentHistoryData {
  stripePaymentIntentId?: string
  stripeInvoiceId?: string
  stripeCustomerId?: string
  amount: number
  currency: string
  status: 'PENDING' | 'PROCESSING' | 'SUCCEEDED' | 'FAILED' | 'CANCELLED' | 'REFUNDED' | 'PARTIALLY_REFUNDED'
  paymentType: 'SUBSCRIPTION' | 'MARKETPLACE' | 'REPAIR' | 'ADDON' | 'UPGRADE'
  paymentMethodType?: string // 'card', 'multibanco', 'klarna'
  paymentMethodDetails?: any
  description?: string
  customerEmail?: string
  customerName?: string
  lojistId?: string
  lojistName?: string
  subscriptionId?: string
  orderId?: string
  repairId?: string
  platformFee?: number
  netAmount?: number
  failureReason?: string
  metadata?: any
}

/**
 * Registar pagamento no histórico
 */
export async function recordPaymentHistory(data: PaymentHistoryData) {
  try {
    const paymentHistory = await prisma.paymentHistory.create({
      data: {
        stripePaymentIntentId: data.stripePaymentIntentId,
        stripeInvoiceId: data.stripeInvoiceId,
        stripeCustomerId: data.stripeCustomerId,
        amount: data.amount,
        currency: data.currency,
        status: data.status,
        paymentType: data.paymentType,
        paymentMethodType: data.paymentMethodType,
        paymentMethodDetails: data.paymentMethodDetails,
        description: data.description,
        customerEmail: data.customerEmail,
        customerName: data.customerName,
        lojistId: data.lojistId,
        lojistName: data.lojistName,
        subscriptionId: data.subscriptionId,
        orderId: data.orderId,
        repairId: data.repairId,
        platformFee: data.platformFee,
        netAmount: data.netAmount,
        failureReason: data.failureReason,
        metadata: data.metadata,
        webhookProcessedAt: new Date()
      }
    })

    console.log('✅ Pagamento registado no histórico:', paymentHistory.id)
    return paymentHistory

  } catch (error) {
    console.error('❌ Erro ao registar pagamento no histórico:', error)
    throw error
  }
}

/**
 * Atualizar status do pagamento no histórico
 */
export async function updatePaymentHistoryStatus(
  stripePaymentIntentId: string,
  status: PaymentHistoryData['status'],
  failureReason?: string
) {
  try {
    const updated = await prisma.paymentHistory.updateMany({
      where: {
        stripePaymentIntentId: stripePaymentIntentId
      },
      data: {
        status,
        failureReason,
        webhookProcessedAt: new Date()
      }
    })

    console.log(`✅ Status atualizado para ${updated.count} pagamento(s):`, status)
    return updated

  } catch (error) {
    console.error('❌ Erro ao atualizar status do pagamento:', error)
    throw error
  }
}

/**
 * Extrair detalhes do método de pagamento do Stripe
 */
export function extractPaymentMethodDetails(paymentMethod: any): {
  type: string
  details: any
} {
  if (!paymentMethod) {
    return { type: 'unknown', details: {} }
  }

  switch (paymentMethod.type) {
    case 'card':
      return {
        type: 'card',
        details: {
          brand: paymentMethod.card?.brand,
          last4: paymentMethod.card?.last4,
          exp_month: paymentMethod.card?.exp_month,
          exp_year: paymentMethod.card?.exp_year,
          country: paymentMethod.card?.country
        }
      }

    case 'multibanco':
      return {
        type: 'multibanco',
        details: {
          entity: paymentMethod.multibanco?.entity,
          reference: paymentMethod.multibanco?.reference
        }
      }

    case 'klarna':
      return {
        type: 'klarna',
        details: {
          country: paymentMethod.klarna?.country,
          preferred_locale: paymentMethod.klarna?.preferred_locale
        }
      }

    default:
      return {
        type: paymentMethod.type || 'unknown',
        details: {}
      }
  }
}

/**
 * Processar webhook do Stripe e registar no histórico
 */
export async function processStripeWebhookForHistory(event: Stripe.Event) {
  try {
    console.log('🔔 Processando webhook para histórico:', event.type)

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break

      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break

      default:
        console.log('ℹ️ Evento não processado para histórico:', event.type)
    }

  } catch (error) {
    console.error('❌ Erro ao processar webhook para histórico:', error)
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  const stripe = await createStripeInstance()
  
  // Buscar método de pagamento
  let paymentMethod = null
  if (paymentIntent.payment_method) {
    try {
      paymentMethod = await stripe.paymentMethods.retrieve(paymentIntent.payment_method as string)
    } catch (error) {
      console.warn('Não foi possível buscar método de pagamento:', error)
    }
  }

  const methodDetails = extractPaymentMethodDetails(paymentMethod)
  const metadata = paymentIntent.metadata || {}

  // Determinar tipo de pagamento
  let paymentType: PaymentHistoryData['paymentType'] = 'REPAIR'
  if (metadata.type?.includes('subscription')) paymentType = 'SUBSCRIPTION'
  else if (metadata.type?.includes('marketplace')) paymentType = 'MARKETPLACE'
  else if (metadata.type?.includes('addon')) paymentType = 'ADDON'
  else if (metadata.type?.includes('upgrade')) paymentType = 'UPGRADE'

  // Calcular comissões
  const amount = paymentIntent.amount / 100
  const platformFee = amount * 0.05 // 5% de comissão
  const netAmount = amount - platformFee

  await recordPaymentHistory({
    stripePaymentIntentId: paymentIntent.id,
    stripeCustomerId: paymentIntent.customer as string,
    amount,
    currency: paymentIntent.currency.toUpperCase(),
    status: 'SUCCEEDED',
    paymentType,
    paymentMethodType: methodDetails.type,
    paymentMethodDetails: methodDetails.details,
    description: paymentIntent.description || `Pagamento ${paymentType.toLowerCase()}`,
    customerEmail: metadata.customerEmail,
    customerName: metadata.customerName,
    lojistId: metadata.lojistId,
    lojistName: metadata.lojistName,
    subscriptionId: metadata.subscriptionId,
    orderId: metadata.orderId,
    repairId: metadata.repairId,
    platformFee,
    netAmount,
    metadata
  })
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  const metadata = paymentIntent.metadata || {}
  
  let paymentType: PaymentHistoryData['paymentType'] = 'REPAIR'
  if (metadata.type?.includes('subscription')) paymentType = 'SUBSCRIPTION'
  else if (metadata.type?.includes('marketplace')) paymentType = 'MARKETPLACE'
  else if (metadata.type?.includes('addon')) paymentType = 'ADDON'
  else if (metadata.type?.includes('upgrade')) paymentType = 'UPGRADE'

  const amount = paymentIntent.amount / 100
  const failureReason = paymentIntent.last_payment_error?.message || 'Pagamento falhado'

  await recordPaymentHistory({
    stripePaymentIntentId: paymentIntent.id,
    stripeCustomerId: paymentIntent.customer as string,
    amount,
    currency: paymentIntent.currency.toUpperCase(),
    status: 'FAILED',
    paymentType,
    description: paymentIntent.description || `Pagamento ${paymentType.toLowerCase()}`,
    customerEmail: metadata.customerEmail,
    customerName: metadata.customerName,
    lojistId: metadata.lojistId,
    lojistName: metadata.lojistName,
    subscriptionId: metadata.subscriptionId,
    orderId: metadata.orderId,
    repairId: metadata.repairId,
    failureReason,
    metadata
  })
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  const amount = (invoice.amount_paid || 0) / 100
  const platformFee = amount * 0.05
  const netAmount = amount - platformFee

  await recordPaymentHistory({
    stripeInvoiceId: invoice.id,
    stripePaymentIntentId: invoice.payment_intent as string,
    stripeCustomerId: invoice.customer as string,
    amount,
    currency: invoice.currency?.toUpperCase() || 'EUR',
    status: 'SUCCEEDED',
    paymentType: 'SUBSCRIPTION',
    description: invoice.description || 'Pagamento de subscrição',
    customerEmail: invoice.customer_email,
    customerName: invoice.customer_name,
    subscriptionId: invoice.subscription as string,
    platformFee,
    netAmount,
    metadata: invoice.metadata
  })
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  const amount = (invoice.amount_due || 0) / 100

  await recordPaymentHistory({
    stripeInvoiceId: invoice.id,
    stripeCustomerId: invoice.customer as string,
    amount,
    currency: invoice.currency?.toUpperCase() || 'EUR',
    status: 'FAILED',
    paymentType: 'SUBSCRIPTION',
    description: invoice.description || 'Pagamento de subscrição',
    customerEmail: invoice.customer_email,
    customerName: invoice.customer_name,
    subscriptionId: invoice.subscription as string,
    failureReason: 'Falha no pagamento da fatura',
    metadata: invoice.metadata
  })
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const metadata = session.metadata || {}
  
  let paymentType: PaymentHistoryData['paymentType'] = 'MARKETPLACE'
  if (metadata.type?.includes('subscription')) paymentType = 'SUBSCRIPTION'
  else if (metadata.type?.includes('repair')) paymentType = 'REPAIR'
  else if (metadata.type?.includes('addon')) paymentType = 'ADDON'
  else if (metadata.type?.includes('upgrade')) paymentType = 'UPGRADE'

  const amount = (session.amount_total || 0) / 100
  const platformFee = amount * 0.05
  const netAmount = amount - platformFee

  await recordPaymentHistory({
    stripePaymentIntentId: session.payment_intent as string,
    stripeCustomerId: session.customer as string,
    amount,
    currency: session.currency?.toUpperCase() || 'EUR',
    status: 'SUCCEEDED',
    paymentType,
    paymentMethodType: 'card', // Checkout sessions são sempre cartão
    description: `Checkout ${paymentType.toLowerCase()}`,
    customerEmail: session.customer_details?.email,
    customerName: session.customer_details?.name,
    lojistId: metadata.lojistId,
    lojistName: metadata.lojistName,
    subscriptionId: metadata.subscriptionId,
    orderId: metadata.orderId,
    repairId: metadata.repairId,
    platformFee,
    netAmount,
    metadata
  })
}
