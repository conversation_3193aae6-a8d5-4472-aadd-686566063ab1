'use client'

import { useState } from 'react'
import DirectPaymentModal from '@/components/payments/DirectPaymentModal'
import { 
  CreditCard, 
  Calendar, 
  Crown, 
  X, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface SubscriptionPaymentFormProps {
  subscription: {
    id: string
    plan: {
      name: string
      monthlyPrice: number
      yearlyPrice: number
    }
    billingCycle: string
    status: string
  }
  pendingPayment?: {
    id: string
    amount: number
    periodStart: string
    periodEnd: string
    multibancoEntity?: string
    multibancoReference?: string
  }
  onSuccess?: () => void
  onCancel?: () => void
  isOpen: boolean
}

export default function SubscriptionPaymentForm({
  subscription,
  pendingPayment,
  onSuccess,
  onCancel,
  isOpen
}: SubscriptionPaymentFormProps) {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'card' | 'multibanco' | null>(null)

  if (!isOpen) return null

  const amount = subscription.billingCycle === 'MONTHLY' 
    ? subscription.plan.monthlyPrice 
    : subscription.plan.yearlyPrice

  const formatPeriod = (start: string, end: string) => {
    const startDate = new Date(start).toLocaleDateString('pt-PT')
    const endDate = new Date(end).toLocaleDateString('pt-PT')
    return `${startDate} - ${endDate}`
  }

  // Se método de pagamento foi selecionado, mostrar modal direto
  if (selectedPaymentMethod) {
    return (
      <DirectPaymentModal
        title="Pagamento de Subscrição"
        description={`${subscription.plan.name} - ${subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}${
          pendingPayment ? ` (${formatPeriod(pendingPayment.periodStart, pendingPayment.periodEnd)})` : ''
        }`}
        amount={amount}
        currency="EUR"
        paymentMethod={selectedPaymentMethod}
        isOpen={true}
        paymentEndpoint="/api/lojista/subscription/pay-unified"
        paymentData={{
          paymentId: pendingPayment?.id
        }}
        onSuccess={() => {
          setSelectedPaymentMethod(null)
          onSuccess?.()
        }}
        onCancel={() => {
          setSelectedPaymentMethod(null)
        }}
      />
    )
  }

  // Modal de seleção de método de pagamento
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Crown className="w-8 h-8 text-yellow-500 mr-3" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Pagamento de Subscrição
                </h2>
                <p className="text-gray-600">
                  {subscription.plan.name} - {subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}
                </p>
              </div>
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Detalhes da Subscrição */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-lg font-medium text-gray-900">Plano:</span>
              <span className="text-lg font-bold text-gray-900">{subscription.plan.name}</span>
            </div>
            
            {pendingPayment && (
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-gray-600">Período:</span>
                <span className="text-sm font-medium text-gray-900">
                  {formatPeriod(pendingPayment.periodStart, pendingPayment.periodEnd)}
                </span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <span className="text-xl font-medium text-gray-900">Total a pagar:</span>
              <span className="text-2xl font-bold text-gray-900">
                {new Intl.NumberFormat('pt-PT', {
                  style: 'currency',
                  currency: 'EUR'
                }).format(amount)}
              </span>
            </div>
          </div>

          {/* Se já existe referência Multibanco */}
          {pendingPayment?.multibancoEntity && pendingPayment?.multibancoReference && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                <span className="font-medium text-green-800">
                  Referência Multibanco já gerada
                </span>
              </div>
              <div className="mt-2 text-sm text-green-700">
                <div>Entidade: <strong>{pendingPayment.multibancoEntity}</strong></div>
                <div>Referência: <strong>{pendingPayment.multibancoReference}</strong></div>
                <div>Valor: <strong>{new Intl.NumberFormat('pt-PT', {
                  style: 'currency',
                  currency: 'EUR'
                }).format(amount)}</strong></div>
              </div>
            </div>
          )}

          {/* Seleção de Método de Pagamento */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Escolha o método de pagamento
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => setSelectedPaymentMethod('card')}
                className="flex items-center justify-center p-6 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
              >
                <CreditCard className="w-8 h-8 text-blue-600 mr-4" />
                <div className="text-left">
                  <div className="font-medium text-gray-900 text-lg">Cartão de Crédito</div>
                  <div className="text-sm text-gray-500">Visa, Mastercard</div>
                  <div className="text-sm text-blue-600 font-medium">Pagamento imediato</div>
                </div>
              </button>

              <button
                onClick={() => setSelectedPaymentMethod('multibanco')}
                className="flex items-center justify-center p-6 border-2 border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors"
              >
                <div className="w-8 h-8 bg-green-600 rounded mr-4 flex items-center justify-center">
                  <span className="text-white text-sm font-bold">MB</span>
                </div>
                <div className="text-left">
                  <div className="font-medium text-gray-900 text-lg">Multibanco</div>
                  <div className="text-sm text-gray-500">Referência MB</div>
                  <div className="text-sm text-green-600 font-medium">Pague no multibanco</div>
                </div>
              </button>
            </div>
          </div>

          {/* Informações Adicionais */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5" />
              <div className="text-sm text-gray-600">
                <p className="font-medium text-gray-900 mb-1">Informações importantes:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>O pagamento ativa imediatamente a sua subscrição</li>
                  <li>Receberá um email de confirmação após o pagamento</li>
                  <li>Para Multibanco, o pagamento pode demorar até 24h a ser processado</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}