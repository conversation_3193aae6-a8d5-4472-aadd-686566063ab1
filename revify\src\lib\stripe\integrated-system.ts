import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import Strip<PERSON> from 'stripe'

export interface StripeSubscriptionData {
  id: string
  status: string
  current_period_start: number
  current_period_end: number
  cancel_at_period_end: boolean
  canceled_at: number | null
  customer: string
  items: {
    data: Array<{
      id: string
      price: {
        id: string
        unit_amount: number
        currency: string
        recurring: {
          interval: string
        }
      }
    }>
  }
  latest_invoice: string | null
  metadata: Record<string, string>
}

export interface StripeInvoiceData {
  id: string
  status: string
  amount_due: number
  amount_paid: number
  currency: string
  created: number
  due_date: number | null
  hosted_invoice_url: string
  invoice_pdf: string
  payment_intent: string | null
  subscription: string | null
  metadata: Record<string, string>
}

/**
 * Sistema Integrado Stripe 100%
 * Segue documentação oficial do Stripe para PaymentIntents, Subscriptions e Invoices
 */
export class StripeIntegratedSystem {
  private stripe: Stripe

  constructor(stripeInstance: Stripe) {
    this.stripe = stripeInstance
  }

  /**
   * Criar subscrição no Stripe seguindo documentação oficial
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    paymentMethod: 'card' | 'multibanco' | 'klarna',
    metadata: Record<string, string> = {}
  ): Promise<{
    subscription: Stripe.Subscription
    paymentIntent?: Stripe.PaymentIntent
    setupIntent?: Stripe.SetupIntent
  }> {
    try {
      console.log('🔄 Criando subscrição no Stripe:', {
        customerId,
        priceId,
        paymentMethod
      })

      // Criar subscrição com configuração adequada para cada método
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: customerId,
        items: [{ price: priceId }],
        expand: ['latest_invoice.payment_intent', 'pending_setup_intent'],
        metadata
      }

      // Configuração específica para Multibanco
      if (paymentMethod === 'multibanco') {
        // Para Multibanco, usar collection_method: send_invoice
        subscriptionParams.collection_method = 'send_invoice'
        subscriptionParams.days_until_due = 3
        subscriptionParams.payment_settings = {
          payment_method_types: ['multibanco']
        }
        console.log('🏦 Configurando subscrição para Multibanco com send_invoice')
      } else {
        // Para cartão e outros métodos, usar charge_automatically
        subscriptionParams.payment_behavior = 'default_incomplete'
        subscriptionParams.payment_settings = {
          payment_method_types: this.getPaymentMethodTypes(paymentMethod),
          save_default_payment_method: 'on_subscription'
        }
        console.log('💳 Configurando subscrição para cartão com charge_automatically')
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionParams)

      console.log('✅ Subscrição criada no Stripe:', {
        id: subscription.id,
        status: subscription.status,
        latest_invoice: subscription.latest_invoice
      })

      // Extrair PaymentIntent ou SetupIntent
      let paymentIntent: Stripe.PaymentIntent | undefined
      let setupIntent: Stripe.SetupIntent | undefined

      if (subscription.latest_invoice && typeof subscription.latest_invoice === 'object') {
        const invoice = subscription.latest_invoice as Stripe.Invoice

        if (paymentMethod === 'multibanco' && invoice.id) {
          // Para Multibanco com send_invoice, criar PaymentIntent manualmente
          console.log('🏦 Criando PaymentIntent para Multibanco da invoice:', invoice.id)

          try {
            paymentIntent = await this.stripe.paymentIntents.create({
              amount: invoice.amount_due || 0,
              currency: invoice.currency || 'eur',
              payment_method_types: ['multibanco'],
              metadata: {
                ...metadata,
                invoice_id: invoice.id,
                subscription_id: subscription.id
              }
            })

            console.log('✅ PaymentIntent Multibanco criado:', paymentIntent.id)
          } catch (error) {
            console.error('❌ Erro ao criar PaymentIntent Multibanco:', error)
          }
        } else if (invoice.payment_intent && typeof invoice.payment_intent === 'object') {
          paymentIntent = invoice.payment_intent as Stripe.PaymentIntent
        }
      }

      if (subscription.pending_setup_intent && typeof subscription.pending_setup_intent === 'object') {
        setupIntent = subscription.pending_setup_intent as Stripe.SetupIntent
      }

      return {
        subscription,
        paymentIntent,
        setupIntent
      }

    } catch (error) {
      console.error('❌ Erro ao criar subscrição no Stripe:', error)
      throw error
    }
  }

  /**
   * Criar PaymentIntent para pagamento único seguindo documentação oficial
   */
  async createPaymentIntent(
    amount: number,
    currency: string,
    paymentMethod: 'card' | 'multibanco' | 'klarna',
    metadata: Record<string, string> = {},
    customerId?: string
  ): Promise<Stripe.PaymentIntent> {
    try {
      console.log('🔄 Criando PaymentIntent no Stripe:', {
        amount,
        currency,
        paymentMethod,
        customerId
      })

      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: Math.round(amount * 100),
        currency: currency.toLowerCase(),
        payment_method_types: this.getPaymentMethodTypes(paymentMethod),
        metadata,
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'always'
        }
      }

      if (customerId) {
        paymentIntentParams.customer = customerId
      }

      // Configurações específicas por método de pagamento
      if (paymentMethod === 'multibanco') {
        paymentIntentParams.payment_method_types = ['multibanco']
        paymentIntentParams.automatic_payment_methods = undefined
      } else if (paymentMethod === 'klarna') {
        paymentIntentParams.payment_method_types = ['klarna']
        paymentIntentParams.automatic_payment_methods = undefined
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams)

      console.log('✅ PaymentIntent criado no Stripe:', {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount
      })

      return paymentIntent

    } catch (error) {
      console.error('❌ Erro ao criar PaymentIntent no Stripe:', error)
      throw error
    }
  }

  /**
   * Confirmar PaymentIntent para gerar referência Multibanco
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethod: 'card' | 'multibanco' | 'klarna',
    returnUrl: string
  ): Promise<Stripe.PaymentIntent> {
    try {
      console.log('🔄 Confirmando PaymentIntent:', paymentIntentId)

      const confirmParams: Stripe.PaymentIntentConfirmParams = {
        return_url: returnUrl
      }

      if (paymentMethod === 'multibanco') {
        // Para Multibanco, criar payment method primeiro
        const paymentMethodObj = await this.stripe.paymentMethods.create({
          type: 'multibanco'
        })
        confirmParams.payment_method = paymentMethodObj.id
      } else if (paymentMethod === 'klarna') {
        // Para Klarna, criar payment method primeiro
        const paymentMethodObj = await this.stripe.paymentMethods.create({
          type: 'klarna'
        })
        confirmParams.payment_method = paymentMethodObj.id
      }

      const confirmedPaymentIntent = await this.stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmParams
      )

      console.log('✅ PaymentIntent confirmado:', {
        id: confirmedPaymentIntent.id,
        status: confirmedPaymentIntent.status,
        next_action: !!confirmedPaymentIntent.next_action
      })

      return confirmedPaymentIntent

    } catch (error) {
      console.error('❌ Erro ao confirmar PaymentIntent:', error)
      throw error
    }
  }

  /**
   * Buscar ou criar cliente no Stripe
   */
  async getOrCreateCustomer(
    userId: string,
    email: string,
    name?: string
  ): Promise<Stripe.Customer> {
    try {
      // Buscar cliente existente
      const existingCustomers = await this.stripe.customers.list({
        email,
        limit: 1
      })

      if (existingCustomers.data.length > 0) {
        const customer = existingCustomers.data[0]
        console.log('✅ Cliente existente encontrado:', customer.id)
        return customer
      }

      // Criar novo cliente
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          userId
        }
      })

      console.log('✅ Novo cliente criado no Stripe:', customer.id)
      return customer

    } catch (error) {
      console.error('❌ Erro ao buscar/criar cliente:', error)
      throw error
    }
  }

  /**
   * Buscar subscrições do cliente
   */
  async getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: 'all',
        expand: ['data.latest_invoice', 'data.items.data.price']
      })

      return subscriptions.data

    } catch (error) {
      console.error('❌ Erro ao buscar subscrições:', error)
      throw error
    }
  }

  /**
   * Buscar faturas do cliente
   */
  async getCustomerInvoices(customerId: string): Promise<Stripe.Invoice[]> {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit: 100,
        expand: ['data.payment_intent']
      })

      return invoices.data

    } catch (error) {
      console.error('❌ Erro ao buscar faturas:', error)
      throw error
    }
  }

  /**
   * Cancelar subscrição
   */
  async cancelSubscription(
    subscriptionId: string,
    cancelAtPeriodEnd: boolean = true
  ): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: cancelAtPeriodEnd
      })

      console.log('✅ Subscrição cancelada:', {
        id: subscription.id,
        cancel_at_period_end: subscription.cancel_at_period_end
      })

      return subscription

    } catch (error) {
      console.error('❌ Erro ao cancelar subscrição:', error)
      throw error
    }
  }

  /**
   * Criar portal de faturação
   */
  async createBillingPortalSession(
    customerId: string,
    returnUrl: string
  ): Promise<Stripe.BillingPortal.Session> {
    try {
      const session = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl
      })

      console.log('✅ Portal de faturação criado:', session.id)
      return session

    } catch (error) {
      console.error('❌ Erro ao criar portal de faturação:', error)
      throw error
    }
  }

  /**
   * Extrair detalhes Multibanco do PaymentIntent
   */
  extractMultibancoDetails(paymentIntent: Stripe.PaymentIntent): {
    entity: string
    reference: string
    amount: number
    expiryDate: Date
  } | null {
    if (paymentIntent.next_action?.multibanco_display_details) {
      const details = paymentIntent.next_action.multibanco_display_details
      return {
        entity: details.entity || '11249',
        reference: details.reference || '',
        amount: paymentIntent.amount / 100,
        expiryDate: details.expires_at 
          ? new Date(details.expires_at * 1000)
          : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
      }
    }
    return null
  }

  /**
   * Extrair detalhes Klarna do PaymentIntent
   */
  extractKlarnaDetails(paymentIntent: Stripe.PaymentIntent): {
    redirect_url: string
    return_url: string
  } | null {
    if (paymentIntent.next_action?.redirect_to_url) {
      return {
        redirect_url: paymentIntent.next_action.redirect_to_url.url,
        return_url: paymentIntent.next_action.redirect_to_url.return_url || ''
      }
    }
    return null
  }

  /**
   * Verificar se Klarna está disponível para o país/moeda
   */
  isKlarnaAvailable(currency: string, country?: string): boolean {
    const supportedCurrencies = ['AUD', 'CAD', 'CHF', 'CZK', 'DKK', 'EUR', 'GBP', 'NOK', 'NZD', 'PLN', 'RON', 'SEK', 'USD']
    const supportedCountries = ['AU', 'AT', 'BE', 'CA', 'CZ', 'DK', 'FI', 'FR', 'DE', 'GR', 'IE', 'IT', 'NL', 'NZ', 'NO', 'PL', 'PT', 'RO', 'ES', 'SE', 'CH', 'GB', 'US']

    const currencySupported = supportedCurrencies.includes(currency.toUpperCase())
    const countrySupported = !country || supportedCountries.includes(country.toUpperCase())

    return currencySupported && countrySupported
  }

  /**
   * Obter tipos de método de pagamento baseado na seleção
   */
  private getPaymentMethodTypes(paymentMethod: 'card' | 'multibanco' | 'klarna'): string[] {
    switch (paymentMethod) {
      case 'card':
        return ['card']
      case 'multibanco':
        return ['multibanco']
      case 'klarna':
        return ['klarna']
      default:
        return ['card']
    }
  }
}

/**
 * Factory function para criar instância do sistema integrado
 */
export async function createStripeIntegratedSystem(customStripeKey?: string): Promise<StripeIntegratedSystem> {
  // Buscar chave do Stripe se não fornecida
  if (!customStripeKey) {
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })
    customStripeKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
  }

  if (!customStripeKey) {
    throw new Error('Stripe não configurado')
  }

  // Criar instância do Stripe corretamente
  const stripeInstance = await createStripeInstance(customStripeKey)

  return new StripeIntegratedSystem(stripeInstance)
}
