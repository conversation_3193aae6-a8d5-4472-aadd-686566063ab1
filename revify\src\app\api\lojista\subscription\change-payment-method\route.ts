import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { subscriptionId, paymentMethod } = await request.json()

    if (!subscriptionId || !paymentMethod) {
      return NextResponse.json(
        { message: 'Subscription ID e método de pagamento são obrigatórios' },
        { status: 400 }
      )
    }

    // Buscar subscrição
    const subscription = await prisma.subscription.findFirst({
      where: { 
        id: subscriptionId,
        userId: session.user.id 
      },
      include: { 
        plan: true,
        payments: {
          where: { status: 'PENDING' },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Subscrição não encontrada' },
        { status: 404 }
      )
    }

    const pendingPayment = subscription.payments[0]
    if (!pendingPayment) {
      return NextResponse.json(
        { message: 'Nenhum pagamento pendente encontrado' },
        { status: 400 }
      )
    }

    const price = Number(pendingPayment.amount)

    if (paymentMethod === 'multibanco') {
      // Gerar nova referência Multibanco
      const multibancoRef = {
        entity: '11249',
        reference: Math.floor(100000000 + Math.random() * 900000000).toString(),
        amount: price.toFixed(2),
        expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      }

      // Atualizar pagamento com dados Multibanco
      await prisma.subscriptionPayment.update({
        where: { id: pendingPayment.id },
        data: {
          multibancoEntity: multibancoRef.entity,
          multibancoReference: multibancoRef.reference
        }
      })

      // Registrar no histórico de pagamentos para aparecer no dashboard admin
      const { recordPaymentHistory } = await import('@/lib/payments/payment-history')
      await recordPaymentHistory({
        amount: price,
        currency: 'EUR',
        status: 'PENDING',
        paymentType: 'SUBSCRIPTION',
        paymentMethodType: 'multibanco',
        description: `Alteração para Multibanco - ${subscription.plan.name}`,
        customerEmail: session.user.email,
        customerName: session.user.name,
        lojistId: session.user.id,
        lojistName: session.user.name,
        subscriptionId: subscription.id,
        platformFee: price * 0.05,
        netAmount: price * 0.95,
        metadata: {
          multibancoEntity: multibancoRef.entity,
          multibancoReference: multibancoRef.reference,
          type: 'subscription_payment_method_change'
        }
      })

      return NextResponse.json({
        success: true,
        multibanco: multibancoRef,
        redirectUrl: `/lojista/subscription/success?multibanco=true&entity=${multibancoRef.entity}&reference=${multibancoRef.reference}&amount=${multibancoRef.amount}&subscription=${subscription.id}`
      })
    } else if (paymentMethod === 'card') {
      try {
        // Buscar chave do Stripe das configurações do admin
        const stripeSecretSetting = await prisma.systemSettings.findUnique({
          where: { key: 'stripeSecretKey' }
        })

        const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

        if (!stripeSecretKey || stripeSecretKey.includes('placeholder') || stripeSecretKey.includes('xyz')) {
          return NextResponse.json(
            { message: 'Stripe não configurado. Configure as chaves do Stripe no admin.' },
            { status: 400 }
          )
        }

        // Criar sessão Stripe
        const stripe = await createStripeInstance(stripeSecretKey)

        const stripeSession = await stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          line_items: [
            {
              price_data: {
                currency: 'eur',
                product_data: {
                  name: `${subscription.plan.name} - ${subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`,
                  description: `Subscrição ${subscription.plan.name}`
                },
                unit_amount: Math.round(price * 100)
              },
              quantity: 1
            }
          ],
          mode: 'payment',
          success_url: `${process.env.NEXTAUTH_URL}/lojista/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${process.env.NEXTAUTH_URL}/lojista/subscricao`,
          metadata: {
            subscriptionId: subscription.id,
            paymentId: pendingPayment.id
          }
        })

        // Limpar dados Multibanco do pagamento
        await prisma.subscriptionPayment.update({
          where: { id: pendingPayment.id },
          data: {
            multibancoEntity: null,
            multibancoReference: null
          }
        })

        return NextResponse.json({
          success: true,
          checkoutUrl: stripeSession.url
        })
      } catch (stripeError) {
        console.error('Erro do Stripe:', stripeError)
        return NextResponse.json(
          { message: 'Pagamento por cartão temporariamente indisponível. Use Multibanco.' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { message: 'Método de pagamento inválido' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Erro ao alterar método de pagamento:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
