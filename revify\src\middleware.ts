import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'
// import { requiresActiveSubscription, isAllowedWithPendingSubscription } from '@/lib/subscription-access'

export async function middleware(request: NextRequest) {
  const hostname = request.headers.get('host') || ''
  const pathname = request.nextUrl.pathname

  // Verificar se é localhost com parâmetro ?shop=
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    const shopParam = request.nextUrl.searchParams.get('shop')

    if (shopParam && !pathname.startsWith('/shop/') && !pathname.startsWith('/api/') && !pathname.startsWith('/_next/')) {
      // Redirecionar para a mini-loja do lojista
      const shopUrl = new URL(request.url)
      shopUrl.pathname = `/shop/${shopParam}${pathname === '/' ? '' : pathname}`
      shopUrl.searchParams.delete('shop') // Remover o parâmetro da URL

      return NextResponse.rewrite(shopUrl)
    }
  }

  // Verificar se é um subdomínio .reparia.pt em produção
  const parts = hostname.split('.')
  if (parts.length >= 3 && parts[parts.length - 2] === 'reparia' && parts[parts.length - 1] === 'pt') {
    const subdomain = parts[0]

    // Excluir subdomínios do sistema
    if (!['www', 'api', 'admin', 'app', 'mail', 'ftp'].includes(subdomain)) {
      if (!pathname.startsWith('/shop/') && !pathname.startsWith('/api/') && !pathname.startsWith('/_next/')) {
        const shopUrl = new URL(request.url)
        shopUrl.pathname = `/shop/${subdomain}${pathname === '/' ? '' : pathname}`

        return NextResponse.rewrite(shopUrl)
      }
    }
  }

  // Permitir acesso sem login para nova reparação
  if (pathname === '/cliente/reparacoes/nova-v2' || pathname.startsWith('/cliente/reparacoes/nova-v2?')) {
    return NextResponse.next()
  }

  // Verificar autenticação para rotas protegidas
  if (pathname.startsWith('/admin') || pathname.startsWith('/lojista') || pathname.startsWith('/cliente') || pathname.startsWith('/estafeta')) {
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })

    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    // Verificar permissões específicas
    if (pathname.startsWith('/admin') && token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (pathname.startsWith('/lojista') && token.role !== 'REPAIR_SHOP' && token.role !== 'EMPLOYEE') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    // Para empregados, verificar permissões específicas em rotas sensíveis
    if (pathname.startsWith('/lojista') && token.role === 'EMPLOYEE') {
      // Rotas que requerem permissões específicas
      const restrictedRoutes = [
        { path: '/lojista/empregados', permission: 'canManageRepairs' },
        { path: '/lojista/financeiro', permission: 'canViewFinancials' },
        { path: '/lojista/produtos', permission: 'canManageInventory' },
        { path: '/lojista/loja-online', permission: 'canManageInventory' },
        { path: '/lojista/encomendas', permission: 'canManageInventory' }
      ]

      const restrictedRoute = restrictedRoutes.find(route => pathname.startsWith(route.path))
      if (restrictedRoute) {
        // Aqui você pode adicionar verificação de permissões se necessário
        // Por agora, permitir acesso e deixar as páginas verificarem
      }
    }

    // TEMPORÁRIO: Desabilitar verificação de subscrição no middleware
    // TODO: Reativar quando o sistema estiver estável
    /*
    // Verificar subscrição para lojistas em rotas protegidas
    if (pathname.startsWith('/lojista') && token.role === 'REPAIR_SHOP') {
      const needsActiveSubscription = requiresActiveSubscription(pathname)
      const allowedWithPending = isAllowedWithPendingSubscription(pathname)

      // Se a rota requer subscrição ativa, verificar status
      if (needsActiveSubscription && !allowedWithPending) {
        // Redirecionar para página de upgrade se não tem acesso
        return NextResponse.redirect(new URL('/lojista/upgrade', request.url))
      }
    }
    */

    if (pathname.startsWith('/cliente') && token.role !== 'CUSTOMER') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (pathname.startsWith('/estafeta') && token.role !== 'COURIER') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - api/upload (upload routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - uploads (uploaded files)
     */
    '/((?!api/auth|api/upload|_next/static|_next/image|favicon.ico|uploads).*)',
  ],
}
