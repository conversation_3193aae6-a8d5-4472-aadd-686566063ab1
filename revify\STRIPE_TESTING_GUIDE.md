# 🧪 Guia de Testes - Integração Stripe Completa

Este guia detalha como testar todos os componentes da integração Stripe implementada.

## 📋 Pré-requisitos

### 1. Configuração do Ambiente
```bash
# Variáveis de ambiente necessárias (.env.local)
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
CRON_SECRET=your_cron_secret_here
```

### 2. Base de Dados
```bash
# Aplicar migrações
npx prisma migrate dev

# Verificar schema
npx prisma studio
```

## 🔧 Testes Automatizados

### 1. Script de Teste Completo
```bash
# Executar todos os testes
npx tsx src/scripts/test-stripe-integration.ts
```

Este script testa:
- ✅ Conexão com Stripe
- ✅ Criação de PaymentIntents (cartão, Multibanco, Klarna)
- ✅ Geração de referências Multibanco
- ✅ Registo no histórico de pagamentos
- ✅ Processamento de webhooks
- ✅ Sistema de reconciliação
- ✅ APIs de admin

### 2. Testes Unitários das APIs
```bash
# Testar API de criação de PaymentIntent
curl -X POST http://localhost:3000/api/payments/create-payment-intent \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 25.99,
    "currency": "EUR",
    "paymentMethod": "card",
    "description": "Teste de pagamento",
    "customerEmail": "<EMAIL>"
  }'

# Testar API de reconciliação
curl -X POST http://localhost:3000/api/admin/payments/reconcile \
  -H "Content-Type: application/json" \
  -d '{"type": "all"}'
```

## 🖥️ Testes Manuais da Interface

### 1. Componente UnifiedPaymentForm

**Localização:** `/src/components/payments/UnifiedPaymentForm.tsx`

**Como testar:**
1. Criar página de teste:
```tsx
// pages/test-payment.tsx
import UnifiedPaymentForm from '@/components/payments/UnifiedPaymentForm'

export default function TestPayment() {
  return (
    <UnifiedPaymentForm
      amount={29.99}
      currency="EUR"
      description="Teste de pagamento unificado"
      customerEmail="<EMAIL>"
      customerName="Cliente Teste"
      onSuccess={(result) => console.log('Sucesso:', result)}
      onError={(error) => console.log('Erro:', error)}
    />
  )
}
```

**Cenários de teste:**
- ✅ Seleção de método de pagamento (cartão/Multibanco/Klarna)
- ✅ Preenchimento de dados do cartão
- ✅ Geração de referência Multibanco
- ✅ Redirecionamento Klarna
- ✅ Tratamento de erros
- ✅ Estados de carregamento

### 2. Componente MultibancoReference

**Como testar:**
```tsx
import MultibancoReference from '@/components/payments/MultibancoReference'

<MultibancoReference
  entity="12345"
  reference="123456789"
  amount={29.99}
  currency="EUR"
  expiresAt={new Date(Date.now() + 24*60*60*1000)}
  description="Teste Multibanco"
/>
```

**Verificar:**
- ✅ Exibição correta dos dados
- ✅ Funcionalidade de copiar
- ✅ Formatação de valores
- ✅ Indicador de expiração

### 3. Admin - Gestão de Pagamentos

**URL:** `/admin/pagamentos`

**Cenários de teste:**
1. **Visualização de dados:**
   - ✅ Lista de pagamentos carrega
   - ✅ Estatísticas são exibidas
   - ✅ Paginação funciona

2. **Filtros:**
   - ✅ Filtro por status
   - ✅ Filtro por tipo de pagamento
   - ✅ Filtro por método
   - ✅ Filtro por data
   - ✅ Pesquisa por texto

3. **Funcionalidades:**
   - ✅ Reconciliação manual
   - ✅ Exportação CSV
   - ✅ Visualização de detalhes
   - ✅ Atualização em tempo real

## 💳 Testes com Cartões de Teste

### Cartões Stripe para Teste

```
# Cartão que sempre funciona
4242424242424242

# Cartão que requer 3D Secure
4000000000003220

# Cartão que falha
4000000000000002

# Cartão insuficiente
4000000000009995
```

### Dados de Teste
```
Número: 4242424242424242
Expiração: 12/34
CVC: 123
CEP: 12345
```

## 🔄 Testes de Webhooks

### 1. Configuração Local
```bash
# Instalar Stripe CLI
stripe listen --forward-to localhost:3000/api/webhooks/stripe/unified
```

### 2. Eventos para Testar
```bash
# Pagamento bem-sucedido
stripe trigger payment_intent.succeeded

# Pagamento falhado
stripe trigger payment_intent.payment_failed

# Fatura paga
stripe trigger invoice.payment_succeeded

# Checkout concluído
stripe trigger checkout.session.completed
```

### 3. Verificar Processamento
- ✅ Webhook recebido (logs do servidor)
- ✅ Pagamento registado no histórico
- ✅ Status atualizado corretamente
- ✅ Dados completos salvos

## 📊 Testes de Reconciliação

### 1. Reconciliação Manual
```bash
# Via API
curl -X POST http://localhost:3000/api/admin/payments/reconcile \
  -H "Content-Type: application/json" \
  -d '{"type": "all"}'

# Via interface admin
# Ir para /admin/pagamentos e clicar "Reconciliar"
```

### 2. Reconciliação Automática
```bash
# Testar cron job (executa diariamente às 02:00 UTC)
curl http://localhost:3000/api/cron/reconcile-payments \
  -H "Authorization: Bearer your_cron_secret"
```

### 3. Verificar Resultados
- ✅ Pagamentos pendentes atualizados
- ✅ Novos pagamentos encontrados
- ✅ Erros reportados corretamente
- ✅ Logs detalhados

## 🌍 Testes de Métodos de Pagamento

### 1. Multibanco
**Cenário:** Cliente português, EUR
- ✅ Referência gerada corretamente
- ✅ Entidade e referência válidas
- ✅ Valor correto
- ✅ Data de expiração definida

### 2. Klarna
**Cenário:** Países suportados, valores dentro dos limites
- ✅ Disponibilidade verificada
- ✅ Redirecionamento funciona
- ✅ Métodos disponíveis (pay_now, pay_later, installments)

### 3. Cartão
**Cenário:** Todos os países
- ✅ Processamento imediato
- ✅ 3D Secure quando necessário
- ✅ Diferentes bandeiras (Visa, Mastercard, Amex)

## 📈 Testes de Performance

### 1. Carga de Pagamentos
```bash
# Criar múltiplos PaymentIntents
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/payments/create-payment-intent \
    -H "Content-Type: application/json" \
    -d "{\"amount\": $((RANDOM % 100 + 10)), \"paymentMethod\": \"card\"}"
done
```

### 2. Admin com Muitos Dados
- ✅ Paginação eficiente
- ✅ Filtros rápidos
- ✅ Exportação de grandes volumes
- ✅ Carregamento progressivo

## ✅ Checklist Final

### Funcionalidades Core
- [ ] PaymentIntent criado para todos os métodos
- [ ] Webhooks processam corretamente
- [ ] Histórico registado completamente
- [ ] Reconciliação funciona
- [ ] Admin mostra dados corretos

### Interface de Utilizador
- [ ] Componentes renderizam sem erros
- [ ] Estados de carregamento visíveis
- [ ] Erros tratados adequadamente
- [ ] Responsivo em mobile
- [ ] Acessibilidade básica

### Segurança
- [ ] Chaves de API protegidas
- [ ] Webhooks verificados
- [ ] Admin requer autenticação
- [ ] Dados sensíveis não expostos

### Performance
- [ ] APIs respondem < 2s
- [ ] Interface carrega < 3s
- [ ] Reconciliação completa < 30s
- [ ] Exportação funciona para 1000+ registos

## 🚀 Deploy para Produção

### 1. Variáveis de Ambiente
```bash
# Produção
STRIPE_SECRET_KEY=sk_live_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_live_...
```

### 2. Configuração Stripe Dashboard
- ✅ Webhooks configurados
- ✅ Métodos de pagamento ativados
- ✅ Configurações de país/moeda
- ✅ Compliance verificado

### 3. Monitorização
- ✅ Logs de erro configurados
- ✅ Alertas de webhook failures
- ✅ Métricas de reconciliação
- ✅ Dashboard de pagamentos

---

## 📞 Suporte

Em caso de problemas:
1. Verificar logs do servidor
2. Consultar Stripe Dashboard
3. Executar reconciliação manual
4. Verificar configurações de webhook

**Documentação Stripe:** https://stripe.com/docs
**Status Stripe:** https://status.stripe.com
