'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  Share2,
  Copy,
  Users,
  Euro,
  Gift,
  TrendingUp,
  ExternalLink,
  Facebook,
  Twitter,
  MessageCircle,
  Mail,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react'

interface ReferralData {
  referralCode: {
    code: string
    uses: number
    rewardAmount: number
  }
  balance: {
    available: number
    pending: number
    totalEarned: number
  }
  stats: {
    totalReferrals: number
    completedReferrals: number
    pendingReferrals: number
    totalEarned: number
    pendingEarnings: number
  }
  referrals: Array<{
    id: string
    referredUser: {
      name: string
      email: string
    }
    status: string
    reward: number
    paid: boolean
    createdAt: string
    completedAt?: string
  }>
}

export default function ReferralViralPanel() {
  const { data: session } = useSession()
  const [referralData, setReferralData] = useState<ReferralData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [copySuccess, setCopySuccess] = useState(false)

  // Não mostrar para empregados
  if (session?.user?.role === 'EMPLOYEE') {
    return null
  }

  useEffect(() => {
    if (session?.user) {
      fetchReferralData()
    }
  }, [session])

  const fetchReferralData = async () => {
    try {
      // Verificar se temos sessão válida
      if (!session?.user?.id) {
        console.log('Sessão não disponível, aguardando...')
        setIsLoading(false)
        return
      }

      // Primeiro tentar inicializar dados de referral
      console.log('🔄 Tentando carregar dados via /api/referral/init...')
      const initResponse = await fetch('/api/referral/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (initResponse.ok) {
        const data = await initResponse.json()
        console.log('✅ Dados carregados com sucesso via /api/referral/init:', data)
        setReferralData(data)
        return
      } else {
        console.error('❌ Erro na API /api/referral/init:', initResponse.status, initResponse.statusText)
        const errorText = await initResponse.text()
        console.error('❌ Detalhes do erro:', errorText)
      }

      // Fallback para APIs específicas
      const endpoint = session?.user.role === 'REPAIR_SHOP'
        ? '/api/lojista/referral'
        : '/api/cliente/referral'

      const response = await fetch(endpoint)
      if (response.ok) {
        const data = await response.json()
        setReferralData(data)
      } else {
        console.error('Erro na resposta da API:', response.status, response.statusText)
        // Inicializar dados básicos
        const fallbackData = {
          referralCode: {
            code: 'INIT' + Math.random().toString(36).substring(2, 8).toUpperCase(),
            uses: 0,
            rewardAmount: session?.user.role === 'REPAIR_SHOP' ? 25 : 5
          },
          balance: {
            available: 0,
            pending: 0,
            totalEarned: 0
          },
          stats: {
            totalReferrals: 0,
            completedReferrals: 0,
            pendingReferrals: 0,
            totalEarned: 0,
            pendingEarnings: 0
          },
          referrals: []
        }
        setReferralData(fallbackData)
      }
    } catch (error) {
      console.error('Erro ao buscar dados de referral:', error)
      // Dados fallback para evitar crash
      const fallbackData = {
        referralCode: {
          code: 'TEMP' + Math.random().toString(36).substring(2, 6).toUpperCase(),
          uses: 0,
          rewardAmount: session?.user.role === 'REPAIR_SHOP' ? 25 : 5
        },
        balance: {
          available: 0,
          pending: 0,
          totalEarned: 0
        },
        stats: {
          totalReferrals: 0,
          completedReferrals: 0,
          pendingReferrals: 0,
          totalEarned: 0,
          pendingEarnings: 0
        },
        referrals: []
      }
      setReferralData(fallbackData)
    } finally {
      setIsLoading(false)
    }
  }

  const copyReferralLink = async () => {
    if (!referralData) return

    const baseUrl = window.location.origin
    const referralLink = `${baseUrl}/auth/signup?ref=${referralData.referralCode.code}`
    
    try {
      await navigator.clipboard.writeText(referralLink)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('Erro ao copiar link:', error)
    }
  }

  const shareOnSocial = (platform: string) => {
    if (!referralData) return

    const baseUrl = window.location.origin
    const referralLink = `${baseUrl}/auth/signup?ref=${referralData.referralCode.code}`
    const message = session?.user.role === 'REPAIR_SHOP'
      ? `Junte-se à rede Reparia como loja parceira e receba €${referralData.referralCode.rewardAmount} de bónus! Use o código: ${referralData.referralCode.code}`
      : `Experimente a Reparia e receba €${referralData.referralCode.rewardAmount} de bónus! Use o meu código: ${referralData.referralCode.code}`

    const urls = {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}&quote=${encodeURIComponent(message)}`,
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(referralLink)}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(message + ' ' + referralLink)}`,
      email: `mailto:?subject=${encodeURIComponent('Convite para a Reparia')}&body=${encodeURIComponent(message + '\n\n' + referralLink)}`
    }

    window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400')
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!referralData || !session?.user) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center text-gray-500">
          <AlertCircle className="w-8 h-8 mx-auto mb-2" />
          <p>Sistema de referrals não disponível</p>
        </div>
      </div>
    )
  }

  const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
  const referralLink = `${baseUrl}/auth/signup?ref=${referralData.referralCode.code}`

  return (
    <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg shadow-sm border border-primary-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <Share2 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Sistema de Referrals</h3>
            <p className="text-sm text-gray-600">
              {session?.user.role === 'REPAIR_SHOP'
                ? `Convide outras lojas e ganhe €${referralData.referralCode.rewardAmount} por cada uma!`
                : `Convide amigos e ganhe €${referralData.referralCode.rewardAmount} por cada um!`
              }
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-primary-600">
            €{referralData.balance.available.toFixed(2)}
          </div>
          <div className="text-sm text-gray-500">Saldo disponível</div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-gray-900">{referralData.stats.totalReferrals}</div>
              <div className="text-sm text-gray-500">
                {session?.user.role === 'REPAIR_SHOP' ? 'Lojas Convidadas' : 'Amigos Convidados'}
              </div>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-600">{referralData.stats.completedReferrals}</div>
              <div className="text-sm text-gray-500">Completados</div>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-600">{referralData.stats.pendingReferrals}</div>
              <div className="text-sm text-gray-500">Pendentes</div>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-primary-600">€{referralData.stats.totalEarned.toFixed(2)}</div>
              <div className="text-sm text-gray-500">Total Ganho</div>
            </div>
            <Euro className="w-8 h-8 text-primary-500" />
          </div>
        </div>
      </div>

      {/* Referral Code and Sharing */}
      <div className="bg-white rounded-lg p-6 border border-gray-200 mb-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">O Seu Código de Referral</h4>
        
        <div className="flex items-center space-x-3 mb-4">
          <div className="flex-1 bg-gray-50 rounded-lg p-3 font-mono text-lg font-bold text-center text-primary-600 border-2 border-dashed border-primary-300">
            {referralData.referralCode.code}
          </div>
          <button
            onClick={copyReferralLink}
            className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
              copySuccess 
                ? 'bg-green-100 text-green-700 border border-green-300' 
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            {copySuccess ? (
              <>
                <CheckCircle className="w-4 h-4" />
                <span>Copiado!</span>
              </>
            ) : (
              <>
                <Copy className="w-4 h-4" />
                <span>Copiar Link</span>
              </>
            )}
          </button>
        </div>

        <div className="text-sm text-gray-600 mb-4">
          <strong>Link de referral:</strong> {referralLink}
        </div>

        {/* Social Sharing */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => shareOnSocial('whatsapp')}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <MessageCircle className="w-4 h-4" />
            <span>WhatsApp</span>
          </button>
          
          <button
            onClick={() => shareOnSocial('facebook')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Facebook className="w-4 h-4" />
            <span>Facebook</span>
          </button>
          
          <button
            onClick={() => shareOnSocial('twitter')}
            className="flex items-center space-x-2 px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
          >
            <Twitter className="w-4 h-4" />
            <span>Twitter</span>
          </button>
          
          <button
            onClick={() => shareOnSocial('email')}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Mail className="w-4 h-4" />
            <span>Email</span>
          </button>
        </div>
      </div>

      {/* Recent Referrals */}
      {Array.isArray(referralData.referrals) && referralData.referrals.length > 0 && (
        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Referrals Recentes</h4>
          <div className="space-y-3">
            {referralData.referrals.slice(0, 5).map((referral) => (
              <div key={referral.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    referral.status === 'COMPLETED' ? 'bg-green-500' : 'bg-yellow-500'
                  }`}></div>
                  <div>
                    <div className="font-medium text-gray-900">{referral.referredUser?.name || 'Nome não disponível'}</div>
                    <div className="text-sm text-gray-500">{referral.referredUser?.email || 'Email não disponível'}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-primary-600">€{(referral.reward || 0).toFixed(2)}</div>
                  <div className="text-sm text-gray-500">
                    {referral.status === 'COMPLETED' ? 'Pago' : 'Pendente'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
