'use client'

import { useState, useEffect } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import { Loader2, CreditCard, Smartphone, AlertCircle } from 'lucide-react'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

interface StripePaymentElementProps {
  amount: number
  currency?: string
  paymentMethod: 'card' | 'multibanco' | 'klarna'
  description: string
  metadata?: Record<string, string>
  customerEmail?: string
  customerName?: string
  onSuccess?: (result: any) => void
  onError?: (error: string) => void
  returnUrl?: string
  appearance?: any
  className?: string
}

export default function StripePaymentElement({
  amount,
  currency = 'EUR',
  paymentMethod,
  description,
  metadata = {},
  customerEmail,
  customerName,
  onSuccess,
  onError,
  returnUrl,
  appearance,
  className = ''
}: StripePaymentElementProps) {
  const [clientSecret, setClientSecret] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    createPaymentIntent()
  }, [amount, currency, paymentMethod])

  const createPaymentIntent = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/payments/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount,
          currency,
          paymentMethod,
          metadata: {
            ...metadata,
            description
          },
          customerEmail,
          customerName,
          returnUrl: returnUrl || `${window.location.origin}/payment/success`
        })
      })

      const data = await response.json()

      if (response.ok) {
        setClientSecret(data.paymentIntent.client_secret)
        
        // Para Multibanco, mostrar referência imediatamente se disponível
        if (paymentMethod === 'multibanco' && data.multibanco) {
          onSuccess?.({
            type: 'multibanco_reference',
            multibanco: data.multibanco,
            paymentIntent: data.paymentIntent
          })
        }
      } else {
        setError(data.error || 'Erro ao criar pagamento')
        onError?.(data.error || 'Erro ao criar pagamento')
      }

    } catch (error) {
      console.error('Erro ao criar PaymentIntent:', error)
      const errorMessage = 'Erro ao inicializar pagamento'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const elementsOptions = {
    clientSecret,
    appearance: appearance || {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#0570de',
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px'
      }
    }
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">A preparar pagamento...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center">
          <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-red-800 font-medium">Erro no Pagamento</h3>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  if (!clientSecret) {
    return (
      <div className={`text-center p-8 ${className}`}>
        <p className="text-gray-600">A inicializar...</p>
      </div>
    )
  }

  return (
    <div className={className}>
      <Elements stripe={stripePromise} options={elementsOptions}>
        <PaymentForm
          paymentMethod={paymentMethod}
          amount={amount}
          currency={currency}
          description={description}
          onSuccess={onSuccess}
          onError={onError}
          returnUrl={returnUrl}
        />
      </Elements>
    </div>
  )
}

function PaymentForm({
  paymentMethod,
  amount,
  currency,
  description,
  onSuccess,
  onError,
  returnUrl
}: {
  paymentMethod: string
  amount: number
  currency: string
  description: string
  onSuccess?: (result: any) => void
  onError?: (error: string) => void
  returnUrl?: string
}) {
  const stripe = useStripe()
  const elements = useElements()
  const [processing, setProcessing] = useState(false)
  const [message, setMessage] = useState<string>('')

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setProcessing(true)
    setMessage('')

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: returnUrl || `${window.location.origin}/payment/success`
        },
        redirect: paymentMethod === 'card' ? 'if_required' : 'always'
      })

      if (error) {
        const errorMessage = error.message || 'Erro no pagamento'
        setMessage(errorMessage)
        onError?.(errorMessage)
      } else if (paymentIntent) {
        if (paymentIntent.status === 'succeeded') {
          setMessage('Pagamento processado com sucesso!')
          onSuccess?.({
            type: 'payment_succeeded',
            paymentIntent
          })
        } else if (paymentIntent.status === 'requires_action') {
          // Para métodos que requerem ação (Multibanco, Klarna)
          onSuccess?.({
            type: 'requires_action',
            paymentIntent
          })
        }
      }

    } catch (error) {
      console.error('Erro ao processar pagamento:', error)
      const errorMessage = 'Erro inesperado no pagamento'
      setMessage(errorMessage)
      onError?.(errorMessage)
    } finally {
      setProcessing(false)
    }
  }

  const getPaymentMethodIcon = () => {
    switch (paymentMethod) {
      case 'card':
        return <CreditCard className="w-5 h-5" />
      case 'multibanco':
        return <Smartphone className="w-5 h-5" />
      case 'klarna':
        return (
          <div className="w-5 h-5 bg-pink-500 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">K</span>
          </div>
        )
      default:
        return <CreditCard className="w-5 h-5" />
    }
  }

  const getPaymentMethodLabel = () => {
    switch (paymentMethod) {
      case 'card':
        return 'Pagar com Cartão'
      case 'multibanco':
        return 'Gerar Referência Multibanco'
      case 'klarna':
        return 'Continuar com Klarna'
      default:
        return 'Pagar'
    }
  }

  return (
    <div className="space-y-6">
      {/* Resumo do pagamento */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600">Total:</span>
          <span className="text-xl font-bold text-gray-900">
            {new Intl.NumberFormat('pt-PT', {
              style: 'currency',
              currency: currency
            }).format(amount)}
          </span>
        </div>
        <p className="text-sm text-gray-500">{description}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Payment Element */}
        <div className="border border-gray-200 rounded-lg p-4">
          <PaymentElement />
        </div>

        {/* Mensagem de status */}
        {message && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <span className="text-red-800 text-sm">{message}</span>
            </div>
          </div>
        )}

        {/* Botão de pagamento */}
        <button
          type="submit"
          disabled={!stripe || processing}
          className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {processing ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin mr-2" />
              Processando...
            </>
          ) : (
            <>
              {getPaymentMethodIcon()}
              <span className="ml-2">{getPaymentMethodLabel()}</span>
            </>
          )}
        </button>

        {/* Informações de segurança */}
        <div className="text-center">
          <div className="flex items-center justify-center text-xs text-gray-500">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            Pagamento seguro processado pelo Stripe
          </div>
        </div>
      </form>
    </div>
  )
}
