import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { planId, billingCycle, paymentMethod } = await request.json()

    if (!planId || !billingCycle || !paymentMethod) {
      return NextResponse.json(
        { message: 'Dados obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Buscar plano
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId }
    })

    if (!plan) {
      return NextResponse.json(
        { message: 'Plano não encontrado' },
        { status: 404 }
      )
    }

    // Calcular preço
    const price = billingCycle === 'MONTHLY' ? Number(plan.monthlyPrice) : Number(plan.yearlyPrice)

    // Verificar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 500 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretKey)

    // Buscar ou criar subscrição pendente
    let subscription = await prisma.subscription.findFirst({
      where: {
        userId: session.user.id,
        status: {
          in: ['INCOMPLETE', 'INCOMPLETE_EXPIRED', 'UNPAID']
        }
      }
    })

    if (!subscription) {
      // Criar nova subscrição pendente
      const currentPeriodStart = new Date()
      const currentPeriodEnd = new Date()
      if (billingCycle === 'MONTHLY') {
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1)
      } else {
        currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1)
      }

      subscription = await prisma.subscription.create({
        data: {
          userId: session.user.id,
          planId,
          status: 'INCOMPLETE',
          billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
          currentPeriodStart,
          currentPeriodEnd
        }
      })
    } else {
      // Atualizar subscrição existente
      subscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          planId,
          billingCycle: billingCycle as 'MONTHLY' | 'YEARLY'
        }
      })
    }

    // Criar pagamento pendente
    const pendingPayment = await prisma.subscriptionPayment.create({
      data: {
        subscriptionId: subscription.id,
        amount: price,
        currency: 'EUR',
        status: 'PENDING',
        periodStart: subscription.currentPeriodStart,
        periodEnd: subscription.currentPeriodEnd
      }
    })

    if (paymentMethod === 'card') {
      // Fluxo para cartão - usar Checkout Session
      const checkoutSession = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'payment',
        line_items: [
          {
            price_data: {
              currency: 'eur',
              product_data: {
                name: `${plan.name} - ${billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`,
                description: `Subscrição ${plan.name}`
              },
              unit_amount: Math.round(price * 100)
            },
            quantity: 1
          }
        ],
        success_url: `${process.env.NEXTAUTH_URL}/lojista/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXTAUTH_URL}/lojista/subscricao`,
        customer_email: session.user.email || undefined,
        metadata: {
          subscriptionId: subscription.id,
          paymentId: pendingPayment.id,
          userId: session.user.id,
          planId,
          billingCycle,
          type: 'subscription_payment'
        }
      })

      // Atualizar pagamento com session ID
      await prisma.subscriptionPayment.update({
        where: { id: pendingPayment.id },
        data: {
          stripePaymentIntentId: checkoutSession.payment_intent as string || checkoutSession.id
        }
      })

      return NextResponse.json({
        success: true,
        paymentMethod: 'card',
        checkoutUrl: checkoutSession.url
      })

    } else if (paymentMethod === 'multibanco') {
      // Fluxo para Multibanco - usar PaymentIntent com multibanco
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(price * 100),
        currency: 'eur',
        payment_method_types: ['multibanco'],
        metadata: {
          subscriptionId: subscription.id,
          paymentId: pendingPayment.id,
          userId: session.user.id,
          planId,
          billingCycle,
          type: 'subscription_multibanco'
        }
      })

      // Atualizar pagamento com PaymentIntent ID
      await prisma.subscriptionPayment.update({
        where: { id: pendingPayment.id },
        data: {
          stripePaymentIntentId: paymentIntent.id
        }
      })

      // Confirmar PaymentIntent para gerar referência Multibanco
      const confirmedPaymentIntent = await stripe.paymentIntents.confirm(paymentIntent.id, {
        payment_method: {
          type: 'multibanco'
        },
        return_url: `${process.env.NEXTAUTH_URL}/lojista/subscription/success?payment_intent=${paymentIntent.id}`
      })

      console.log('PaymentIntent confirmado:', {
        id: confirmedPaymentIntent.id,
        status: confirmedPaymentIntent.status,
        next_action: !!confirmedPaymentIntent.next_action
      })

      // Extrair detalhes Multibanco
      let multibancoDetails = null
      if (confirmedPaymentIntent.next_action?.multibanco_display_details) {
        const details = confirmedPaymentIntent.next_action.multibanco_display_details
        multibancoDetails = {
          entity: details.entity,
          reference: details.reference,
          amount: price,
          expiryDate: details.expires_at ? new Date(details.expires_at * 1000) : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
        }

        // Salvar referência na base de dados
        await prisma.multibancoReference.create({
          data: {
            orderId: subscription.id,
            entity: details.entity || '11249',
            reference: details.reference?.replace(/\s/g, '') || '',
            amount: Math.round(price * 100),
            customerEmail: session.user.email,
            status: 'PENDING',
            expiryDate: multibancoDetails.expiryDate,
            stripePaymentIntentId: paymentIntent.id
          }
        })

        console.log('✅ Referência Multibanco gerada via Stripe:', {
          entity: details.entity,
          reference: details.reference?.substring(0, 6) + '***'
        })
      }

      return NextResponse.json({
        success: true,
        paymentMethod: 'multibanco',
        paymentIntent: {
          id: confirmedPaymentIntent.id,
          status: confirmedPaymentIntent.status,
          client_secret: confirmedPaymentIntent.client_secret
        },
        multibanco: multibancoDetails,
        redirectUrl: `/lojista/subscription/success?payment_intent=${paymentIntent.id}&multibanco=true`
      })

    } else {
      return NextResponse.json(
        { message: 'Método de pagamento não suportado' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Erro no checkout v2:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
