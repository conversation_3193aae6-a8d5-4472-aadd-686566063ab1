import Stripe from 'stripe'
import { prisma } from './prisma'

// Função para obter a chave do Stripe das configurações
async function getStripeSecretKey(): Promise<string> {
  try {
    const setting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    if (setting?.value) {
      return setting.value
    }
  } catch (error) {
    console.log('Erro ao buscar chave do Stripe do banco, usando .env')
  }

  // Fallback para .env
  return process.env.STRIPE_SECRET_KEY || ''
}

// Criar instância do Stripe com chave dinâmica
export async function createStripeInstance(customSecretKey?: string): Promise<Stripe> {
  const secretKey = customSecretKey || await getStripeSecretKey()

  if (!secretKey) {
    throw new Error('STRIPE_SECRET_KEY não configurada')
  }

  return new Stripe(secretKey, {
    apiVersion: '2024-06-20',
    typescript: true,
  })
}

// Instância padrão para compatibilidade (usa .env)
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_placeholder', {
  apiVersion: '2024-06-20',
  typescript: true,
})

export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  currency: 'eur',
  country: 'PT',
  payment_method_types: ['card'],
  mode: 'payment' as const,
}

// Payment method configurations
export const PAYMENT_METHODS = {
  card: {
    name: 'Cartão de Crédito/Débito',
    description: 'Visa, Mastercard, American Express',
    icon: '💳',
    provider: 'stripe',
  },
  paypal: {
    name: 'PayPal',
    description: 'Pague com a sua conta PayPal',
    icon: '🅿️',
    provider: 'stripe',
  },
  multibanco: {
    name: 'Multibanco',
    description: 'Referência MB',
    icon: '🏧',
    provider: 'internal',
  },
}

// Multibanco configuration - Real integration with Stripe
export const MULTIBANCO_CONFIG = {
  entity: '10611', // Real Multibanco entity for production (must match Stripe configuration)
  subEntity: '001', // Sub-entity for different shops
  testEntity: '11249', // Test entity for development
  // Note: Real Multibanco references should be generated via Stripe API
  // This ensures proper tracking and webhook notifications
}

// Get correct entity based on environment
export function getMultibancoEntity(): string {
  const isProduction = process.env.NODE_ENV === 'production'
  const stripeKey = process.env.STRIPE_SECRET_KEY || ''
  const isLiveKey = stripeKey.startsWith('sk_live_')

  // Use production entity only if both environment and Stripe key are production
  return (isProduction && isLiveKey) ? MULTIBANCO_CONFIG.entity : MULTIBANCO_CONFIG.testEntity
}

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: string
  client_secret?: string
  payment_method_types: string[]
}

// Generate Multibanco reference - prioritize local generation for reliability
export async function generateMultibancoReferenceViaStripe(
  amount: number,
  orderId: string,
  customerEmail?: string
): Promise<{
  entity: string
  reference: string
  amount: number
  expiryDate: Date
  paymentIntentId?: string
  source: 'stripe' | 'local_fallback'
}> {
  const correctEntity = getMultibancoEntity()

  console.log('🏧 Generating Multibanco reference:', {
    amount,
    orderId,
    entity: correctEntity,
    environment: process.env.NODE_ENV
  })

  // Para Multibanco, vamos usar geração local como método principal
  // pois é mais confiável que depender do Stripe
  try {
    // Tentar gerar via Stripe primeiro (opcional)
    let paymentIntentId: string | undefined

    try {
      const stripe = await createStripeInstance()

      // Criar PaymentIntent apenas para tracking, não para geração de referência
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency: 'eur',
        payment_method_types: ['multibanco'],
        metadata: {
          orderId: orderId,
          customerEmail: customerEmail || '',
          type: 'multibanco_manual_reference'
        },
        // Não tentar confirmar automaticamente - Multibanco é manual
        confirmation_method: 'manual',
        capture_method: 'automatic'
      })

      paymentIntentId = paymentIntent.id
      console.log('✅ PaymentIntent criado para tracking:', paymentIntentId)

    } catch (stripeError) {
      console.log('⚠️ Falha ao criar PaymentIntent, continuando com geração local:', stripeError)
    }

    // Gerar referência localmente (método principal)
    const localReference = generateLocalReference(correctEntity, amount, orderId)
    const expiryDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 dias

    console.log('✅ Referência Multibanco gerada localmente:', {
      entity: correctEntity,
      reference: localReference.substring(0, 6) + '***',
      amount,
      paymentIntentId
    })

    return {
      entity: correctEntity,
      reference: localReference,
      amount,
      expiryDate,
      paymentIntentId,
      source: 'local_fallback'
    }
  } catch (error) {
    console.error('❌ Error generating Multibanco reference via Stripe:', error)

    // Fallback to local generation
    const entity = getMultibancoEntity()
    const reference = generateLocalReference(entity, amount, orderId)
    const expiryDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)

    console.log('🔄 Using complete local fallback for Multibanco:', {
      entity,
      reference: reference.substring(0, 6) + '***',
      amount,
      source: 'local_fallback_error'
    })

    return {
      entity,
      reference,
      amount,
      expiryDate,
      source: 'local_fallback'
    }
  }
}

// Local reference generation (fallback) - Generates valid 9-digit Multibanco reference
function generateLocalReference(entity: string, amount: number, orderId: string): string {
  // Multibanco references must be exactly 9 digits
  // Format: XXXYYYYZZ where:
  // XXX = sequential number or identifier (3 digits)
  // YYYY = amount in cents (4 digits max)
  // ZZ = check digits (MOD 97)

  const amountInCents = Math.round(amount * 100)

  // Create a unique 3-digit identifier from orderId and timestamp
  const orderHash = orderId.replace(/\D/g, '').slice(-3).padStart(3, '0')
  const timeHash = (Date.now() % 1000).toString().padStart(3, '0')
  const identifier = orderHash || timeHash

  // Create a 4-digit amount identifier (max 9999 = €99.99)
  // For larger amounts, use modulo to fit in 4 digits
  const amountId = (amountInCents % 10000).toString().padStart(4, '0')

  // Create base reference (7 digits)
  const baseReference = `${identifier}${amountId}`

  // Calculate MOD 97 check digits (Portuguese standard)
  const baseNumber = parseInt(baseReference)
  const mod97 = baseNumber % 97
  const checkDigits = (98 - mod97).toString().padStart(2, '0')

  // Final 9-digit reference
  const fullReference = `${baseReference}${checkDigits}`

  console.log('Generated local Multibanco reference:', {
    entity,
    identifier,
    amountId,
    baseReference,
    mod97,
    checkDigits,
    fullReference,
    formatted: fullReference.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3')
  })

  // Format as XXX XXX XXX
  return fullReference.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3')
}

export interface MultibancoReference {
  entity: string
  reference: string
  amount: number
  expiryDate: string
}
