const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function verifySupeAdmin() {
  try {
    console.log('🔍 Verificando super admin Carlos...')

    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        profile: true
      }
    })

    if (!user) {
      console.log('❌ Utilizador não encontrado!')
      return
    }

    console.log('✅ Utilizador encontrado:')
    console.log('📧 Email:', user.email)
    console.log('👤 Nome:', user.name)
    console.log('🔑 Role:', user.role)
    console.log('✅ Verificado:', user.isVerified)
    console.log('👑 Super Admin:', user.isSuperAdmin)
    console.log('📅 Criado em:', user.createdAt.toLocaleString('pt-PT'))

    // Verificar se a password está correta
    if (user.password) {
      const isPasswordValid = await bcrypt.compare('Teste123123_', user.password)
      console.log('🔐 Password válida:', isPasswordValid ? '✅' : '❌')
    } else {
      console.log('🔐 Password: ❌ Não definida')
    }

    // Verificar perfil
    if (user.profile) {
      console.log('📋 Perfil:', '✅ Existe')
    } else {
      console.log('📋 Perfil:', '❌ Não existe')
    }

    console.log('\n🎯 Status final:')
    if (user.role === 'ADMIN' && user.isSuperAdmin && user.isVerified) {
      console.log('✅ Super admin configurado corretamente!')
      console.log('🚀 Pode fazer login em /admin/login')
    } else {
      console.log('❌ Configuração incompleta')
    }

  } catch (error) {
    console.error('❌ Erro ao verificar super admin:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifySupeAdmin()
