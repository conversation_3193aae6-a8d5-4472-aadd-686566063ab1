import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// GET - Buscar empregado específico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || (session.user.role !== 'REPAIR_SHOP' && session.user.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Determinar critérios de busca baseado no role
    const whereClause = session.user.role === 'REPAIR_SHOP'
      ? {
          id: params.id,
          repairShopId: session.user.id
        }
      : {
          id: params.id,
          userId: session.user.id // Empregado só pode ver seus próprios dados
        }

    const employee = await prisma.employee.findFirst({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            isVerified: true,
            createdAt: true
          }
        },
        assignedRepairs: {
          select: {
            id: true,
            status: true,
            customerName: true,
            deviceModel: {
              select: {
                name: true
              }
            },
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        },
        _count: {
          select: {
            assignedRepairs: true,
            statusUpdates: true
          }
        }
      }
    })

    if (!employee) {
      return NextResponse.json(
        { message: 'Empregado não encontrado' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      employee: {
        id: employee.id,
        userId: employee.userId,
        name: employee.name,
        email: employee.email,
        phone: employee.phone,
        position: employee.position,
        isActive: employee.isActive,
        permissions: employee.permissions,
        createdAt: employee.createdAt,
        user: employee.user,
        assignedRepairs: employee.assignedRepairs,
        stats: {
          totalAssignedRepairs: employee._count.assignedRepairs,
          totalStatusUpdates: employee._count.statusUpdates
        }
      }
    })

  } catch (error) {
    console.error('Erro ao buscar empregado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// PUT - Atualizar empregado
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, email, phone, position, isActive, permissions, password } = await request.json()

    // Verificar se empregado pertence à loja
    const existingEmployee = await prisma.employee.findFirst({
      where: {
        id: params.id,
        repairShopId: session.user.id
      },
      include: {
        user: true
      }
    })

    if (!existingEmployee) {
      return NextResponse.json(
        { message: 'Empregado não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se email já existe (se foi alterado)
    if (email && email !== existingEmployee.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email }
      })

      if (emailExists) {
        return NextResponse.json(
          { message: 'Email já está em uso' },
          { status: 400 }
        )
      }
    }

    // Atualizar em transação
    const result = await prisma.$transaction(async (tx) => {
      // Preparar dados para atualizar usuário
      const userUpdateData: any = {}
      if (name) userUpdateData.name = name
      if (email) userUpdateData.email = email
      if (password) {
        userUpdateData.password = await bcrypt.hash(password, 12)
      }

      // Atualizar usuário se há dados para atualizar
      if (Object.keys(userUpdateData).length > 0) {
        await tx.user.update({
          where: { id: existingEmployee.userId },
          data: userUpdateData
        })
      }

      // Atualizar empregado
      const employee = await tx.employee.update({
        where: { id: params.id },
        data: {
          ...(name && { name }),
          ...(email && { email }),
          ...(phone !== undefined && { phone }),
          ...(position !== undefined && { position }),
          ...(isActive !== undefined && { isActive }),
          ...(permissions && { permissions })
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              isVerified: true,
              createdAt: true
            }
          }
        }
      })

      return employee
    })

    return NextResponse.json({
      message: 'Empregado atualizado com sucesso',
      employee: {
        id: result.id,
        userId: result.userId,
        name: result.name,
        email: result.email,
        phone: result.phone,
        position: result.position,
        isActive: result.isActive,
        permissions: result.permissions,
        createdAt: result.createdAt,
        user: result.user
      }
    })

  } catch (error) {
    console.error('Erro ao atualizar empregado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// DELETE - Remover empregado
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se empregado pertence à loja
    const existingEmployee = await prisma.employee.findFirst({
      where: {
        id: params.id,
        repairShopId: session.user.id
      }
    })

    if (!existingEmployee) {
      return NextResponse.json(
        { message: 'Empregado não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se tem reparações atribuídas
    const assignedRepairsCount = await prisma.repair.count({
      where: {
        assignedEmployeeId: params.id,
        status: {
          notIn: ['COMPLETED', 'DELIVERED', 'CANCELLED']
        }
      }
    })

    if (assignedRepairsCount > 0) {
      return NextResponse.json(
        { message: `Não é possível remover empregado com ${assignedRepairsCount} reparações ativas atribuídas` },
        { status: 400 }
      )
    }

    // Remover empregado e usuário em transação
    await prisma.$transaction(async (tx) => {
      // Remover empregado
      await tx.employee.delete({
        where: { id: params.id }
      })

      // Remover usuário
      await tx.user.delete({
        where: { id: existingEmployee.userId }
      })
    })

    return NextResponse.json({
      message: 'Empregado removido com sucesso'
    })

  } catch (error) {
    console.error('Erro ao remover empregado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
