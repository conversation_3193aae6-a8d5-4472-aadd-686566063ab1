# 🔧 Troubleshooting - Login de Admin

## ✅ Status Atual

O utilizador <PERSON> est<PERSON> **corretamente configurado**:
- ✅ Email: `<EMAIL>`
- ✅ Password: `Teste123123_`
- ✅ Role: `ADMIN`
- ✅ Super Admin: `true`
- ✅ Verificado: `true`
- ✅ Perfil: Criado

## 🚨 Problema Identificado

**Erro:** `/admin/pagamentos` redireciona para `/admin/login` que dá 404

**Causa:** Mudanças no NextAuth não foram aplicadas (servidor precisa reiniciar)

## 🔧 Soluções

### 1. **Reiniciar o Servidor (OBRIGATÓRIO)**
```bash
# Parar o servidor atual (Ctrl+C)
# Depois executar:
npm run dev
# ou
yarn dev
```

### 2. **Limpar Cache do NextAuth**
```bash
# Limpar cache do Next.js
rm -rf .next
npm run dev
```

### 3. **Verificar Variáveis de Ambiente**
```bash
# Verificar se estão definidas:
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-here
```

## 🔍 Como Testar o Login

### Passo 1: Ir para a página de login
```
URL: /auth/signin
```

### Passo 2: Fazer login
- **Email:** `<EMAIL>`
- **Password:** `Teste123123_`

### Passo 3: Verificar redirecionamento
- Deve redirecionar para `/admin`
- Se redirecionar para `/` é porque não tem permissões

### Passo 4: Testar acesso direto
```
URL: /admin/pagamentos
```

## 🐛 Debugging

### 1. **Verificar Sessão no Browser**
```javascript
// No console do browser
console.log(await fetch('/api/auth/session').then(r => r.json()))
```

**Deve retornar:**
```json
{
  "user": {
    "id": "...",
    "email": "<EMAIL>",
    "name": "Carlos Teixeira",
    "role": "ADMIN",
    "isSuperAdmin": true
  }
}
```

### 2. **Verificar Logs do Servidor**
Procurar por:
- Erros de autenticação
- Redirects inesperados
- Problemas de middleware

### 3. **Testar API Diretamente**
```bash
# Testar se a API funciona
curl http://localhost:3000/api/admin/payments
```

## 🔄 Mudanças Aplicadas

### 1. **NextAuth Configuration** (`/src/lib/auth.ts`)
```typescript
// Adicionado isSuperAdmin à sessão
session.user.isSuperAdmin = token.isSuperAdmin as boolean
```

### 2. **TypeScript Types** (`/src/types/next-auth.d.ts`)
```typescript
interface Session {
  user: {
    // ...
    isSuperAdmin?: boolean
  }
}
```

### 3. **Middleware Updates**
- Agora inclui `isSuperAdmin` na verificação
- Redireciona corretamente para `/auth/signin`

## ✅ Checklist de Verificação

Antes de testar, confirmar:

- [ ] **Servidor reiniciado** após mudanças
- [ ] **Cache limpo** (.next removido)
- [ ] **Variáveis de ambiente** configuradas
- [ ] **Carlos existe** na base de dados
- [ ] **Password correta** (Teste123123_)
- [ ] **isSuperAdmin = true** na base de dados

## 🚀 Fluxo Correto

```
1. Utilizador vai para /admin/pagamentos
2. Middleware verifica autenticação
3. Se não autenticado → redireciona para /auth/signin
4. Utilizador faz login com credenciais do Carlos
5. NextAuth cria sessão com isSuperAdmin: true
6. Redireciona para /admin
7. Utilizador pode aceder a /admin/pagamentos
```

## 📞 Se Ainda Não Funcionar

### 1. **Verificar Base de Dados**
```bash
node scripts/test-admin-access.js
```

### 2. **Verificar Sessão**
```bash
# No browser, ir para:
/api/auth/session
```

### 3. **Logs Detalhados**
Adicionar logs no middleware para debug:
```typescript
console.log('Session:', session)
console.log('User role:', session?.user?.role)
console.log('Is super admin:', session?.user?.isSuperAdmin)
```

---

## 🎯 Resumo

**O problema mais provável é que o servidor precisa ser reiniciado para aplicar as mudanças no NextAuth.**

**Solução rápida:**
1. Parar servidor (Ctrl+C)
2. `npm run dev`
3. Ir para `/auth/signin`
4. Login com Carlos
5. Testar `/admin/pagamentos`

**Se ainda não funcionar, verificar os logs do servidor e a sessão no browser.** ✅
