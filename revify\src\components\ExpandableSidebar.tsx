'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import {
  Home, Wrench, Package, Plus, ShoppingCart, Percent, Store, Globe,
  Users2, LineChart, Settings, BarChart3, ChevronRight, ChevronDown,
  Truck, Euro, QrCode, Zap, UserPlus
} from 'lucide-react'

interface MenuItem {
  name: string
  href: string
  icon: any
}

interface MenuSection {
  title: string
  items: MenuItem[]
}

const menuItems: MenuSection[] = [
  {
    title: 'Dashboard',
    items: [
      { name: '<PERSON><PERSON><PERSON>', href: '/lojista', icon: Home }
    ]
  },
  {
    title: 'Reparações',
    items: [
      { name: 'Reparações Ativas', href: '/lojista/reparacoes', icon: Wrench },
      { name: 'Históric<PERSON>', href: '/lojista/reparacoes/historico', icon: BarChart3 },
      { name: 'Reparações Independentes', href: '/lojista/reparacoes/independentes', icon: Wrench },
      { name: 'Gestão de Preços', href: '/lojista/precos', icon: Euro }
    ]
  },
  {
    title: 'Marketplace',
    items: [
      { name: 'Meus Produtos', href: '/lojista/produtos', icon: Package },
      { name: 'Adicionar Produto', href: '/lojista/produtos/novo', icon: Plus },
      { name: 'Encomendas', href: '/lojista/encomendas', icon: ShoppingCart },
      { name: 'Cupões', href: '/lojista/cupoes', icon: Percent },
      { name: 'Configurar Envios', href: '/lojista/configuracoes/envios', icon: Truck }
    ]
  },
  {
    title: 'Reparia Hub',
    items: [
      { name: 'Comprar Peças', href: '/lojista/loja-pecas', icon: Zap },
      { name: 'Encomendas de Peças', href: '/lojista/encomendas-pecas', icon: ShoppingCart }
    ]
  },
  {
    title: 'Loja Online',
    items: [
      { name: 'Loja Online', href: '/lojista/loja-online', icon: Store },
      { name: 'Configurar Domínio', href: '/lojista/loja-online/configuracoes', icon: Globe }
    ]
  },
  {
    title: 'Negócio',
    items: [
      { name: 'Clientes', href: '/lojista/clientes', icon: Users2 },
      { name: 'Empregados', href: '/lojista/empregados', icon: UserPlus },
      { name: 'Financeiro', href: '/lojista/financeiro', icon: LineChart },
      { name: 'Gerar QR Code', href: '/lojista/qrcode', icon: QrCode },
      { name: 'Configurações', href: '/lojista/configuracoes', icon: Settings }
    ]
  }
]

export default function ExpandableSidebar() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const pathname = usePathname()
  const { data: session } = useSession()
  const [userPermissions, setUserPermissions] = useState<any>(null)

  // Buscar permissões do empregado
  useEffect(() => {
    if (session?.user?.role === 'EMPLOYEE' && session?.user?.employeeId) {
      fetchEmployeePermissions()
    }
  }, [session])

  const fetchEmployeePermissions = async () => {
    try {
      const response = await fetch(`/api/lojista/employees/${session?.user?.employeeId}`)
      if (response.ok) {
        const data = await response.json()
        setUserPermissions(data.employee?.permissions || {})
      }
    } catch (error) {
      console.error('Erro ao buscar permissões:', error)
    }
  }

  const handleMouseEnter = () => {
    setIsExpanded(true)
  }

  const handleMouseLeave = () => {
    setIsExpanded(false)
    setActiveDropdown(null)
  }

  const toggleDropdown = (sectionTitle: string) => {
    if (!isExpanded) return
    setActiveDropdown(activeDropdown === sectionTitle ? null : sectionTitle)
  }

  const isActiveSection = (section: MenuSection) => {
    return section.items.some(item => pathname === item.href || pathname.startsWith(item.href + '/'))
  }

  const isActiveItem = (item: MenuItem) => {
    return pathname === item.href || pathname.startsWith(item.href + '/')
  }

  // Filtrar menus baseado em permissões
  const hasPermissionForItem = (item: MenuItem) => {
    if (session?.user?.role === 'REPAIR_SHOP') {
      return true // Lojista tem acesso a tudo
    }

    if (session?.user?.role === 'EMPLOYEE') {
      // Se ainda não carregou as permissões, não mostrar nada
      if (!userPermissions) {
        return false
      }

      // Mapear rotas para permissões
      const routePermissions: { [key: string]: string } = {
        '/lojista/reparacoes': 'canViewRepairs',
        '/lojista/reparacoes/historico': 'canViewRepairs',
        '/lojista/reparacoes/independentes': 'canViewRepairs',
        '/lojista/precos': 'canManageRepairs',
        '/lojista/clientes': 'canViewFinancials',
        '/lojista/empregados': 'canManageRepairs',
        '/lojista/financeiro': 'canViewFinancials',
        '/lojista/produtos': 'canManageInventory',
        '/lojista/loja-online': 'canManageInventory',
        '/lojista/encomendas': 'canManageInventory'
      }

      const requiredPermission = routePermissions[item.href]
      if (requiredPermission) {
        return userPermissions[requiredPermission] === true
      }

      // Rotas básicas sempre permitidas para empregados
      const basicRoutes = ['/lojista', '/lojista/configuracoes']
      return basicRoutes.includes(item.href)
    }

    return false
  }

  const getFilteredMenuItems = () => {
    return menuItems.map(section => ({
      ...section,
      items: section.items.filter(hasPermissionForItem)
    })).filter(section => section.items.length > 0)
  }

  return (
    <aside 
      className={`fixed inset-y-0 left-0 z-50 bg-white border-r border-gray-200 shadow-lg transition-all duration-300 ${
        isExpanded ? 'w-64' : 'w-16'
      }`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center gap-3 p-4 border-b border-gray-200">
          <Link
            href="/lojista"
            className="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg text-white font-bold text-sm"
          >
            R
          </Link>
          {isExpanded && (
            <div>
              <span className="font-bold text-gray-900">Reparia</span>
              <p className="text-xs text-gray-500">Painel do Lojista</p>
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-2 space-y-1 overflow-y-auto">
          {getFilteredMenuItems().map((section, sectionIndex) => {
            const hasActiveItem = isActiveSection(section)
            const isDropdownOpen = activeDropdown === section.title
            const showDropdown = isExpanded && section.items.length > 1

            return (
              <div key={sectionIndex}>
                {section.items.length === 1 ? (
                  // Single item - render directly
                  <Link
                    href={section.items[0].href}
                    className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 group ${
                      isActiveItem(section.items[0])
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                    title={!isExpanded ? section.items[0].name : undefined}
                  >
                    {(() => {
                      const IconComponent = section.items[0].icon
                      return (
                        <IconComponent className={`w-4 h-4 flex-shrink-0 ${
                          isActiveItem(section.items[0]) ? 'text-primary-600' : 'text-gray-500'
                        }`} />
                      )
                    })()}
                    {isExpanded && (
                      <span className="text-sm font-medium">{section.items[0].name}</span>
                    )}
                  </Link>
                ) : (
                  // Multiple items - render with dropdown
                  <div>
                    <button
                      onClick={() => toggleDropdown(section.title)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 group ${
                        hasActiveItem
                          ? 'bg-primary-50 text-primary-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                      title={!isExpanded ? section.title : undefined}
                    >
                      {(() => {
                        const IconComponent = section.items[0].icon
                        return (
                          <IconComponent className={`w-4 h-4 flex-shrink-0 ${
                            hasActiveItem ? 'text-primary-600' : 'text-gray-500'
                          }`} />
                        )
                      })()}
                      {isExpanded && (
                        <>
                          <span className="text-sm font-medium flex-1 text-left">{section.title}</span>
                          {showDropdown && (
                            <ChevronRight className={`w-3 h-3 transition-transform duration-200 ${
                              isDropdownOpen ? 'rotate-90' : ''
                            }`} />
                          )}
                        </>
                      )}
                    </button>

                    {/* Dropdown Items */}
                    {isExpanded && isDropdownOpen && (
                      <div className="ml-8 mt-1 space-y-1">
                        {section.items.map((item, itemIndex) => (
                          <Link
                            key={itemIndex}
                            href={item.href}
                            className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 text-sm ${
                              isActiveItem(item)
                                ? 'bg-primary-100 text-primary-700 border border-primary-200'
                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                            }`}
                          >
                            {(() => {
                              const IconComponent = item.icon
                              return (
                                <IconComponent className={`w-3 h-3 flex-shrink-0 ${
                                  isActiveItem(item) ? 'text-primary-600' : 'text-gray-400'
                                }`} />
                              )
                            })()}
                            <span className="text-xs">{item.name}</span>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </nav>

        {/* Footer */}
        {isExpanded && (
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              Versão 2.0.1
            </div>
          </div>
        )}
      </div>
    </aside>
  )
}
