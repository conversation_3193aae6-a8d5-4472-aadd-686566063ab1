'use client'

import { useState, useEffect, Suspense, useCallback, useMemo } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useTranslation } from '@/hooks/useTranslation'
import ModernLayout from '@/components/ModernLayout'
import Link from 'next/link'
import { ArrowLeft, ArrowRight, MapPin, Clock, Euro, Truck, Store, Mail, User, CreditCard, Star } from 'lucide-react'

interface RepairFormData {
  categoryId: string
  brandId: string
  deviceId: string
  problemTypeId: string
  description: string
  problemImages: File[]
  selectedShopId: string
  customerLocation: string
  deliveryMethod: string
  pickupAddress: string
  deliveryAddress: string
  customerName: string
  customerPhone: string
  customerNif: string
  customerPostalCode: string
}

function NovaReparacaoPageContent() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { t } = useTranslation()

  // Estados principais
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Estados de dados
  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [devices, setDevices] = useState<any[]>([])
  const [problemTypes, setProblemTypes] = useState<any[]>([])
  const [availableShops, setAvailableShops] = useState<any[]>([])
  const [savedAddresses, setSavedAddresses] = useState<any[]>([])

  // Estado do formulário
  const [formData, setFormData] = useState<RepairFormData>({
    categoryId: searchParams?.get('categoryId') || '',
    brandId: searchParams?.get('brandId') || '',
    deviceId: searchParams?.get('deviceId') || '',
    problemTypeId: searchParams?.get('problemTypeId') || '',
    description: searchParams?.get('description') || '',
    problemImages: [],
    deliveryMethod: '',
    pickupAddress: '',
    deliveryAddress: '',
    customerName: '',
    customerPhone: '',
    customerNif: '',
    customerPostalCode: '',
    selectedShopId: '',
    customerLocation: ''
  })

  // Estados de estimativa
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null)
  const [estimatedTime, setEstimatedTime] = useState<number | null>(null)
  const [selectedShopDistance, setSelectedShopDistance] = useState<number | null>(null)

  // Funções de carregamento de dados
  const fetchInitialData = useCallback(async () => {
    try {
      const [categoriesRes, brandsRes, problemTypesRes] = await Promise.all([
        fetch('/api/admin/categories'),
        fetch('/api/admin/brands'),
        fetch('/api/problem-types')
      ])

      if (categoriesRes.ok) setCategories(await categoriesRes.json())
      if (brandsRes.ok) setBrands(await brandsRes.json())
      if (problemTypesRes.ok) setProblemTypes(await problemTypesRes.json())
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      setError('Erro ao carregar dados iniciais')
    }
  }, [])

  const fetchSavedAddresses = useCallback(async () => {
    if (!session?.user?.id) return

    try {
      const response = await fetch('/api/cliente/addresses')
      if (response.ok) {
        const data = await response.json()
        setSavedAddresses(data.addresses || [])

        // Se há endereço padrão, usar seu código postal
        const defaultAddress = data.addresses?.find((addr: any) => addr.isDefault)
        if (defaultAddress && !formData.customerPostalCode) {
          handleInputChange('customerPostalCode', defaultAddress.postalCode)
        }
      }
    } catch (error) {
      console.error('Erro ao buscar endereços salvos:', error)
    }
  }, [session?.user?.id, formData.customerPostalCode])

  const fetchDevices = useCallback(async () => {
    if (formData.categoryId && formData.brandId) {
      try {
        const response = await fetch(`/api/admin/device-models?categoryId=${formData.categoryId}&brandId=${formData.brandId}`)
        if (response.ok) {
          setDevices(await response.json())
        }
      } catch (error) {
        console.error('Erro ao carregar modelos:', error)
      }
    }
  }, [formData.categoryId, formData.brandId])

  // useEffect hooks
  useEffect(() => {
    fetchInitialData()
    fetchSavedAddresses()
  }, [fetchInitialData, fetchSavedAddresses])

  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        customerName: session.user.name || '',
        customerPhone: session.user.profile?.phone || ''
      }))
    }
  }, [session])

  useEffect(() => {
    if (formData.categoryId && formData.brandId) {
      fetchDevices()
    }
  }, [formData.categoryId, formData.brandId, fetchDevices])

  // Função para atualizar dados do formulário
  const handleInputChange = useCallback((field: keyof RepairFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Se selecionou uma loja, guardar a distância para validação de estafeta
    if (field === 'selectedShopId' && availableShops.length > 0) {
      const selectedShop = availableShops.find(shop => shop.id === value)
      setSelectedShopDistance(selectedShop?.distance || null)
    }
  }, [availableShops])

  // Função para avançar para o próximo passo
  const nextStep = useCallback(() => {
    if (currentStep < 5) {
      setCurrentStep(prev => prev + 1)
    }
  }, [currentStep])

  // Função para voltar ao passo anterior
  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1)
    }
  }, [currentStep])

  // Renderização condicional baseada no loading
  if (isLoading) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Carregando...</p>
          </div>
        </div>
      </ModernLayout>
    )
  }

  // Renderização de erro
  if (error) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong className="font-bold">Erro!</strong>
              <span className="block sm:inline"> {error}</span>
            </div>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </ModernLayout>
    )
  }

  // Passos do formulário
  const steps = [
    { id: 1, title: 'Dispositivo', icon: MapPin },
    { id: 2, title: 'Loja', icon: Store },
    { id: 3, title: 'Entrega', icon: Truck },
    { id: 4, title: 'Dados', icon: User },
    { id: 5, title: 'Pagamento', icon: CreditCard }
  ]

  return (
    <ModernLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-gray-900">Nova Reparação</h1>
            <Link href="/cliente/reparacoes" className="text-blue-600 hover:text-blue-800">
              <ArrowLeft className="w-5 h-5 inline mr-1" />
              Voltar
            </Link>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    isActive ? 'border-blue-600 bg-blue-600 text-white' :
                    isCompleted ? 'border-green-600 bg-green-600 text-white' :
                    'border-gray-300 bg-white text-gray-400'
                  }`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <span className={`ml-2 text-sm font-medium ${
                    isActive ? 'text-blue-600' :
                    isCompleted ? 'text-green-600' :
                    'text-gray-400'
                  }`}>
                    {step.title}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`mx-4 h-0.5 w-16 ${
                      isCompleted ? 'bg-green-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Conteúdo do passo atual */}
        <div className="bg-white rounded-lg shadow-md p-6">
          {currentStep === 1 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Selecione o Dispositivo</h2>
              <p className="text-gray-600 mb-6">Escolha a categoria, marca e modelo do seu dispositivo.</p>
              
              {/* Formulário do passo 1 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
                  <select
                    value={formData.categoryId}
                    onChange={(e) => handleInputChange('categoryId', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Selecione uma categoria</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Marca</label>
                  <select
                    value={formData.brandId}
                    onChange={(e) => handleInputChange('brandId', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    disabled={!formData.categoryId}
                  >
                    <option value="">Selecione uma marca</option>
                    {brands.map((brand) => (
                      <option key={brand.id} value={brand.id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Modelo</label>
                  <select
                    value={formData.deviceId}
                    onChange={(e) => handleInputChange('deviceId', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    disabled={!formData.brandId}
                  >
                    <option value="">Selecione um modelo</option>
                    {devices.map((device) => (
                      <option key={device.id} value={device.id}>
                        {device.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Navegação */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4 inline mr-1" />
              Anterior
            </button>

            <button
              onClick={nextStep}
              disabled={currentStep === 5}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Próximo
              <ArrowRight className="w-4 h-4 inline ml-1" />
            </button>
          </div>
        </div>
      </div>
    </ModernLayout>
  )
}

export default function NovaReparacaoPage() {
  return (
    <Suspense fallback={
      <ModernLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Carregando...</p>
          </div>
        </div>
      </ModernLayout>
    }>
      <NovaReparacaoPageContent />
    </Suspense>
  )
}
