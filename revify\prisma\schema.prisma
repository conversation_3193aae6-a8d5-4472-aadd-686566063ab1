generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                  String               @id @default(cuid())
  email               String               @unique
  name                String?
  password            String?
  role                UserRole             @default(CUSTOMER)
  isVerified          Boolean              @default(false)
  isFeatured          Boolean              @default(false)
  isSuperAdmin        Boolean              @default(false)
  resetToken          String?
  resetTokenExpiry    DateTime?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  accounts            Account[]
  addresses           Address[]
  appConfigs          AppConfig[]          @relation("AppConfigs")
  bankDetails         BankDetails?
  cartItems           CartItem[]
  coupons             Coupon[]             @relation("SellerCoupons")
  disputes            Dispute[]
  installedApps       InstalledApp[]       @relation("InstalledApps")
  lemarConversations  LemarConversation[]
  marketplaceProducts MarketplaceProduct[] @relation("MarketplaceSeller")
  messages            Message[]
  newsletterCampaigns NewsletterCampaign[] @relation("NewsletterCampaigns")
  newsletterLists     NewsletterList[]     @relation("NewsletterLists")
  newsletterTemplates NewsletterTemplate[] @relation("NewsletterTemplates")
  notifications       Notification[]
  orders              Order[]
  pendingAddons       PendingAddon[]       @relation("PendingAddons")
  pendingUpgrades     PendingUpgrade[]     @relation("PendingUpgrades")
  profile             Profile?
  repairShopPrices    RepairShopPrice[]    @relation("RepairShopPrices")
  repairs             Repair[]
  repairShopRepairs   Repair[]             @relation("RepairShopRepairs")
  reviews             Review[]
  shopReviews         Review[]             @relation("ShopReviews")
  sessions            Session[]
  shopCategories      ShopCategory[]
  sparePartOrders     SparePartOrder[]     @relation("SparePartOrders")
  subscription        Subscription?
  taxCalculations     TaxCalculation[]     @relation("TaxCalculations")
  transactions        Transaction[]
  settings            UserSettings?        @relation("UserSettings")
  whatsappMessages    WhatsAppMessage[]    @relation("WhatsAppMessages")
  customPages         CustomPage[]
  referralCode        ReferralCode?
  referrals           Referral[]           @relation("UserReferrals")
  referredBy          Referral[]           @relation("UserReferred")
  balance             UserBalance?
  // Relações para sistema de empregados
  employees           Employee[]           @relation("RepairShopEmployees") // Empregados da loja (se for REPAIR_SHOP)
  employeeOf          Employee?            @relation("EmployeeUser") // Dados de empregado (se for EMPLOYEE)

  @@map("users")
}

model Profile {
  id                    String      @id @default(cuid())
  userId                String      @unique
  phone                 String?
  avatar                String?
  dateOfBirth           DateTime?
  nif                   String?
  companyName           String?
  companyNif            String?
  website               String?
  description           String?
  logo                  String?
  address               String?
  street                String?
  city                  String?
  postalCode            String?
  district              String?
  country               String?     @default("Portugal")
  latitude              Float?
  longitude             Float?
  companyType           String?
  legalDocument         String?
  workingBrands         String[]
  workingCategories     String[]
  workingProblems       String[]
  serviceRadius         Int?
  averageRepairTime     Int?
  shippingEnabled       Boolean     @default(false)
  freeShippingThreshold Decimal?
  freeShippingCountries String[]    @default([])
  shippingRates         Json?
  customDomain          String?
  customSubdomain       String?
  ifthenPayEntityId     String?
  ifthenPaySubEntityId  String?
  ifthenPayApiKey       String?
  monthlyRepairs        Int?
  expectedGrowth        Int?
  businessHours         Json?
  customerName          String?
  customerNif           String?
  addresses             Json?
  emailNotifications    Boolean     @default(true)
  smsNotifications      Boolean     @default(false)
  createdAt             DateTime    @default(now())
  updatedAt             DateTime    @updatedAt
  user                  User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  shopConfig            ShopConfig?

  @@map("profiles")
}

model ShopConfig {
  id                    String   @id @default(cuid())
  profileId             String   @unique
  aboutUs               String?
  contactInfo           String?
  homePageText          String?
  stripePublicKey       String?
  stripeSecretKey       String?
  shippingRates         Json?
  marketingText         String?
  customCss             String?
  logoUrl               String?
  bannerUrl             String?
  primaryColor          String   @default("#000000")
  secondaryColor        String   @default("#666666")

  // Configurações de envio
  freeShippingThreshold Float?   @default(50)
  defaultShippingRate   Float?   @default(5)
  shippingByCountry     Json?    @default("[]")

  // Configurações de impostos
  taxEnabled            Boolean  @default(false)
  taxIncluded           Boolean  @default(true)
  defaultTaxRate        Float?   @default(23)
  taxByCountry          Json?    @default("[]")

  // Configurações de categorias
  showDefaultCategories Boolean  @default(true)

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  profile               Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("shop_configs")
}

model ShopCategory {
  id          String   @id @default(cuid())
  shopId      String
  name        String
  description String?
  icon        String?
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  shop        User     @relation(fields: [shopId], references: [id], onDelete: Cascade)

  @@unique([shopId, name])
  @@map("shop_categories")
}

model LemarConversation {
  id        String   @id @default(cuid())
  userId    String?
  sessionId String
  messages  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([sessionId])
  @@map("lemar_conversations")
}

model Address {
  id          String   @id @default(cuid())
  userId      String
  name        String
  street      String
  city        String
  postalCode  String
  country     String   @default("Portugal")
  isDefault   Boolean  @default(false)
  coordinates String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Brand {
  id                  String               @id @default(cuid())
  name                String               @unique
  logo                String?
  isActive            Boolean              @default(true)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  deviceModels        DeviceModel[]
  marketplaceProducts MarketplaceProduct[] @relation("MarketplaceProducts")
  parts               Part[]
  spareParts          SparePart[]          @relation("SparePartBrand")

  @@map("brands")
}

model Category {
  id                  String               @id @default(cuid())
  name                String               @unique
  description         String?
  isActive            Boolean              @default(true)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  basePrices          BasePrice[]
  deviceModels        DeviceModel[]
  marketplaceProducts MarketplaceProduct[] @relation("MarketplaceProducts")
  repairShopPrices    RepairShopPrice[]
  spareParts          SparePart[]          @relation("SparePartCategory")

  @@map("categories")
}

model ProblemType {
  id               String            @id @default(cuid())
  name             String            @unique
  description      String?
  icon             String?
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  basePrices       BasePrice[]
  repairShopPrices RepairShopPrice[]
  repairs          Repair[]

  @@map("problem_types")
}

model BasePrice {
  id            String      @id @default(cuid())
  categoryId    String
  problemTypeId String
  basePrice     Float
  estimatedTime Int
  isActive      Boolean     @default(true)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  category      Category    @relation(fields: [categoryId], references: [id])
  problemType   ProblemType @relation(fields: [problemTypeId], references: [id])

  @@unique([categoryId, problemTypeId])
  @@map("base_prices")
}

model RepairShopPrice {
  id            String       @id @default(cuid())
  repairShopId  String
  deviceModelId String?
  categoryId    String?
  problemTypeId String
  price         Float
  estimatedTime Int
  isActive      Boolean      @default(true)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  category      Category?    @relation(fields: [categoryId], references: [id])
  deviceModel   DeviceModel? @relation(fields: [deviceModelId], references: [id])
  problemType   ProblemType  @relation(fields: [problemTypeId], references: [id])
  repairShop    User         @relation("RepairShopPrices", fields: [repairShopId], references: [id])

  @@unique([repairShopId, deviceModelId, problemTypeId])
  @@unique([repairShopId, categoryId, problemTypeId])
  @@map("repair_shop_prices")
}

model DeviceModel {
  id                  String               @id @default(cuid())
  brandId             String
  categoryId          String
  name                String
  releaseYear         Int?
  image               String?
  isActive            Boolean              @default(true)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  specifications      Json?
  brand               Brand                @relation(fields: [brandId], references: [id])
  category            Category             @relation(fields: [categoryId], references: [id])
  marketplaceProducts MarketplaceProduct[] @relation("MarketplaceProducts")
  parts               Part[]
  repairShopPrices    RepairShopPrice[]
  repairs             Repair[]
  spareParts          SparePart[]          @relation("SparePartModel")

  @@unique([brandId, name])
  @@map("device_models")
}

model Repair {
  id                     String        @id @default(cuid())
  customerId             String
  repairShopId           String?
  assignedEmployeeId     String?       // ID do empregado responsável pela reparação
  deviceModelId          String
  problemTypeId          String?
  description            String
  aiDiagnosis            Json?         // Diagnóstico da IA (original + resultado)
  images                 String[]
  status                 RepairStatus  @default(PENDING_PAYMENT)
  customerName           String
  customerPhone          String
  customerNif            String?
  deliveryMethod         String
  pickupAddress          String?
  deliveryAddress        String?
  trackingCode           String?
  pickupTrackingNumber   String?
  deliveryTrackingNumber String?
  estimatedPrice         Decimal?
  finalPrice             Decimal?
  stripeSessionId        String?
  stripePaymentIntentId  String?
  invoiceNumber          String?
  invoiceIssued          Boolean       @default(false)
  invoiceIssuedAt        DateTime?
  atcud                  String?
  qrCode                 String?
  moloniInvoiceId        String?
  scheduledDate          DateTime?
  completedDate          DateTime?
  deliveryDate           DateTime?
  paidAt                 DateTime?
  confirmedAt            DateTime?
  createdAt              DateTime      @default(now())
  updatedAt              DateTime      @updatedAt
  dispute                Dispute?
  messages               Message[]
  payments               Payment[]
  repairItems            RepairItem[]
  customer               User          @relation(fields: [customerId], references: [id])
  deviceModel            DeviceModel   @relation(fields: [deviceModelId], references: [id])
  problemType            ProblemType?  @relation(fields: [problemTypeId], references: [id])
  repairShop             User?         @relation("RepairShopRepairs", fields: [repairShopId], references: [id])
  assignedEmployee       Employee?     @relation("EmployeeRepairs", fields: [assignedEmployeeId], references: [id])
  review                 Review?
  transactions           Transaction[]
  statusHistory          RepairStatusHistory[] @relation("RepairStatusHistory")

  @@map("repairs")
}

model RepairItem {
  id          String   @id @default(cuid())
  repairId    String
  partId      String?
  name        String
  description String?
  quantity    Int      @default(1)
  price       Decimal
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  part        Part?    @relation(fields: [partId], references: [id])
  repair      Repair   @relation(fields: [repairId], references: [id], onDelete: Cascade)

  @@map("repair_items")
}

model Employee {
  id                String              @id @default(cuid())
  userId            String              @unique // Referência ao User com role EMPLOYEE
  repairShopId      String              // Loja à qual pertence
  name              String
  email             String              @unique
  phone             String?
  position          String?             // Cargo/função
  isActive          Boolean             @default(true)
  permissions       Json                @default("{}") // Permissões em JSON
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  // Relações
  user              User                @relation("EmployeeUser", fields: [userId], references: [id], onDelete: Cascade)
  repairShop        User                @relation("RepairShopEmployees", fields: [repairShopId], references: [id], onDelete: Cascade)
  assignedRepairs   Repair[]            @relation("EmployeeRepairs")
  statusUpdates     RepairStatusHistory[] @relation("EmployeeStatusUpdates")

  @@map("employees")
}

model RepairStatusHistory {
  id          String    @id @default(cuid())
  repairId    String
  status      RepairStatus
  notes       String?
  updatedBy   String?   // ID do usuário que fez a alteração (lojista ou empregado)
  updatedByType String? // "REPAIR_SHOP" ou "EMPLOYEE"
  employeeId  String?   // ID do empregado se foi ele que fez a alteração
  createdAt   DateTime  @default(now())
  // Relações
  repair      Repair    @relation("RepairStatusHistory", fields: [repairId], references: [id], onDelete: Cascade)
  employee    Employee? @relation("EmployeeStatusUpdates", fields: [employeeId], references: [id])

  @@map("repair_status_history")
}

model Part {
  id          String       @id @default(cuid())
  brandId     String
  deviceId    String?
  name        String
  description String?
  category    String
  sku         String       @unique
  price       Decimal
  stock       Int          @default(0)
  images      String[]
  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  orderItems  OrderItem[]
  brand       Brand        @relation(fields: [brandId], references: [id])
  device      DeviceModel? @relation(fields: [deviceId], references: [id])
  repairItems RepairItem[]

  @@map("parts")
}

model Order {
  id                    String                 @id @default(cuid())
  customerId            String
  status                OrderStatus            @default(PENDING)
  paymentMethod         String?
  total                 Decimal
  subtotal              Decimal?
  tax                   Decimal?
  couponId              String?
  couponCode            String?
  discountValue         Decimal?
  trackingNumber        String?
  invoiceNumber         String?
  invoiceIssued         Boolean                @default(false)
  invoiceIssuedAt       DateTime?
  shippingAddress       String?
  shippingName          String
  shippingStreet        String
  shippingCity          String
  shippingPostalCode    String
  shippingCountry       String                 @default("Portugal")
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  dispute               Dispute?
  marketplaceOrderItems MarketplaceOrderItem[] @relation("MarketplaceOrderItems")
  orderItems            OrderItem[]
  coupon                Coupon?                @relation("OrderCoupons", fields: [couponId], references: [id])
  customer              User                   @relation(fields: [customerId], references: [id])
  payments              Payment[]
  transactions          Transaction[]

  @@map("orders")
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  partId    String
  quantity  Int
  price     Decimal
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  part      Part     @relation(fields: [partId], references: [id])

  @@map("order_items")
}

model Payment {
  id                    String        @id @default(cuid())
  amount                Decimal
  currency              String        @default("eur")
  status                PaymentStatus @default(PENDING)
  method                String        @default("stripe")
  paymentMethodType     String?       // 'card', 'multibanco', 'klarna'
  paymentMethodDetails  Json?         // Detalhes específicos do método (últimos 4 dígitos cartão, etc.)
  platformFee           Decimal?
  shopAmount            Decimal?
  escrowReleaseDate     DateTime?
  releasedAt            DateTime?
  stripePaymentIntentId String?
  stripeInvoiceId       String?       // ID da fatura Stripe (para subscrições)
  stripeCustomerId      String?       // ID do cliente Stripe
  failureReason         String?       // Razão da falha se status = FAILED
  repairId              String?
  orderId               String?
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt
  order                 Order?        @relation(fields: [orderId], references: [id])
  repair                Repair?       @relation(fields: [repairId], references: [id])

  @@map("payments")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  metadata  Json?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Message {
  id        String   @id @default(cuid())
  repairId  String
  senderId  String
  message   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  repair    Repair   @relation(fields: [repairId], references: [id], onDelete: Cascade)
  sender    User     @relation(fields: [senderId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Review {
  id             String    @id @default(cuid())
  repairId       String    @unique
  customerId     String
  repairShopId   String?
  rating         Int
  comment        String?
  shopResponse   String?
  shopResponseAt DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  customer       User      @relation(fields: [customerId], references: [id], onDelete: Cascade)
  repair         Repair    @relation(fields: [repairId], references: [id], onDelete: Cascade)
  repairShop     User?     @relation("ShopReviews", fields: [repairShopId], references: [id], onDelete: Cascade)

  @@map("reviews")
}

model Dispute {
  id             String        @id @default(cuid())
  repairId       String?       @unique
  orderId        String?       @unique
  customerId     String
  reason         String
  description    String
  status         DisputeStatus @default(OPEN)
  resolution     String?
  shopResponse   String?
  shopResponseAt DateTime?
  refundAmount   Decimal?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  customer       User          @relation(fields: [customerId], references: [id], onDelete: Cascade)
  order          Order?        @relation(fields: [orderId], references: [id], onDelete: Cascade)
  repair         Repair?       @relation(fields: [repairId], references: [id], onDelete: Cascade)

  @@map("disputes")
}

model MarketplaceProduct {
  id            String                 @id @default(cuid())
  sellerId      String
  categoryId    String?
  brandId       String?
  deviceModelId String?
  name          String
  description   String
  price         Decimal
  originalPrice Decimal?
  condition     ProductCondition
  images        String[]
  isActive      Boolean                @default(true)
  stock         Int                    @default(1)
  rating        Decimal?               @default(0)
  reviewCount   Int                    @default(0)
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt
  cartItems     CartItem[]
  orderItems    MarketplaceOrderItem[]
  brand         Brand?                 @relation("MarketplaceProducts", fields: [brandId], references: [id])
  category      Category?              @relation("MarketplaceProducts", fields: [categoryId], references: [id])
  deviceModel   DeviceModel?           @relation("MarketplaceProducts", fields: [deviceModelId], references: [id])
  seller        User                   @relation("MarketplaceSeller", fields: [sellerId], references: [id], onDelete: Cascade)

  @@map("marketplace_products")
}

model Coupon {
  id               String     @id @default(cuid())
  sellerId         String
  code             String     @unique
  name             String
  description      String?
  type             CouponType
  value            Decimal
  minOrderValue    Decimal?
  maxOrderValue    Decimal?
  maxDiscountValue Decimal?
  allowedCountries String[]
  usageLimit       Int?
  usageCount       Int        @default(0)
  userUsageLimit   Int?
  startsAt         DateTime?
  expiresAt        DateTime?
  isActive         Boolean    @default(true)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt
  seller           User       @relation("SellerCoupons", fields: [sellerId], references: [id], onDelete: Cascade)
  orders           Order[]    @relation("OrderCoupons")

  @@map("coupons")
}

model CartItem {
  id        String             @id @default(cuid())
  userId    String
  productId String
  quantity  Int                @default(1)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  product   MarketplaceProduct @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

model MarketplaceOrderItem {
  id        String             @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  order     Order              @relation("MarketplaceOrderItems", fields: [orderId], references: [id], onDelete: Cascade)
  product   MarketplaceProduct @relation(fields: [productId], references: [id])

  @@map("marketplace_order_items")
}

model SystemSettings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}

model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  status      TransactionStatus @default(PENDING)
  amount      Decimal           @db.Decimal(10, 2)
  commission  Decimal?          @db.Decimal(10, 2)
  netAmount   Decimal           @db.Decimal(10, 2)
  description String
  orderId     String?
  repairId    String?
  payoutDate  DateTime?
  paidAt      DateTime?
  metadata    Json?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  order       Order?            @relation(fields: [orderId], references: [id])
  repair      Repair?           @relation(fields: [repairId], references: [id])
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

model BankDetails {
  id          String   @id @default(cuid())
  userId      String   @unique
  iban        String
  accountName String
  bankName    String?
  verified    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("bank_details")
}

model SparePart {
  id             String               @id @default(cuid())
  name           String
  description    String?
  sku            String               @unique
  price          Decimal              @db.Decimal(10, 2)
  stock          Int                  @default(0)
  images         String[]
  categoryId     String
  brandId        String?
  deviceModelId  String?
  availability   Boolean              @default(true)
  deliveryTime   Int                  @default(3)
  specifications Json?
  compatibility  String[]
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  orderItems     SparePartOrderItem[]
  brand          Brand?               @relation("SparePartBrand", fields: [brandId], references: [id])
  category       Category             @relation("SparePartCategory", fields: [categoryId], references: [id])
  deviceModel    DeviceModel?         @relation("SparePartModel", fields: [deviceModelId], references: [id])

  @@map("spare_parts")
}

model SparePartOrder {
  id                 String               @id @default(cuid())
  orderNumber        String               @unique
  repairShopId       String
  status             SparePartOrderStatus @default(PENDING)
  total              Decimal              @db.Decimal(10, 2)
  shippingCost       Decimal              @default(0) @db.Decimal(10, 2)
  shippingName       String
  shippingStreet     String
  shippingCity       String
  shippingPostalCode String
  shippingCountry    String               @default("Portugal")
  paymentMethod      String?
  paymentStatus      PaymentStatus        @default(PENDING)
  paidAt             DateTime?
  trackingCode       String?
  estimatedDelivery  DateTime?
  deliveredAt        DateTime?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  comments           OrderComment[]
  items              SparePartOrderItem[]
  repairShop         User                 @relation("SparePartOrders", fields: [repairShopId], references: [id])

  @@map("spare_part_orders")
}

model SparePartOrderItem {
  id        String         @id @default(cuid())
  orderId   String
  partId    String
  quantity  Int
  price     Decimal        @db.Decimal(10, 2)
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  order     SparePartOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  part      SparePart      @relation(fields: [partId], references: [id])

  @@map("spare_part_order_items")
}

model OrderComment {
  id         String         @id @default(cuid())
  orderId    String
  text       String
  author     String
  authorId   String
  authorRole String
  isInternal Boolean        @default(false)
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  order      SparePartOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_comments")
}

model UserSettings {
  id          String   @id @default(cuid())
  userId      String   @unique
  preferences Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation("UserSettings", fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}

model CustomPage {
  id              String   @id @default(cuid())
  userId          String
  slug            String
  title           String
  content         String   @db.Text
  metaDescription String?
  isActive        Boolean  @default(true)
  showInHeader    Boolean  @default(false)
  showInFooter    Boolean  @default(false)
  headerOrder     Int?
  footerOrder     Int?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, slug])
  @@map("custom_pages")
}

model InstalledApp {
  id                  String    @id @default(cuid())
  userId              String
  appId               String
  isActive            Boolean   @default(true)
  installedAt         DateTime  @default(now())
  uninstalledAt       DateTime?
  isTrialActive       Boolean   @default(false)
  trialStartDate      DateTime?
  trialEndDate        DateTime?
  isPaid              Boolean   @default(false)
  pendingBilling      Boolean   @default(false)
  addonSubscriptionId String?
  nextBillingDate     DateTime?
  user                User      @relation("InstalledApps", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, appId])
  @@map("installed_apps")
}

model PendingAddon {
  id              String   @id @default(cuid())
  userId          String
  appId           String
  monthlyPrice    Decimal  @db.Decimal(10, 2)
  addedAt         DateTime @default(now())
  nextBillingDate DateTime
  processed       Boolean  @default(false)
  user            User     @relation("PendingAddons", fields: [userId], references: [id], onDelete: Cascade)

  @@map("pending_addons")
}

model WhatsAppMessage {
  id         String   @id @default(cuid())
  messageId  String   @unique
  from       String
  to         String
  type       String
  content    String?
  timestamp  DateTime
  customerId String?
  direction  String
  status     String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  customer   User?    @relation("WhatsAppMessages", fields: [customerId], references: [id])

  @@map("whatsapp_messages")
}

model ElectronicInvoice {
  id            String    @id @default(cuid())
  invoiceNumber String    @unique
  atcud         String
  hashControl   String
  qrCode        String
  invoiceData   Json
  status        String
  environment   String
  submittedAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("electronic_invoices")
}

model AppConfig {
  id        String   @id @default(cuid())
  userId    String
  appId     String
  settings  Json     @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation("AppConfigs", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, appId])
  @@map("app_configs")
}

model AppDefinition {
  id             String   @id @default(cuid())
  appId          String   @unique
  name           String
  description    String?
  category       String
  isPaid         Boolean  @default(false)
  monthlyPrice   Decimal  @default(0)
  hasTrialPeriod Boolean  @default(false)
  trialDays      Int      @default(30)
  features       Json     @default("[]")
  requiredPlans  Json     @default("[]")
  isActive       Boolean  @default(true)
  isPopular      Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("app_definitions")
}

model TaxCalculation {
  id          String   @id @default(cuid())
  userId      String
  name        String
  baseAmount  Decimal
  vatRate     Decimal
  vatAmount   Decimal
  totalAmount Decimal
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation("TaxCalculations", fields: [userId], references: [id], onDelete: Cascade)

  @@map("tax_calculations")
}

model PendingUpgrade {
  id            String       @id @default(cuid())
  userId        String
  currentPlanId String?
  newPlanId     String
  billingCycle  BillingCycle
  amount        Decimal
  currency      String       @default("EUR")
  status        String       @default("PENDING")
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  user          User         @relation("PendingUpgrades", fields: [userId], references: [id], onDelete: Cascade)

  @@map("pending_upgrades")
}

model NewsletterTemplate {
  id        String               @id @default(cuid())
  userId    String
  name      String
  subject   String
  content   String
  isActive  Boolean              @default(true)
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt
  campaigns NewsletterCampaign[]
  user      User                 @relation("NewsletterTemplates", fields: [userId], references: [id], onDelete: Cascade)

  @@map("newsletter_templates")
}

model NewsletterList {
  id          String                 @id @default(cuid())
  userId      String
  name        String
  description String                 @default("")
  createdAt   DateTime               @default(now())
  updatedAt   DateTime               @updatedAt
  campaigns   NewsletterCampaign[]
  user        User                   @relation("NewsletterLists", fields: [userId], references: [id], onDelete: Cascade)
  subscribers NewsletterSubscriber[]

  @@map("newsletter_lists")
}

model NewsletterSubscriber {
  id             String         @id @default(cuid())
  listId         String
  email          String
  name           String         @default("")
  isActive       Boolean        @default(true)
  subscribedAt   DateTime       @default(now())
  unsubscribedAt DateTime?
  list           NewsletterList @relation(fields: [listId], references: [id], onDelete: Cascade)

  @@unique([listId, email])
  @@map("newsletter_subscribers")
}

model NewsletterCampaign {
  id            String             @id @default(cuid())
  userId        String
  name          String
  subject       String
  templateId    String
  listId        String?
  recipientType String             @default("list")
  customerIds   Json               @default("[]")
  status        String             @default("DRAFT")
  sentAt        DateTime?
  openRate      Decimal?
  clickRate     Decimal?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  list          NewsletterList?    @relation(fields: [listId], references: [id])
  template      NewsletterTemplate @relation(fields: [templateId], references: [id])
  user          User               @relation("NewsletterCampaigns", fields: [userId], references: [id], onDelete: Cascade)

  @@map("newsletter_campaigns")
}

model SubscriptionPlan {
  id                         String         @id @default(cuid())
  name                       String
  description                String?
  monthlyPrice               Decimal
  yearlyPrice                Decimal
  stripePriceId              String?        // ID do preço no Stripe
  features                   Json
  maxProducts                Int?
  maxRepairs                 Int?
  marketplaceCommission      Decimal        @default(5.0)
  repairCommission           Decimal        @default(5.0)
  smsAccess                  Boolean        @default(false)
  whatsappAccess             Boolean        @default(false)
  emailSupport               Boolean        @default(true)
  paymentDelayDays           Int            @default(7)
  sparePartsDiscount         Decimal        @default(0.0)
  recommendedProductsEnabled Boolean        @default(false)
  recommendedProductsDays    Int            @default(0)
  certifiedBadge             Boolean        @default(false)
  priority                   Int            @default(0)
  moloniIntegration          Boolean        @default(false)
  miniStore                  Boolean        @default(false)
  individualRepairs          Boolean        @default(false)
  availableApps              Json           @default("[]")
  isActive                   Boolean        @default(true)
  isPopular                  Boolean        @default(false)
  createdAt                  DateTime       @default(now())
  updatedAt                  DateTime       @updatedAt
  subscriptions              Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id                   String                @id @default(cuid())
  userId               String                @unique
  planId               String
  status               SubscriptionStatus    @default(ACTIVE)
  billingCycle         BillingCycle          @default(MONTHLY)
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  stripeSubscriptionId String?
  stripeCustomerId     String?
  cancelAtPeriodEnd    Boolean               @default(false)
  canceledAt           DateTime?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  payments             SubscriptionPayment[]
  plan                 SubscriptionPlan      @relation(fields: [planId], references: [id])
  user                 User                  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model SubscriptionPayment {
  id                    String        @id @default(cuid())
  subscriptionId        String
  amount                Decimal
  currency              String        @default("EUR")
  status                PaymentStatus
  paymentMethodType     String?       // 'card', 'multibanco', 'klarna'
  paymentMethodDetails  Json?         // Detalhes do método de pagamento
  stripePaymentIntentId String?
  stripeInvoiceId       String?
  stripeCustomerId      String?
  multibancoEntity      String?
  multibancoReference   String?
  failureReason         String?       // Razão da falha se aplicável
  paidAt                DateTime?
  periodStart           DateTime
  periodEnd             DateTime
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt
  subscription          Subscription  @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("subscription_payments")
}

model MultibancoReference {
  id                    String           @id @default(cuid())
  orderId               String           @unique
  entity                String
  reference             String           @unique
  amount                Int
  customerEmail         String?
  status                MultibancoStatus @default(PENDING)
  expiryDate            DateTime
  paidAt                DateTime?
  transactionId         String?
  stripePaymentIntentId String?          // Stripe Payment Intent ID for tracking
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt

  @@map("multibanco_references")
}

model ReferralCode {
  id           String   @id @default(cuid())
  userId       String   @unique
  code         String   @unique
  uses         Int      @default(0)
  maxUses      Int?
  rewardAmount Decimal  @db.Decimal(10, 2) @default(25.00)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  referrals    Referral[]

  @@map("referral_codes")
}

model Referral {
  id              String        @id @default(cuid())
  referralCodeId  String
  referrerId      String        // Quem convidou
  referredId      String        // Quem foi convidado
  status          ReferralStatus @default(PENDING)
  referrerReward  Decimal       @db.Decimal(10, 2) @default(25.00)
  referredReward  Decimal       @db.Decimal(10, 2) @default(25.00)
  referrerPaid    Boolean       @default(false)
  referredPaid    Boolean       @default(false)
  completedAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  referralCode    ReferralCode  @relation(fields: [referralCodeId], references: [id])
  referrer        User          @relation("UserReferrals", fields: [referrerId], references: [id])
  referred        User          @relation("UserReferred", fields: [referredId], references: [id])

  @@unique([referrerId, referredId])
  @@map("referrals")
}

model UserBalance {
  id              String   @id @default(cuid())
  userId          String   @unique
  availableAmount Decimal  @db.Decimal(10, 2) @default(0.00)
  pendingAmount   Decimal  @db.Decimal(10, 2) @default(0.00)
  totalEarned     Decimal  @db.Decimal(10, 2) @default(0.00)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_balances")
}

model PaymentHistory {
  id                    String              @id @default(cuid())
  stripePaymentIntentId String?             @unique
  stripeInvoiceId       String?
  stripeCustomerId      String?
  amount                Decimal             @db.Decimal(10, 2)
  currency              String              @default("EUR")
  status                PaymentHistoryStatus
  paymentType           PaymentType         // 'subscription', 'marketplace', 'repair'
  paymentMethodType     String?             // 'card', 'multibanco', 'klarna'
  paymentMethodDetails  Json?               // Detalhes do método (brand, last4, etc.)
  description           String?
  customerEmail         String?
  customerName          String?
  lojistId              String?             // ID do lojista (se aplicável)
  lojistName            String?             // Nome do lojista
  subscriptionId        String?             // ID da subscrição (se aplicável)
  orderId               String?             // ID da encomenda (se aplicável)
  repairId              String?             // ID da reparação (se aplicável)
  platformFee           Decimal?            @db.Decimal(10, 2)
  netAmount             Decimal?            @db.Decimal(10, 2)
  failureReason         String?             // Razão da falha
  webhookProcessedAt    DateTime?           // Quando foi processado pelo webhook
  reconciledAt          DateTime?           // Quando foi reconciliado (se aplicável)
  metadata              Json?               // Metadados adicionais
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  @@index([status])
  @@index([paymentType])
  @@index([paymentMethodType])
  @@index([lojistId])
  @@index([createdAt])
  @@map("payment_history")
}

enum ReferralStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum UserRole {
  ADMIN
  CUSTOMER
  REPAIR_SHOP
  COURIER
  EMPLOYEE
}

enum DeviceType {
  SMARTPHONE
  TABLET
  LAPTOP
  DESKTOP
  SMARTWATCH
  HEADPHONES
  GAMING_CONSOLE
  OTHER
}

enum RepairStatus {
  PENDING_PAYMENT
  PAYMENT_FAILED
  PAID
  CONFIRMED
  RECEIVED
  DIAGNOSIS
  WAITING_PARTS
  IN_REPAIR
  TESTING
  COMPLETED
  DELIVERED
  CANCELLED
  DISPUTE
}

enum OrderStatus {
  PENDING
  PENDING_PAYMENT
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  ESCROW
  RELEASED
}

enum TransactionType {
  MARKETPLACE_SALE
  REPAIR_PAYMENT
  PARTS_PURCHASE
  COMMISSION
  PAYOUT
  REFUND
  REFERRAL_BONUS
  REFERRAL_REWARD
}

enum TransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum NotificationType {
  REPAIR_UPDATE
  ORDER_UPDATE
  PAYMENT_UPDATE
  SYSTEM_NOTIFICATION
  MARKETING
  DISPUTE_OPENED
  REVIEW_RECEIVED
  PRICE_UPDATE
}

enum DisputeStatus {
  OPEN
  IN_REVIEW
  RESOLVED
  ESCALATED
  CLOSED
}

enum ProductCondition {
  NEW
  LIKE_NEW
  GOOD
  FAIR
  POOR
}

enum CouponType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum SparePartOrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum SubscriptionStatus {
  ACTIVE
  PAST_DUE
  CANCELED
  INCOMPLETE
  INCOMPLETE_EXPIRED
  TRIALING
  UNPAID
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

enum MultibancoStatus {
  PENDING
  PAID
  EXPIRED
  CANCELLED
}

enum PaymentHistoryStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum PaymentType {
  SUBSCRIPTION
  MARKETPLACE
  REPAIR
  ADDON
  UPGRADE
}

model SystemConfig {
  id                  String   @id @default(cuid())
  platformName        String   @default("Reparia")
  platformLogo        String?
  platformIcon        String?
  awsAccessKeyId      String?
  awsSecretAccessKey  String?
  awsS3Bucket         String?
  awsRegion           String   @default("eu-west-1")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@map("system_config")
}
