'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Search, Clock, CheckCircle, AlertCircle, Package, Edit, Eye } from 'lucide-react'

interface Repair {
  id: string
  status: string
  description: string
  estimatedPrice?: string | number
  finalPrice?: string | number
  createdAt: string
  scheduledDate?: string
  isIndependent?: boolean
  trackingCode?: string
  customerName?: string
  deviceBrand?: string
  deviceModel: {
    name: string
    brand: {
      name: string
    }
    category: {
      name: string
    }
  }
  customer: {
    name: string
    email: string
  }
}

const STATUS_CONFIG = {
  CONFIRMED: { label: 'Confirmado', color: 'bg-indigo-50 text-indigo-700', icon: CheckCircle },
  RECEIVED: { label: 'Recebido', color: 'bg-blue-100 text-blue-800', icon: Package },
  DIAGNOSIS: { label: 'Diagnóstico', color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
  WAITING_PARTS: { label: 'Aguarda Peças', color: 'bg-orange-100 text-orange-800', icon: Clock },
  IN_REPAIR: { label: 'Em Reparação', color: 'bg-purple-100 text-purple-800', icon: Clock },
  TESTING: { label: 'Teste', color: 'bg-indigo-100 text-indigo-800', icon: Clock },
  COMPLETED: { label: 'Concluído', color: 'bg-indigo-50 text-indigo-700', icon: CheckCircle },
  DELIVERED: { label: 'Entregue', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
  CANCELLED: { label: 'Cancelado', color: 'bg-red-100 text-red-800', icon: AlertCircle }
}



export default function LojistaReparacoesPage() {
  const [repairs, setRepairs] = useState<Repair[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    fetchRepairs()
  }, [])

  const fetchRepairs = async () => {
    try {
      const response = await fetch('/api/lojista/repairs')
      if (response.ok) {
        const data = await response.json()
        setRepairs(data.repairs)
      }
    } catch (error) {
      console.error('Erro ao carregar reparações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredRepairs = repairs.filter(repair => {
    const matchesSearch =
      repair.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repair.deviceModel?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repair.deviceModel?.brand?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repair.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repair.description?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = !statusFilter || repair.status === statusFilter

    return matchesSearch && matchesStatus
  })



  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/lojista" className="text-2xl font-bold text-black">Revify</Link>
              <span className="ml-4 text-sm text-gray-500">Gestão de Reparações</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Recebidas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {repairs.filter(r => r.status === 'RECEIVED').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Em Progresso</p>
                <p className="text-2xl font-bold text-gray-900">
                  {repairs.filter(r => ['DIAGNOSIS', 'WAITING_PARTS', 'IN_REPAIR', 'TESTING'].includes(r.status)).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Concluídas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {repairs.filter(r => r.status === 'COMPLETED').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Canceladas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {repairs.filter(r => r.status === 'CANCELLED').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-600" />
              </div>
              <input
                type="text"
                placeholder="Pesquisar reparações..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent placeholder:text-gray-500"
              />
            </div>
          </div>
          
          <div className="lg:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent text-gray-900 bg-white"
            >
              <option value="">Todos os estados</option>
              {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                <option key={status} value={status}>
                  {config.label}
                </option>
              ))}
            </select>
          </div>


        </div>

        {/* Repairs Table */}
        {filteredRepairs.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-700 text-lg mb-4 font-medium">
              {searchTerm || statusFilter ? 'Nenhuma reparação encontrada' : 'Ainda não tem reparações'}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reparação
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dispositivo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pagamento
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Custo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRepairs.map((repair) => {
                    const statusConfig = STATUS_CONFIG[repair.status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.CONFIRMED
                    const StatusIcon = statusConfig.icon

                    return (
                      <tr key={repair.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium text-gray-900">
                                {repair.trackingCode || `#${repair.id.slice(-8)}`}
                              </span>
                              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                repair.isIndependent
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {repair.isIndependent ? 'Independente' : 'Revify'}
                              </span>
                            </div>
                            <div className="text-sm text-gray-500">
                              {new Date(repair.createdAt).toLocaleDateString('pt-PT')}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {repair.isIndependent ? repair.customerName : repair.customer.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {repair.isIndependent ? 'Cliente walk-in' : repair.customer.email}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {repair.isIndependent
                                ? `${repair.deviceBrand} ${repair.deviceModel?.name || 'N/A'}`
                                : `${repair.deviceModel?.brand?.name} ${repair.deviceModel?.name}`
                              }
                            </div>
                            <div className="text-sm text-gray-500">
                              {repair.isIndependent
                                ? 'Reparação independente'
                                : repair.deviceModel?.category?.name
                              }
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color}`}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {statusConfig.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {repair.status === 'CONFIRMED' ? (
                            <div className="flex items-center space-x-1">
                              <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                              <span className="text-xs text-gray-600">Verificar pagamento</span>
                            </div>
                          ) : repair.status === 'PENDING_PAYMENT' ? (
                            <div className="flex items-center space-x-1">
                              <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                              <span className="text-xs text-gray-600">Aguarda pagamento</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-1">
                              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                              <span className="text-xs text-gray-600">Pago</span>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {repair.finalPrice && Number(repair.finalPrice) > 0 ? (
                            <span className="font-medium">€{Number(repair.finalPrice).toFixed(2)}</span>
                          ) : repair.estimatedPrice && Number(repair.estimatedPrice) > 0 ? (
                            <span className="text-gray-600">~€{Number(repair.estimatedPrice).toFixed(2)}</span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Link
                              href={`/lojista/reparacoes/${repair.id}`}
                              className="text-green-600 hover:text-green-900"
                            >
                              <Eye className="w-4 h-4" />
                            </Link>
                            <Link
                              href={`/lojista/reparacoes/${repair.id}/edit`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Edit className="w-4 h-4" />
                            </Link>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
