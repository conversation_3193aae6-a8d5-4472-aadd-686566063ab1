// Sistema de cálculo de distâncias baseado em códigos postais portugueses

interface PostalCodeInfo {
  district: string
  municipality: string
  coordinates?: {
    lat: number
    lng: number
  }
}

// Mapeamento básico de códigos postais portugueses por distrito
const POSTAL_CODE_DISTRICTS: { [key: string]: PostalCodeInfo } = {
  // Lisboa (1000-1999)
  '1': { district: 'Lisboa', municipality: 'Lisboa', coordinates: { lat: 38.7223, lng: -9.1393 } },
  
  // Porto (4000-4999)
  '4': { district: 'Porto', municipality: 'Porto', coordinates: { lat: 41.1579, lng: -8.6291 } },
  
  // Braga (4700-4899)
  '47': { district: 'Braga', municipality: 'Braga', coordinates: { lat: 41.5518, lng: -8.4229 } },
  
  // Aveiro (3800-3899)
  '38': { district: 'Aveiro', municipality: 'Aveiro', coordinates: { lat: 40.6443, lng: -8.6455 } },
  
  // Coimbra (3000-3099)
  '30': { district: 'Coimbra', municipality: 'Coimbra', coordinates: { lat: 40.2033, lng: -8.4103 } },
  
  // Leiria (2400-2499)
  '24': { district: 'Leiria', municipality: 'Leiria', coordinates: { lat: 39.7436, lng: -8.8071 } },
  
  // Santarém (2000-2099)
  '20': { district: 'Santarém', municipality: 'Santarém', coordinates: { lat: 39.2362, lng: -8.6868 } },
  
  // Setúbal (2900-2999)
  '29': { district: 'Setúbal', municipality: 'Setúbal', coordinates: { lat: 38.5244, lng: -8.8882 } },
  
  // Faro (8000-8999)
  '8': { district: 'Faro', municipality: 'Faro', coordinates: { lat: 37.0194, lng: -7.9322 } },
  
  // Beja (7800-7899)
  '78': { district: 'Beja', municipality: 'Beja', coordinates: { lat: 38.0150, lng: -7.8632 } },
  
  // Évora (7000-7099)
  '70': { district: 'Évora', municipality: 'Évora', coordinates: { lat: 38.5664, lng: -7.9073 } },
  
  // Portalegre (7300-7399)
  '73': { district: 'Portalegre', municipality: 'Portalegre', coordinates: { lat: 39.2967, lng: -7.4281 } },
  
  // Castelo Branco (6000-6099)
  '60': { district: 'Castelo Branco', municipality: 'Castelo Branco', coordinates: { lat: 39.8197, lng: -7.4967 } },
  
  // Guarda (6300-6399)
  '63': { district: 'Guarda', municipality: 'Guarda', coordinates: { lat: 40.5364, lng: -7.2683 } },
  
  // Viseu (3500-3599)
  '35': { district: 'Viseu', municipality: 'Viseu', coordinates: { lat: 40.6566, lng: -7.9122 } },
  
  // Vila Real (5000-5099)
  '50': { district: 'Vila Real', municipality: 'Vila Real', coordinates: { lat: 41.3006, lng: -7.7441 } },
  
  // Bragança (5300-5399)
  '53': { district: 'Bragança', municipality: 'Bragança', coordinates: { lat: 41.8066, lng: -6.7578 } },
  
  // Viana do Castelo (4900-4999)
  '49': { district: 'Viana do Castelo', municipality: 'Viana do Castelo', coordinates: { lat: 41.6947, lng: -8.8314 } }
}

/**
 * Extrai informações de um código postal português
 */
export function parsePostalCode(postalCode: string): PostalCodeInfo | null {
  if (!postalCode) return null
  
  // Remover espaços e hífens
  const cleanCode = postalCode.replace(/[\s-]/g, '')
  
  // Validar formato (4 dígitos + 3 dígitos)
  if (!/^\d{7}$/.test(cleanCode)) return null
  
  const firstDigit = cleanCode[0]
  const firstTwoDigits = cleanCode.substring(0, 2)
  
  // Tentar match com 2 dígitos primeiro, depois 1 dígito
  return POSTAL_CODE_DISTRICTS[firstTwoDigits] || POSTAL_CODE_DISTRICTS[firstDigit] || null
}

/**
 * Calcula distância entre dois códigos postais usando fórmula de Haversine
 */
export function calculatePostalCodeDistance(postalCode1: string, postalCode2: string): number | null {
  const info1 = parsePostalCode(postalCode1)
  const info2 = parsePostalCode(postalCode2)
  
  if (!info1?.coordinates || !info2?.coordinates) return null
  
  return calculateHaversineDistance(
    info1.coordinates.lat,
    info1.coordinates.lng,
    info2.coordinates.lat,
    info2.coordinates.lng
  )
}

/**
 * Fórmula de Haversine para calcular distância entre coordenadas
 */
function calculateHaversineDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371 // Raio da Terra em km
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return Math.round(R * c * 10) / 10 // Arredondar para 1 casa decimal
}

/**
 * Estima distância baseada em distritos quando não há coordenadas precisas
 */
export function estimateDistanceByDistrict(postalCode1: string, postalCode2: string): number {
  const info1 = parsePostalCode(postalCode1)
  const info2 = parsePostalCode(postalCode2)
  
  if (!info1 || !info2) return 50 // Distância padrão se não conseguir parsear
  
  // Se são do mesmo distrito
  if (info1.district === info2.district) {
    return Math.random() * 20 + 5 // Entre 5-25 km
  }
  
  // Se são distritos adjacentes (simplificado)
  const adjacentDistricts: { [key: string]: string[] } = {
    'Lisboa': ['Santarém', 'Setúbal', 'Leiria'],
    'Porto': ['Braga', 'Aveiro', 'Vila Real', 'Viana do Castelo'],
    'Braga': ['Porto', 'Viana do Castelo', 'Vila Real'],
    'Aveiro': ['Porto', 'Coimbra', 'Viseu'],
    'Coimbra': ['Aveiro', 'Leiria', 'Viseu', 'Castelo Branco'],
    // ... adicionar mais conforme necessário
  }
  
  const adjacent = adjacentDistricts[info1.district]?.includes(info2.district)
  
  if (adjacent) {
    return Math.random() * 50 + 30 // Entre 30-80 km
  }
  
  // Distritos distantes
  return Math.random() * 100 + 80 // Entre 80-180 km
}

/**
 * Valida formato de código postal português
 */
export function isValidPortuguesePostalCode(postalCode: string): boolean {
  if (!postalCode) return false
  
  // Aceitar formatos: 1234-567, 1234567, 1234 567
  const cleanCode = postalCode.replace(/[\s-]/g, '')
  return /^\d{7}$/.test(cleanCode)
}

/**
 * Formata código postal para o padrão português (1234-567)
 */
export function formatPostalCode(postalCode: string): string {
  if (!postalCode) return ''

  const cleanCode = postalCode.replace(/[\s-]/g, '')
  if (cleanCode.length === 7) {
    return `${cleanCode.substring(0, 4)}-${cleanCode.substring(4)}`
  }

  return postalCode
}

/**
 * Filtra lojas dentro de um raio específico baseado no código postal
 */
export function filterShopsByRadius(
  customerPostalCode: string,
  shops: Array<{ profile?: { postalCode?: string; serviceRadius?: number } }>,
  maxRadius: number = 10
): Array<{ shop: any; distance: number }> {
  const results: Array<{ shop: any; distance: number }> = []

  for (const shop of shops) {
    if (!shop.profile?.postalCode) continue

    // Calcular distância
    let distance = calculatePostalCodeDistance(customerPostalCode, shop.profile.postalCode)

    if (distance === null) {
      // Fallback para estimativa por distrito
      distance = estimateDistanceByDistrict(customerPostalCode, shop.profile.postalCode)
    }

    // Verificar se está dentro do raio
    const shopRadius = shop.profile.serviceRadius || 10
    const effectiveRadius = Math.max(maxRadius, shopRadius) // Usar o maior entre o raio solicitado e o raio da loja

    if (distance <= effectiveRadius) {
      results.push({ shop, distance })
    }
  }

  // Ordenar por distância
  return results.sort((a, b) => a.distance - b.distance)
}

/**
 * Sugere raios alternativos quando não há lojas suficientes
 */
export function suggestAlternativeRadius(currentRadius: number): number[] {
  const suggestions = []

  if (currentRadius < 20) suggestions.push(20)
  if (currentRadius < 50) suggestions.push(50)
  if (currentRadius < 100) suggestions.push(100)

  // Sempre incluir "toda Portugal" como última opção
  suggestions.push(999)

  return suggestions
}
