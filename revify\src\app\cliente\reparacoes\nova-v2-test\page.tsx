'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

export default function NovaReparacaoTest() {
  const { data: session } = useSession()
  const router = useRouter()
  
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    console.log('🧪 Página de teste carregada')
    setLoading(false)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong className="font-bold">Erro!</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            🧪 Página de Teste - Nova Reparação
          </h1>
          
          <div className="space-y-4">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <strong className="font-bold">✅ Sucesso!</strong>
              <span className="block sm:inline"> Página carregada sem erros JavaScript.</span>
            </div>
            
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
              <strong className="font-bold">ℹ️ Informação:</strong>
              <span className="block sm:inline"> Esta é uma versão simplificada para testar problemas JavaScript.</span>
            </div>
            
            {session ? (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                <strong className="font-bold">👤 Sessão:</strong>
                <span className="block sm:inline"> Logado como {session.user?.email}</span>
              </div>
            ) : (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <strong className="font-bold">❌ Sessão:</strong>
                <span className="block sm:inline"> Não logado</span>
              </div>
            )}
            
            <div className="mt-6">
              <h2 className="text-lg font-semibold mb-4">Testes Realizados:</h2>
              <ul className="list-disc list-inside space-y-2 text-gray-700">
                <li>✅ useState funcionando</li>
                <li>✅ useEffect funcionando</li>
                <li>✅ useSession funcionando</li>
                <li>✅ useRouter funcionando</li>
                <li>✅ Renderização condicional funcionando</li>
                <li>✅ Nenhum erro de hoisting</li>
              </ul>
            </div>
            
            <div className="mt-6 flex space-x-4">
              <button
                onClick={() => router.push('/cliente/reparacoes/nova-v2')}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Ir para Página Original
              </button>
              
              <button
                onClick={() => router.push('/cliente/dashboard')}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                Voltar ao Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
