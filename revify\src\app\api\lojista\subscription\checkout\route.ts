import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe-config'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { planId, billingCycle, paymentMethod = 'card', useBalance = false } = await request.json()

    if (!planId || !billingCycle) {
      return NextResponse.json(
        { message: 'Plano e ciclo de faturação são obrigatórios' },
        { status: 400 }
      )
    }

    // Buscar o plano
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId }
    })

    if (!plan || !plan.isActive) {
      return NextResponse.json(
        { message: 'Plano não encontrado ou inativo' },
        { status: 404 }
      )
    }

    // Verificar se já tem subscrição ativa
    const existingSubscription = await prisma.subscription.findUnique({
      where: { userId: session.user.id }
    })

    // Se já tem subscrição ativa e é o mesmo plano, bloquear
    if (existingSubscription && existingSubscription.status === 'ACTIVE' && existingSubscription.planId === planId) {
      return NextResponse.json(
        { message: 'Você já possui este plano ativo' },
        { status: 400 }
      )
    }

    // Se é upgrade/downgrade para plano diferente, permitir
    if (existingSubscription && existingSubscription.status === 'ACTIVE' && existingSubscription.planId !== planId) {
      console.log(`Upgrade/downgrade permitido: de ${existingSubscription.planId} para ${planId}`)
    }

    // Calcular preço baseado no ciclo
    let originalPrice = Number(billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice)
    let finalPrice = originalPrice
    let balanceUsed = 0

    // Verificar saldo disponível se useBalance for true
    if (useBalance) {
      const userBalance = await prisma.userBalance.findUnique({
        where: { userId: session.user.id }
      })

      if (userBalance && Number(userBalance.availableAmount) > 0) {
        const availableBalance = Number(userBalance.availableAmount)
        balanceUsed = Math.min(availableBalance, originalPrice)
        finalPrice = Math.max(0, originalPrice - balanceUsed)

        console.log('💰 Usando saldo:', {
          originalPrice,
          availableBalance,
          balanceUsed,
          finalPrice
        })
      }
    }

    const price = finalPrice
    const currentPeriodStart = new Date()
    const currentPeriodEnd = new Date()

    if (billingCycle === 'MONTHLY') {
      currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1)
    } else {
      currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1)
    }

    // Para Multibanco, criar subscrição diretamente
    if (paymentMethod === 'multibanco') {
      // Criar ou atualizar subscrição
      const subscriptionData = {
        planId,
        status: 'INCOMPLETE' as const,
        billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
        currentPeriodStart,
        currentPeriodEnd
      }

      let subscription
      if (existingSubscription) {
        subscription = await prisma.subscription.update({
          where: { id: existingSubscription.id },
          data: subscriptionData
        })
      } else {
        subscription = await prisma.subscription.create({
          data: {
            userId: session.user.id,
            ...subscriptionData
          }
        })
      }

      // Debitar saldo se foi usado
      if (balanceUsed > 0) {
        await prisma.userBalance.update({
          where: { userId: session.user.id },
          data: {
            availableAmount: {
              decrement: balanceUsed
            }
          }
        })

        // Criar transação de débito
        await prisma.transaction.create({
          data: {
            userId: session.user.id,
            type: 'SUBSCRIPTION_PAYMENT',
            amount: -balanceUsed,
            status: 'COMPLETED',
            description: `Pagamento de subscrição - Saldo utilizado: €${balanceUsed.toFixed(2)}`
          }
        })

        console.log(`💰 Saldo debitado: €${balanceUsed} para subscrição`)
      }

      // Criar pagamento pendente (apenas se ainda há valor a pagar)
      if (price > 0) {
        await prisma.subscriptionPayment.create({
          data: {
            subscriptionId: subscription.id,
            amount: price,
            status: 'PENDING',
            periodStart: currentPeriodStart,
            periodEnd: currentPeriodEnd,
            metadata: balanceUsed > 0 ? {
              originalAmount: originalPrice,
              balanceUsed: balanceUsed,
              remainingAmount: price
            } : null
          }
        })
      }

      // Se o pagamento foi 100% coberto pelo saldo, ativar subscrição imediatamente
      if (price === 0) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { status: 'ACTIVE' }
        })

        // Trigger de referral para subscrição ativada
        try {
          await fetch(`${process.env.NEXTAUTH_URL}/api/referral/trigger/subscription`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId: session.user.id })
          })
        } catch (referralError) {
          console.error('❌ Erro ao executar trigger de referral:', referralError)
        }

        return NextResponse.json({
          success: true,
          subscriptionId: subscription.id,
          paidWithBalance: true,
          balanceUsed: balanceUsed,
          redirectUrl: `/lojista/subscription/success?paid_with_balance=true&subscription=${subscription.id}`
        })
      }

      // Sempre criar um pagamento pendente primeiro
      const pendingPayment = await prisma.subscriptionPayment.create({
        data: {
          subscriptionId: subscription.id,
          amount: price,
          currency: 'EUR',
          status: 'PENDING',
          periodStart: currentPeriodStart,
          periodEnd: currentPeriodEnd
        }
      })

      // Tentar gerar referência Multibanco via Stripe
      try {
        const { generateMultibancoReferenceViaStripe } = await import('@/lib/stripe')

        const multibancoData = await generateMultibancoReferenceViaStripe(
          price,
          subscription.id,
          session.user.email || undefined
        )

        // Atualizar o pagamento com os dados do Stripe
        await prisma.subscriptionPayment.update({
          where: { id: pendingPayment.id },
          data: {
            stripePaymentIntentId: multibancoData.paymentIntentId
          }
        })

        // Salvar referência na base de dados
        await prisma.multibancoReference.create({
          data: {
            orderId: subscription.id,
            entity: multibancoData.entity,
            reference: multibancoData.reference.replace(/\s/g, ''),
            amount: Math.round(multibancoData.amount * 100),
            customerEmail: session.user.email,
            status: 'PENDING',
            expiryDate: multibancoData.expiryDate,
            stripePaymentIntentId: multibancoData.paymentIntentId
          }
        })

        console.log('✅ Multibanco reference generated successfully:', {
          source: multibancoData.source,
          entity: multibancoData.entity,
          reference: multibancoData.reference.substring(0, 6) + '***'
        })

      } catch (multibancoError) {
        console.error('❌ Erro ao gerar referência Multibanco:', multibancoError)

        // Mesmo que o Multibanco falhe, mantemos o pagamento pendente
        // O utilizador poderá tentar pagar mais tarde com outro método
        console.log('💡 Pagamento pendente criado, utilizador pode tentar pagar mais tarde')
      }

      return NextResponse.json({
        success: true,
        subscriptionId: subscription.id,
        multibanco: {
          entity: multibancoData.entity,
          reference: multibancoData.reference,
          amount: multibancoData.amount.toFixed(2),
          expires_at: multibancoData.expiryDate.toISOString()
        },
        redirectUrl: `/lojista/subscription/success?multibanco=true&entity=${multibancoData.entity}&reference=${multibancoData.reference}&amount=${multibancoData.amount}&subscription=${subscription.id}`
      })
    }

    // Para outros métodos, usar Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })
    
    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
    
    if (!stripeSecretKey || stripeSecretKey === 'sk_test_TL' || stripeSecretKey.startsWith('sk_live_') && stripeSecretKey.length < 50) {
      console.error('Stripe key validation failed:', {
        hasKey: !!stripeSecretKey,
        keyPrefix: stripeSecretKey?.substring(0, 8),
        keyLength: stripeSecretKey?.length
      })
      return NextResponse.json(
        { message: 'Stripe não configurado corretamente. Verifique as chaves do Stripe no admin.' },
        { status: 400 }
      )
    }
    
    let stripe
    try {
      stripe = await createStripeInstance(stripeSecretKey)
    } catch (error) {
      console.error('Erro ao criar instância Stripe:', error)
      return NextResponse.json(
        { message: 'Erro de configuração do Stripe. Verifique as credenciais.' },
        { status: 400 }
      )
    }

    // Criar sessão de checkout do Stripe para subscrição
    let session_stripe
    try {
      // Configurar métodos de pagamento baseado na seleção
      let paymentMethodTypes = ['card']
      if (paymentMethod === 'paypal') {
        paymentMethodTypes = ['paypal']
      } else if (paymentMethod === 'card') {
        paymentMethodTypes = ['card']
      }

      session_stripe = await stripe.checkout.sessions.create({
      payment_method_types: paymentMethodTypes,
      mode: 'subscription',
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: `Plano ${plan.name}`,
              description: plan.description || undefined
            },
            unit_amount: Math.round(Number(price) * 100),
            recurring: {
              interval: billingCycle === 'MONTHLY' ? 'month' : 'year'
            }
          },
          quantity: 1
        }
      ],
      success_url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/lojista/subscription/success?session_id={CHECKOUT_SESSION_ID}&subscription_id={subscription_id}`,
      cancel_url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/lojista/upgrade`,
      customer_email: session.user.email!,
      metadata: {
        userId: session.user.id,
        planId: planId,
        billingCycle: billingCycle
      }
    })
  } catch (stripeError) {
    console.error('Erro ao criar sessão Stripe:', stripeError)
    return NextResponse.json(
      { message: 'Erro ao processar pagamento. Tente novamente.' },
      { status: 500 }
    )
  }

  return NextResponse.json({
    checkoutUrl: session_stripe.url
  })

  } catch (error) {
    console.error('Erro no checkout de subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
