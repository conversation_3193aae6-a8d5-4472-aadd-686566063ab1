import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const subscriptionId = searchParams.get('subscriptionId')

    if (!subscriptionId) {
      return NextResponse.json(
        { message: 'ID da subscrição é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar subscrição com pagamentos pendentes
    const subscription = await prisma.subscription.findFirst({
      where: {
        id: subscriptionId,
        userId: session.user.id
      },
      include: {
        plan: true,
        payments: {
          where: { status: 'PENDING' },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Subscrição não encontrada' },
        { status: 404 }
      )
    }

    if (subscription.payments.length === 0) {
      return NextResponse.json({
        success: true,
        status: 'no_pending_payments',
        subscription: {
          id: subscription.id,
          status: subscription.status,
          planName: subscription.plan.name
        }
      })
    }

    const pendingPayment = subscription.payments[0]

    // Se tem PaymentIntent do Stripe, verificar o status
    if (pendingPayment.stripePaymentIntentId) {
      try {
        const stripeSecretSetting = await prisma.systemSettings.findUnique({
          where: { key: 'stripeSecretKey' }
        })

        const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

        if (stripeSecretKey) {
          const stripe = await createStripeInstance(stripeSecretKey)
          const paymentIntent = await stripe.paymentIntents.retrieve(pendingPayment.stripePaymentIntentId)

          if (paymentIntent.status === 'succeeded') {
            // Pagamento foi confirmado, atualizar base de dados
            await prisma.subscriptionPayment.update({
              where: { id: pendingPayment.id },
              data: {
                status: 'COMPLETED',
                paidAt: new Date()
              }
            })

            await prisma.subscription.update({
              where: { id: subscription.id },
              data: {
                status: 'ACTIVE'
              }
            })

            // Atualizar referência Multibanco se existir
            await prisma.multibancoReference.updateMany({
              where: {
                stripePaymentIntentId: pendingPayment.stripePaymentIntentId
              },
              data: {
                status: 'PAID',
                paidAt: new Date()
              }
            })

            return NextResponse.json({
              success: true,
              status: 'payment_completed',
              subscription: {
                id: subscription.id,
                status: 'ACTIVE',
                planName: subscription.plan.name
              }
            })
          }

          // Verificar se há detalhes Multibanco atualizados
          if (paymentIntent.next_action?.multibanco_display_details) {
            const details = paymentIntent.next_action.multibanco_display_details

            // Atualizar referência na base de dados se necessário
            await prisma.multibancoReference.updateMany({
              where: {
                stripePaymentIntentId: pendingPayment.stripePaymentIntentId
              },
              data: {
                entity: details.entity || '11249',
                reference: details.reference?.replace(/\s/g, '') || '',
                status: 'PENDING'
              }
            })
          }
        }
      } catch (stripeError) {
        console.error('Erro ao verificar PaymentIntent:', stripeError)
      }
    }

    // Buscar referência Multibanco atual
    const multibancoRef = await prisma.multibancoReference.findFirst({
      where: {
        orderId: subscription.id,
        status: 'PENDING'
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      status: 'payment_pending',
      subscription: {
        id: subscription.id,
        status: subscription.status,
        planName: subscription.plan.name
      },
      payment: {
        id: pendingPayment.id,
        amount: Number(pendingPayment.amount),
        currency: pendingPayment.currency,
        createdAt: pendingPayment.createdAt
      },
      multibanco: multibancoRef ? {
        entity: multibancoRef.entity,
        reference: multibancoRef.reference,
        amount: multibancoRef.amount / 100,
        expiryDate: multibancoRef.expiryDate
      } : null
    })

  } catch (error) {
    console.error('Erro ao verificar status Multibanco:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
