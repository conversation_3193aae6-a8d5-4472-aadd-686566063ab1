import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { paymentMethod = 'card' } = await request.json()

    if (!['card', 'multibanco', 'klarna'].includes(paymentMethod)) {
      return NextResponse.json(
        { message: 'Método de pagamento inválido. Use "card", "multibanco" ou "klarna"' },
        { status: 400 }
      )
    }

    // Verificar se a reparação pertence ao cliente
    const repair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        customerId: session.user.id
      },
      include: {
        repairShop: true
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se há pagamento pendente
    const existingPayment = await prisma.payment.findFirst({
      where: {
        repairId: params.id,
        status: 'PENDING'
      }
    })

    if (!existingPayment) {
      return NextResponse.json(
        { message: 'Nenhum pagamento pendente encontrado' },
        { status: 404 }
      )
    }

    const amount = Number(existingPayment.amount)
    const description = `Reparação #${params.id.slice(-8)} - ${repair.repairShop?.name || 'Loja'}`

    // Usar a API unificada de pagamentos V2
    const paymentIntentResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/create-payment-intent-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount,
        currency: 'EUR',
        paymentMethod,
        metadata: {
          type: 'repair',
          repairId: params.id,
          paymentId: existingPayment.id,
          description,
          customerEmail: session.user.email,
          customerName: session.user.name,
          lojistId: repair.repairShopId,
          lojistName: repair.repairShop?.name,
          platformFee: amount * 0.05,
          shopAmount: amount * 0.95
        },
        customerEmail: session.user.email,
        customerName: session.user.name,
        returnUrl: paymentMethod === 'card' ? undefined : `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}/success`,
        confirmationMethod: 'automatic'
      })
    })

    const paymentIntentData = await paymentIntentResponse.json()

    if (!paymentIntentResponse.ok) {
      console.error('Erro ao criar PaymentIntent para reparação:', paymentIntentData)
      return NextResponse.json(
        { 
          message: paymentIntentData.error || 'Erro ao criar pagamento',
          details: paymentIntentData.details 
        },
        { status: 400 }
      )
    }

    // Atualizar pagamento com PaymentIntent ID
    await prisma.payment.update({
      where: { id: existingPayment.id },
      data: { 
        stripeSessionId: paymentIntentData.paymentIntent.id,
        method: paymentMethod.toUpperCase()
      }
    })

    // Se for Multibanco e tiver referência
    if (paymentMethod === 'multibanco' && paymentIntentData.multibanco) {
      return NextResponse.json({
        success: true,
        type: 'multibanco_reference',
        multibanco: paymentIntentData.multibanco,
        paymentIntent: paymentIntentData.paymentIntent,
        repair: {
          id: repair.id,
          amount: amount,
          description: repair.description
        },
        redirectUrl: `/cliente/reparacoes/${params.id}/success?multibanco=true&entity=${paymentIntentData.multibanco.entity}&reference=${paymentIntentData.multibanco.reference}&amount=${amount}`
      })
    }

    // Para cartão ou Klarna, retornar dados do PaymentIntent
    return NextResponse.json({
      success: true,
      type: 'payment_intent',
      paymentIntent: paymentIntentData.paymentIntent,
      clientSecret: paymentIntentData.paymentIntent.client_secret,
      repair: {
        id: repair.id,
        amount: amount,
        description: repair.description
      }
    })

  } catch (error) {
    console.error('Erro ao processar pagamento unificado da reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}