'use client'

import { useState, useEffect } from 'react'
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  Package, 
  Wrench, 
  Truck,
  User,
  UserCheck
} from 'lucide-react'

interface TimelineEntry {
  id: string
  status: string
  notes?: string
  createdAt: string
  updatedBy?: string
  updatedByType?: string
  updatedByName: string
  updatedByPosition?: string
}

interface RepairTimelineProps {
  repairId: string
  currentStatus: string
  showAddStatus?: boolean
  onStatusUpdate?: () => void
}

const statusConfig = {
  PENDING_PAYMENT: {
    label: 'Aguarda Pagamento',
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100'
  },
  CONFIRMED: {
    label: 'Confirmado',
    icon: CheckCircle,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100'
  },
  IN_PROGRESS: {
    label: 'Em Progresso',
    icon: Wrench,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100'
  },
  WAITING_PARTS: {
    label: 'Aguarda Peças',
    icon: Package,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100'
  },
  COMPLETED: {
    label: 'Concluído',
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100'
  },
  DELIVERED: {
    label: 'Entregue',
    icon: Truck,
    color: 'text-green-700',
    bgColor: 'bg-green-200'
  },
  CANCELLED: {
    label: 'Cancelado',
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-100'
  }
}

export default function RepairTimeline({ 
  repairId, 
  currentStatus, 
  showAddStatus = false,
  onStatusUpdate 
}: RepairTimelineProps) {
  const [timeline, setTimeline] = useState<TimelineEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newStatus, setNewStatus] = useState('')
  const [newNotes, setNewNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchTimeline()
  }, [repairId])

  const fetchTimeline = async () => {
    try {
      const response = await fetch(`/api/repairs/${repairId}/status-history`)
      if (response.ok) {
        const data = await response.json()
        setTimeline(data.statusHistory)
      }
    } catch (error) {
      console.error('Erro ao carregar timeline:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddStatus = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newStatus) return

    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/repairs/${repairId}/status-history`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          notes: newNotes || undefined
        })
      })

      if (response.ok) {
        const data = await response.json()
        setTimeline(prev => [data.historyEntry, ...prev])
        setNewStatus('')
        setNewNotes('')
        setShowAddForm(false)
        onStatusUpdate?.()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar status')
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
      alert('Erro ao atualizar status')
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusConfig = (status: string) => {
    return statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      icon: AlertCircle,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="flex space-x-4">
            <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Add Status Form */}
      {showAddStatus && (
        <div className="bg-gray-50 rounded-lg p-4">
          {!showAddForm ? (
            <button
              onClick={() => setShowAddForm(true)}
              className="w-full text-left px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              + Adicionar atualização de status
            </button>
          ) : (
            <form onSubmit={handleAddStatus} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Novo Status
                </label>
                <select
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Selecionar status...</option>
                  {Object.entries(statusConfig).map(([key, config]) => (
                    <option key={key} value={key}>
                      {config.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notas (opcional)
                </label>
                <textarea
                  value={newNotes}
                  onChange={(e) => setNewNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Adicionar detalhes sobre esta atualização..."
                />
              </div>

              <div className="flex space-x-3">
                <button
                  type="submit"
                  disabled={isSubmitting || !newStatus}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Atualizando...' : 'Atualizar Status'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false)
                    setNewStatus('')
                    setNewNotes('')
                  }}
                  className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancelar
                </button>
              </div>
            </form>
          )}
        </div>
      )}

      {/* Timeline */}
      <div className="flow-root">
        <ul className="-mb-8">
          {timeline.map((entry, index) => {
            const config = getStatusConfig(entry.status)
            const Icon = config.icon
            const isLast = index === timeline.length - 1

            return (
              <li key={entry.id}>
                <div className="relative pb-8">
                  {!isLast && (
                    <span
                      className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                      aria-hidden="true"
                    />
                  )}
                  <div className="relative flex space-x-3">
                    <div>
                      <span className={`h-8 w-8 rounded-full ${config.bgColor} flex items-center justify-center ring-8 ring-white`}>
                        <Icon className={`h-4 w-4 ${config.color}`} aria-hidden="true" />
                      </span>
                    </div>
                    <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {config.label}
                        </p>
                        {entry.notes && (
                          <p className="mt-1 text-sm text-gray-600">
                            {entry.notes}
                          </p>
                        )}
                        <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500">
                          <div className="flex items-center">
                            {entry.updatedByType === 'EMPLOYEE' ? (
                              <UserCheck className="w-3 h-3 mr-1" />
                            ) : (
                              <User className="w-3 h-3 mr-1" />
                            )}
                            <span>
                              {entry.updatedByName}
                              {entry.updatedByPosition && ` (${entry.updatedByPosition})`}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="whitespace-nowrap text-right text-sm text-gray-500">
                        <time dateTime={entry.createdAt}>
                          {formatDate(entry.createdAt)}
                        </time>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            )
          })}
        </ul>
      </div>

      {timeline.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Clock className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p>Nenhuma atualização de status ainda</p>
        </div>
      )}
    </div>
  )
}
