import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || (session.user.role !== 'ADMIN' && !session.user.isSuperAdmin)) {
      console.log('❌ Acesso negado - Session:', {
        exists: !!session,
        role: session?.user?.role,
        isSuperAdmin: session?.user?.isSuperAdmin
      })
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status')
    const paymentType = searchParams.get('paymentType')
    const paymentMethod = searchParams.get('paymentMethod')
    const lojistId = searchParams.get('lojistId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const search = searchParams.get('search')

    // Construir filtros
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (paymentType) {
      where.paymentType = paymentType
    }

    if (paymentMethod) {
      where.paymentMethodType = paymentMethod
    }

    if (lojistId) {
      where.lojistId = lojistId
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate)
      }
    }

    if (search) {
      where.OR = [
        { customerEmail: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
        { lojistName: { contains: search, mode: 'insensitive' } },
        { stripePaymentIntentId: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Buscar pagamentos com paginação (sem relacionamentos pois não existem no schema)
    const [payments, total] = await Promise.all([
      prisma.paymentHistory.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.paymentHistory.count({ where })
    ])

    // Buscar estatísticas
    const stats = await prisma.paymentHistory.aggregate({
      where,
      _sum: {
        amount: true,
        platformFee: true,
        netAmount: true
      },
      _count: {
        id: true
      }
    })

    // Estatísticas por status
    const statusStats = await prisma.paymentHistory.groupBy({
      by: ['status'],
      where,
      _count: {
        id: true
      },
      _sum: {
        amount: true
      }
    })

    // Estatísticas por método de pagamento
    const methodStats = await prisma.paymentHistory.groupBy({
      by: ['paymentMethodType'],
      where,
      _count: {
        id: true
      },
      _sum: {
        amount: true
      }
    })

    // Processar dados dos pagamentos usando campos existentes no schema
    const processedPayments = payments.map(payment => {
      // Usar paymentMethodType do schema (já existe)
      const paymentMethodType = payment.paymentMethodType || 'N/A'

      // Usar customerName e customerEmail do schema (já existem)
      const customerName = payment.customerName || 'N/A'
      const customerEmail = payment.customerEmail || 'N/A'

      // Usar lojistName do schema (já existe)
      const lojistName = payment.lojistName || 'N/A'

      return {
        ...payment,
        paymentMethodType,
        customerName,
        customerEmail,
        lojistName
      }
    })

    return NextResponse.json({
      payments: processedPayments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: {
        totalAmount: stats._sum.amount || 0,
        totalFees: stats._sum.platformFee || 0,
        totalNet: stats._sum.netAmount || 0,
        totalCount: stats._count.id || 0,
        byStatus: statusStats,
        byMethod: methodStats
      }
    })

  } catch (error) {
    console.error('Erro ao buscar histórico de pagamentos:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !session.user.isSuperAdmin) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { action } = await request.json()

    if (action === 'reconcile') {
      // Função de reconciliação - sincronizar com Stripe
      const result = await reconcilePayments()
      return NextResponse.json(result)
    }

    if (action === 'export') {
      // Exportar dados (implementar depois)
      return NextResponse.json({ message: 'Funcionalidade de exportação em desenvolvimento' })
    }

    return NextResponse.json(
      { message: 'Ação não reconhecida' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Erro na ação de pagamentos:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Função para reconciliar pagamentos com Stripe
async function reconcilePayments() {
  const { reconcileAllPayments } = await import('@/lib/payments/reconciliation')
  return await reconcileAllPayments()
}
