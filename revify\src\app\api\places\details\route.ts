import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Função para obter a API key do Google Maps das configurações
async function getGoogleMapsApiKey(): Promise<string | null> {
  try {
    const setting = await prisma.systemSettings.findUnique({
      where: { key: 'googleMapsApiKey' }
    })
    return setting?.value || null
  } catch (error) {
    console.error('Erro ao buscar API key do Google Maps:', error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const placeId = searchParams.get('place_id')

    if (!placeId) {
      return NextResponse.json(
        { message: 'place_id é obrigatório' },
        { status: 400 }
      )
    }

    const apiKey = await getGoogleMapsApiKey()
    if (!apiKey) {
      return NextResponse.json(
        { message: 'Google Maps API key não configurada' },
        { status: 500 }
      )
    }

    // Campos que queremos obter do local
    const fields = 'formatted_address,geometry,address_components,name'
    const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=${fields}&key=${apiKey}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'OK' && data.result) {
      const result = data.result

      // Extrair componentes do endereço
      const addressComponents = result.address_components || []
      let street = ''
      let city = ''
      let postalCode = ''
      let country = ''

      for (const component of addressComponents) {
        const types = component.types
        
        if (types.includes('route')) {
          street = component.long_name
        } else if (types.includes('street_number')) {
          street = `${component.long_name} ${street}`.trim()
        } else if (types.includes('locality') || types.includes('administrative_area_level_2')) {
          city = component.long_name
        } else if (types.includes('postal_code')) {
          postalCode = component.long_name
        } else if (types.includes('country')) {
          country = component.long_name
        }
      }

      return NextResponse.json({
        place_id: placeId,
        formatted_address: result.formatted_address,
        name: result.name || '',
        geometry: {
          location: {
            lat: result.geometry?.location?.lat || 0,
            lng: result.geometry?.location?.lng || 0
          }
        },
        address_components: {
          street: street.trim(),
          city: city,
          postal_code: postalCode,
          country: country
        }
      })
    } else {
      console.error('Places Details API error:', data.status, data.error_message)
      return NextResponse.json(
        { message: `Erro na Places API: ${data.status}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Erro na API de detalhes do local:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
