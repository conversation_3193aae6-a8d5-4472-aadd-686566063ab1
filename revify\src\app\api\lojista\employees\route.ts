import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// GET - Listar empregados da loja
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || (session.user.role !== 'REPAIR_SHOP' && session.user.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    // Para empregados, verificar se tem permissão para ver outros empregados
    if (session.user.role === 'EMPLOYEE') {
      const employee = await prisma.employee.findUnique({
        where: { id: session.user.employeeId },
        select: { permissions: true, repairShopId: true, isActive: true }
      })

      if (!employee || !employee.isActive || !employee.permissions?.canManageRepairs) {
        return NextResponse.json(
          { message: 'Sem permissão para ver empregados' },
          { status: 403 }
        )
      }
    }

    // Determinar ID da loja
    const repairShopId = session.user.role === 'REPAIR_SHOP'
      ? session.user.id
      : session.user.repairShopId

    const employees = await prisma.employee.findMany({
      where: {
        repairShopId: repairShopId
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            isVerified: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            assignedRepairs: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      employees: employees.map(emp => ({
        id: emp.id,
        userId: emp.userId,
        name: emp.name,
        email: emp.email,
        phone: emp.phone,
        position: emp.position,
        isActive: emp.isActive,
        permissions: emp.permissions,
        assignedRepairsCount: emp._count.assignedRepairs,
        createdAt: emp.createdAt,
        user: emp.user
      }))
    })

  } catch (error) {
    console.error('Erro ao buscar empregados:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// POST - Criar novo empregado
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, email, phone, position, password, permissions } = await request.json()

    // Validações
    if (!name || !email || !password) {
      return NextResponse.json(
        { message: 'Nome, email e password são obrigatórios' },
        { status: 400 }
      )
    }

    // Verificar se email já existe
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { message: 'Email já está em uso' },
        { status: 400 }
      )
    }

    // Hash da password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Criar usuário e empregado em transação
    const result = await prisma.$transaction(async (tx) => {
      // Criar usuário
      const user = await tx.user.create({
        data: {
          name,
          email,
          password: hashedPassword,
          role: 'EMPLOYEE',
          isVerified: true
        }
      })

      // Criar empregado
      const employee = await tx.employee.create({
        data: {
          userId: user.id,
          repairShopId: session.user.id,
          name,
          email,
          phone,
          position,
          permissions: permissions || {
            canViewRepairs: true,
            canUpdateRepairStatus: false,
            canManageRepairs: false,
            canViewFinancials: false,
            canManageInventory: false
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              isVerified: true,
              createdAt: true
            }
          }
        }
      })

      return employee
    })

    return NextResponse.json({
      message: 'Empregado criado com sucesso',
      employee: {
        id: result.id,
        userId: result.userId,
        name: result.name,
        email: result.email,
        phone: result.phone,
        position: result.position,
        isActive: result.isActive,
        permissions: result.permissions,
        createdAt: result.createdAt,
        user: result.user
      }
    })

  } catch (error) {
    console.error('Erro ao criar empregado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
