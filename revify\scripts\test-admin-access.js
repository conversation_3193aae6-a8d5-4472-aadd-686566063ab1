const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAdminAccess() {
  try {
    console.log('🔍 Testando acesso de admin...')

    // Buscar o Carlos
    const carlos = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        profile: true
      }
    })

    if (!carlos) {
      console.log('❌ Carlos não encontrado!')
      return
    }

    console.log('✅ Carlos encontrado:')
    console.log('📧 Email:', carlos.email)
    console.log('👤 Nome:', carlos.name)
    console.log('🔑 Role:', carlos.role)
    console.log('👑 Super Admin:', carlos.isSuperAdmin)
    console.log('✅ Verificado:', carlos.isVerified)

    // Simular verificação de acesso admin
    console.log('\n🔐 Verificações de acesso:')
    
    // Verificação 1: Role é ADMIN
    const isAdmin = carlos.role === 'ADMIN'
    console.log('1. Role é ADMIN:', isAdmin ? '✅' : '❌')
    
    // Verificação 2: É Super Admin
    const isSuperAdmin = carlos.isSuperAdmin === true
    console.log('2. É Super Admin:', isSuperAdmin ? '✅' : '❌')
    
    // Verificação 3: Está verificado
    const isVerified = carlos.isVerified === true
    console.log('3. Está verificado:', isVerified ? '✅' : '❌')
    
    // Verificação 4: Tem perfil
    const hasProfile = !!carlos.profile
    console.log('4. Tem perfil:', hasProfile ? '✅' : '❌')

    // Verificação final
    const canAccessAdmin = isAdmin && isSuperAdmin && isVerified
    console.log('\n🎯 Pode aceder ao admin:', canAccessAdmin ? '✅ SIM' : '❌ NÃO')

    if (canAccessAdmin) {
      console.log('\n🚀 Páginas acessíveis:')
      console.log('✅ /admin - Dashboard principal')
      console.log('✅ /admin/pagamentos - Gestão de pagamentos')
      console.log('✅ /admin/users - Gestão de utilizadores')
      console.log('✅ /admin/analytics - Relatórios')
      console.log('✅ /admin/settings - Configurações')
      console.log('✅ /admin/integracoes - Integrações')
    } else {
      console.log('\n❌ Problemas encontrados:')
      if (!isAdmin) console.log('- Role não é ADMIN')
      if (!isSuperAdmin) console.log('- Não é Super Admin')
      if (!isVerified) console.log('- Conta não verificada')
    }

    // Testar query que seria usada no middleware
    console.log('\n🔍 Testando query do middleware...')
    const middlewareQuery = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isSuperAdmin: true,
        isVerified: true
      }
    })

    if (middlewareQuery) {
      console.log('✅ Query do middleware funciona')
      console.log('📋 Dados retornados:', JSON.stringify(middlewareQuery, null, 2))
    } else {
      console.log('❌ Query do middleware falhou')
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAdminAccess()
