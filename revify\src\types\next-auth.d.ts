import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      role: string
      isSuperAdmin?: boolean
      employeeId?: string | null
      repairShopId?: string | null
    }
  }

  interface User {
    id: string
    email: string
    name?: string | null
    role: string
    isSuperAdmin?: boolean
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: string
    isSuperAdmin?: boolean
    employeeId?: string | null
    repairShopId?: string | null
  }
}
