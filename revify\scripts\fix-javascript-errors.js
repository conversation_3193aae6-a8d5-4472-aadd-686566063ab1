const fs = require('fs')
const path = require('path')

// Função para verificar problemas de JavaScript
function checkJavaScriptIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    const issues = []

    lines.forEach((line, index) => {
      const lineNum = index + 1
      
      // Verificar problemas de hoisting
      if (line.includes('const ') || line.includes('let ')) {
        const varMatch = line.match(/(?:const|let)\s+(\w+)/)
        if (varMatch) {
          const varName = varMatch[1]
          
          // Verificar se a variável é usada antes da declaração
          const beforeLines = lines.slice(0, index)
          const usedBefore = beforeLines.some(prevLine => {
            const regex = new RegExp(`\\b${varName}\\b`)
            return regex.test(prevLine) && !prevLine.includes('//') && !prevLine.includes('/*')
          })
          
          if (usedBefore) {
            issues.push({
              line: lineNum,
              type: 'hoisting',
              variable: varName,
              message: `Variável '${varName}' pode estar sendo usada antes da declaração`
            })
          }
        }
      }

      // Verificar problemas de sintaxe comum
      if (line.includes('ev') && !line.includes('event') && !line.includes('prevent') && !line.includes('every') && !line.includes('never') && !line.includes('level')) {
        issues.push({
          line: lineNum,
          type: 'suspicious_ev',
          message: `Possível problema com variável 'ev': ${line.trim()}`
        })
      }

      // Verificar problemas de arrow functions
      if (line.includes('=>') && line.includes('ev')) {
        issues.push({
          line: lineNum,
          type: 'arrow_function',
          message: `Arrow function com 'ev': ${line.trim()}`
        })
      }
    })

    return issues
  } catch (error) {
    return [{ type: 'error', message: `Erro ao ler arquivo: ${error.message}` }]
  }
}

// Arquivos para verificar
const filesToCheck = [
  'src/app/cliente/reparacoes/nova-v2/page.tsx',
  'src/components/AIDiagnosticInput.tsx',
  'src/components/ui/AddressAutocomplete.tsx',
  'src/components/ModernLayout.tsx'
]

console.log('🔍 VERIFICANDO PROBLEMAS JAVASCRIPT...\n')

filesToCheck.forEach(file => {
  const fullPath = path.join(__dirname, '..', file)
  
  if (fs.existsSync(fullPath)) {
    console.log(`📁 Verificando: ${file}`)
    const issues = checkJavaScriptIssues(fullPath)
    
    if (issues.length > 0) {
      console.log('❌ Problemas encontrados:')
      issues.forEach(issue => {
        console.log(`  Linha ${issue.line} (${issue.type}): ${issue.message}`)
      })
    } else {
      console.log('✅ Nenhum problema encontrado')
    }
    console.log('')
  } else {
    console.log(`❌ Arquivo não encontrado: ${file}\n`)
  }
})

// Verificar se há problemas de build
console.log('🔧 SOLUÇÕES RECOMENDADAS:')
console.log('1. Limpar cache do Next.js: rm -rf .next')
console.log('2. Reinstalar dependências: npm install')
console.log('3. Reiniciar servidor: npm run dev')
console.log('4. Verificar se há conflitos de nomes de variáveis')
console.log('5. Verificar se todos os imports estão corretos')

console.log('\n🎯 PARA CORRIGIR O ERRO "Cannot access ev before initialization":')
console.log('- Verificar se há declarações de variáveis após seu uso')
console.log('- Verificar se há problemas de hoisting em arrow functions')
console.log('- Verificar se há conflitos de nomes entre parâmetros e variáveis')
console.log('- Limpar completamente o build e reconstruir')
