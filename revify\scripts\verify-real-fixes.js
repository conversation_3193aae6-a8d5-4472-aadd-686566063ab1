const fs = require('fs')
const path = require('path')

console.log('🔍 VERIFICANDO SE AS CORREÇÕES FORAM REALMENTE APLICADAS...\n')

// 1. Verificar se a página de subscrição tem as correções
console.log('1️⃣ VERIFICANDO PÁGINA DE SUBSCRIÇÃO')
const subscricaoPath = path.join(__dirname, '..', 'src/app/lojista/subscricao/page.tsx')

if (fs.existsSync(subscricaoPath)) {
  const content = fs.readFileSync(subscricaoPath, 'utf8')
  
  // Verificar se tem as verificações de segurança
  const hasOptionalChaining = content.includes('subscription?.plan?.name')
  const hasGetCurrentPriceCheck = content.includes('if (!subscription || !subscription.plan) return 0')
  
  console.log(`✅ Arquivo existe: ${subscricaoPath}`)
  console.log(`${hasOptionalChaining ? '✅' : '❌'} Optional chaining aplicado: subscription?.plan?.name`)
  console.log(`${hasGetCurrentPriceCheck ? '✅' : '❌'} Verificação getCurrentPrice aplicada`)
  
  if (hasOptionalChaining && hasGetCurrentPriceCheck) {
    console.log('✅ PÁGINA DE SUBSCRIÇÃO: CORREÇÕES APLICADAS')
  } else {
    console.log('❌ PÁGINA DE SUBSCRIÇÃO: CORREÇÕES NÃO APLICADAS')
  }
} else {
  console.log('❌ Arquivo de subscrição não encontrado')
}

// 2. Verificar se a página nova-v2-fixed existe
console.log('\n2️⃣ VERIFICANDO PÁGINA NOVA-V2-FIXED')
const novaV2FixedPath = path.join(__dirname, '..', 'src/app/cliente/reparacoes/nova-v2-fixed/page.tsx')

if (fs.existsSync(novaV2FixedPath)) {
  const content = fs.readFileSync(novaV2FixedPath, 'utf8')
  
  const hasUseCallback = content.includes('useCallback')
  const hasSuspense = content.includes('Suspense')
  const hasProperStructure = content.includes('NovaReparacaoPageContent')
  
  console.log(`✅ Arquivo existe: ${novaV2FixedPath}`)
  console.log(`${hasUseCallback ? '✅' : '❌'} useCallback implementado`)
  console.log(`${hasSuspense ? '✅' : '❌'} Suspense wrapper implementado`)
  console.log(`${hasProperStructure ? '✅' : '❌'} Estrutura correta implementada`)
  
  if (hasUseCallback && hasSuspense && hasProperStructure) {
    console.log('✅ PÁGINA NOVA-V2-FIXED: CRIADA CORRETAMENTE')
  } else {
    console.log('❌ PÁGINA NOVA-V2-FIXED: PROBLEMAS NA ESTRUTURA')
  }
} else {
  console.log('❌ Página nova-v2-fixed não encontrada')
}

// 3. Verificar se a página original ainda tem problemas
console.log('\n3️⃣ VERIFICANDO PÁGINA NOVA-V2 ORIGINAL')
const novaV2OriginalPath = path.join(__dirname, '..', 'src/app/cliente/reparacoes/nova-v2/page.tsx')

if (fs.existsSync(novaV2OriginalPath)) {
  const content = fs.readFileSync(novaV2OriginalPath, 'utf8')
  
  // Verificar problemas conhecidos
  const hasSetSelectedDevice = content.includes('setSelectedDevice')
  const hasProblematicVariables = content.match(/\bey\s*=|\bev\s*=/)
  const hasComplexStructure = content.length > 10000 // Arquivo muito grande pode ter problemas
  
  console.log(`✅ Arquivo existe: ${novaV2OriginalPath}`)
  console.log(`${hasSetSelectedDevice ? '❌' : '✅'} setSelectedDevice ${hasSetSelectedDevice ? 'encontrado (problema)' : 'não encontrado'}`)
  console.log(`${hasProblematicVariables ? '❌' : '✅'} Variáveis problemáticas ${hasProblematicVariables ? 'encontradas' : 'não encontradas'}`)
  console.log(`${hasComplexStructure ? '⚠️' : '✅'} Tamanho do arquivo: ${content.length} caracteres`)
  
  if (!hasSetSelectedDevice && !hasProblematicVariables && !hasComplexStructure) {
    console.log('✅ PÁGINA NOVA-V2 ORIGINAL: SEM PROBLEMAS ÓBVIOS')
  } else {
    console.log('❌ PÁGINA NOVA-V2 ORIGINAL: AINDA TEM PROBLEMAS')
  }
} else {
  console.log('❌ Página nova-v2 original não encontrada')
}

// 4. Verificar se o sistema de reconciliação foi corrigido
console.log('\n4️⃣ VERIFICANDO SISTEMA DE RECONCILIAÇÃO')
const reconciliationPath = path.join(__dirname, '..', 'src/lib/payments/reconciliation.ts')

if (fs.existsSync(reconciliationPath)) {
  const content = fs.readFileSync(reconciliationPath, 'utf8')
  
  const hasDeterminePaymentType = content.includes('function determinePaymentType')
  const hasFixExistingPaymentTypes = content.includes('function fixExistingPaymentTypes')
  const hasIntelligentDetection = content.includes('billingCycle') && content.includes('planId')
  
  console.log(`✅ Arquivo existe: ${reconciliationPath}`)
  console.log(`${hasDeterminePaymentType ? '✅' : '❌'} Função determinePaymentType ${hasDeterminePaymentType ? 'implementada' : 'não encontrada'}`)
  console.log(`${hasFixExistingPaymentTypes ? '✅' : '❌'} Função fixExistingPaymentTypes ${hasFixExistingPaymentTypes ? 'implementada' : 'não encontrada'}`)
  console.log(`${hasIntelligentDetection ? '✅' : '❌'} Detecção inteligente ${hasIntelligentDetection ? 'implementada' : 'não encontrada'}`)
  
  if (hasDeterminePaymentType && hasFixExistingPaymentTypes && hasIntelligentDetection) {
    console.log('✅ SISTEMA DE RECONCILIAÇÃO: CORREÇÕES APLICADAS')
  } else {
    console.log('❌ SISTEMA DE RECONCILIAÇÃO: CORREÇÕES NÃO APLICADAS')
  }
} else {
  console.log('❌ Arquivo de reconciliação não encontrado')
}

// 5. Verificar se o sistema Multibanco foi corrigido
console.log('\n5️⃣ VERIFICANDO SISTEMA MULTIBANCO')
const integratedSystemPath = path.join(__dirname, '..', 'src/lib/stripe/integrated-system.ts')

if (fs.existsSync(integratedSystemPath)) {
  const content = fs.readFileSync(integratedSystemPath, 'utf8')
  
  const hasMultibancoFix = content.includes('collection_method: send_invoice')
  const hasPaymentMethodCheck = content.includes("paymentMethod === 'multibanco'")
  const hasDaysUntilDue = content.includes('days_until_due: 3')
  
  console.log(`✅ Arquivo existe: ${integratedSystemPath}`)
  console.log(`${hasMultibancoFix ? '✅' : '❌'} Correção Multibanco ${hasMultibancoFix ? 'implementada' : 'não encontrada'}`)
  console.log(`${hasPaymentMethodCheck ? '✅' : '❌'} Verificação método pagamento ${hasPaymentMethodCheck ? 'implementada' : 'não encontrada'}`)
  console.log(`${hasDaysUntilDue ? '✅' : '❌'} days_until_due configurado ${hasDaysUntilDue ? 'corretamente' : 'incorretamente'}`)
  
  if (hasMultibancoFix && hasPaymentMethodCheck && hasDaysUntilDue) {
    console.log('✅ SISTEMA MULTIBANCO: CORREÇÕES APLICADAS')
  } else {
    console.log('❌ SISTEMA MULTIBANCO: CORREÇÕES NÃO APLICADAS')
  }
} else {
  console.log('❌ Arquivo integrated-system não encontrado')
}

// 6. Verificar se o schema Prisma tem o campo stripePriceId
console.log('\n6️⃣ VERIFICANDO SCHEMA PRISMA')
const schemaPath = path.join(__dirname, '..', 'prisma/schema.prisma')

if (fs.existsSync(schemaPath)) {
  const content = fs.readFileSync(schemaPath, 'utf8')
  
  const hasStripePriceId = content.includes('stripePriceId')
  const hasSubscriptionPlanModel = content.includes('model SubscriptionPlan')
  
  console.log(`✅ Arquivo existe: ${schemaPath}`)
  console.log(`${hasStripePriceId ? '✅' : '❌'} Campo stripePriceId ${hasStripePriceId ? 'adicionado' : 'não encontrado'}`)
  console.log(`${hasSubscriptionPlanModel ? '✅' : '❌'} Modelo SubscriptionPlan ${hasSubscriptionPlanModel ? 'existe' : 'não encontrado'}`)
  
  if (hasStripePriceId && hasSubscriptionPlanModel) {
    console.log('✅ SCHEMA PRISMA: CORREÇÕES APLICADAS')
  } else {
    console.log('❌ SCHEMA PRISMA: CORREÇÕES NÃO APLICADAS')
  }
} else {
  console.log('❌ Schema Prisma não encontrado')
}

console.log('\n📊 RESUMO FINAL:')
console.log('=' .repeat(50))

// Contar correções aplicadas
let correctionsApplied = 0
let totalCorrections = 6

// Verificar cada correção novamente
const checks = [
  { name: 'Página Subscrição', file: subscricaoPath, checks: ['subscription?.plan?.name', 'if (!subscription || !subscription.plan)'] },
  { name: 'Página Nova-V2-Fixed', file: novaV2FixedPath, checks: ['useCallback', 'Suspense'] },
  { name: 'Sistema Reconciliação', file: reconciliationPath, checks: ['determinePaymentType', 'fixExistingPaymentTypes'] },
  { name: 'Sistema Multibanco', file: integratedSystemPath, checks: ['collection_method: send_invoice', "paymentMethod === 'multibanco'"] },
  { name: 'Schema Prisma', file: schemaPath, checks: ['stripePriceId'] }
]

checks.forEach(check => {
  if (fs.existsSync(check.file)) {
    const content = fs.readFileSync(check.file, 'utf8')
    const allChecksPass = check.checks.every(checkStr => content.includes(checkStr))
    
    if (allChecksPass) {
      console.log(`✅ ${check.name}: CORRIGIDO`)
      correctionsApplied++
    } else {
      console.log(`❌ ${check.name}: NÃO CORRIGIDO`)
    }
  } else {
    console.log(`❌ ${check.name}: ARQUIVO NÃO ENCONTRADO`)
  }
})

console.log('\n🎯 RESULTADO:')
console.log(`${correctionsApplied}/${totalCorrections} correções aplicadas (${Math.round(correctionsApplied/totalCorrections*100)}%)`)

if (correctionsApplied === totalCorrections) {
  console.log('🎉 TODAS AS CORREÇÕES FORAM APLICADAS COM SUCESSO!')
} else {
  console.log('⚠️ ALGUMAS CORREÇÕES NÃO FORAM APLICADAS - VERIFICAR DEPLOY')
}

console.log('\n📋 PRÓXIMOS PASSOS:')
if (correctionsApplied < totalCorrections) {
  console.log('1. Verificar se o deploy foi bem-sucedido')
  console.log('2. Fazer novo deploy: vercel --prod --force')
  console.log('3. Verificar cache do navegador: Ctrl+F5')
  console.log('4. Testar URLs específicas das correções')
} else {
  console.log('1. Testar funcionalidades em produção')
  console.log('2. Verificar se erros JavaScript persistem')
  console.log('3. Testar subscrições e reconciliação')
  console.log('4. Confirmar que tudo funciona corretamente')
}

console.log('\n✅ Verificação concluída!')
