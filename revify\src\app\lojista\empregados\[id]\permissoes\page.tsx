'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Save, Shield, Check, X } from 'lucide-react'

interface Employee {
  id: string
  userId: string
  name: string
  email: string
  phone?: string
  position?: string
  isActive: boolean
  permissions: any
  createdAt: string
}

interface EmployeePermissions {
  canViewRepairs: boolean
  canUpdateRepairStatus: boolean
  canManageRepairs: boolean
  canViewFinancials: boolean
  canManageInventory: boolean
}

export default function EmployeePermissionsPage() {
  const params = useParams()
  const router = useRouter()
  const [employee, setEmployee] = useState<Employee | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [permissions, setPermissions] = useState<EmployeePermissions>({
    canViewRepairs: true,
    canUpdateRepairStatus: false,
    canManageRepairs: false,
    canViewFinancials: false,
    canManageInventory: false
  })
  const [errors, setErrors] = useState<any>({})

  useEffect(() => {
    fetchEmployeeDetails()
  }, [params.id])

  const fetchEmployeeDetails = async () => {
    try {
      const response = await fetch(`/api/lojista/employees/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        const emp = data.employee
        setEmployee(emp)
        setPermissions(emp.permissions || {
          canViewRepairs: true,
          canUpdateRepairStatus: false,
          canManageRepairs: false,
          canViewFinancials: false,
          canManageInventory: false
        })
      } else {
        router.push('/lojista/empregados')
      }
    } catch (error) {
      console.error('Erro ao carregar empregado:', error)
      router.push('/lojista/empregados')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    try {
      const response = await fetch(`/api/lojista/employees/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          permissions
        })
      })

      const data = await response.json()

      if (response.ok) {
        router.push(`/lojista/empregados/${params.id}`)
      } else {
        setErrors({ general: data.message })
      }
    } catch (error) {
      setErrors({ general: 'Erro ao atualizar permissões' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePermissionChange = (permission: keyof EmployeePermissions) => {
    setPermissions(prev => ({
      ...prev,
      [permission]: !prev[permission]
    }))
  }

  const permissionLabels = {
    canViewRepairs: {
      title: 'Ver Reparações',
      description: 'Pode visualizar a lista de reparações e seus detalhes',
      icon: '👁️'
    },
    canUpdateRepairStatus: {
      title: 'Atualizar Status das Reparações',
      description: 'Pode alterar o status das reparações (ex: Em Progresso, Concluído)',
      icon: '🔄'
    },
    canManageRepairs: {
      title: 'Gerir Reparações',
      description: 'Pode criar, editar e eliminar reparações',
      icon: '🛠️'
    },
    canViewFinancials: {
      title: 'Ver Informações Financeiras',
      description: 'Pode aceder a relatórios financeiros e informações de pagamento',
      icon: '💰'
    },
    canManageInventory: {
      title: 'Gerir Inventário',
      description: 'Pode gerir stock de peças e produtos',
      icon: '📦'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!employee) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Empregado não encontrado</h1>
          <Link href="/lojista/empregados" className="text-primary-600 hover:text-primary-700">
            Voltar à lista de empregados
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Link
            href={`/lojista/empregados/${employee.id}`}
            className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gerir Permissões</h1>
            <p className="text-gray-600 mt-2">
              Definir permissões de acesso para {employee.name}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-4xl">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <Shield className="w-6 h-6 text-primary-600 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900">Permissões de Acesso</h2>
          </div>
          
          <div className="space-y-6">
            {Object.entries(permissionLabels).map(([key, label]) => (
              <div key={key} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start flex-1">
                    <div className="text-2xl mr-4 mt-1">{label.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="text-base font-medium text-gray-900">
                          {label.title}
                        </h3>
                        {permissions[key as keyof EmployeePermissions] && (
                          <Check className="w-4 h-4 text-green-500 ml-2" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {label.description}
                      </p>
                    </div>
                  </div>
                  <div className="ml-4">
                    <button
                      type="button"
                      onClick={() => handlePermissionChange(key as keyof EmployeePermissions)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                        permissions[key as keyof EmployeePermissions]
                          ? 'bg-primary-600'
                          : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          permissions[key as keyof EmployeePermissions]
                            ? 'translate-x-6'
                            : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start">
              <div className="text-amber-600 mr-2">⚠️</div>
              <div>
                <p className="text-sm text-amber-800 font-medium">Importante:</p>
                <p className="text-sm text-amber-700 mt-1">
                  As alterações de permissões entram em vigor imediatamente. 
                  O empregado precisará fazer logout e login novamente para que as novas permissões sejam aplicadas.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {errors.general && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{errors.general}</p>
          </div>
        )}

        {/* Actions */}
        <div className="mt-8 flex items-center justify-end space-x-4">
          <Link
            href={`/lojista/empregados/${employee.id}`}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Atualizando...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Salvar Permissões
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
