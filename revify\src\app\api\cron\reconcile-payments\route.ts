import { NextRequest, NextResponse } from 'next/server'
import { reconcileAllPayments } from '@/lib/payments/reconciliation'

export async function GET(request: NextRequest) {
  try {
    // Verificar se é uma chamada autorizada (Vercel Cron ou chave secreta)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { message: 'Não autorizado' },
        { status: 401 }
      )
    }

    console.log('🕐 Iniciando reconciliação automática de pagamentos...')
    
    const startTime = Date.now()
    const result = await reconcileAllPayments()
    const duration = Date.now() - startTime

    // Log do resultado
    console.log('✅ Reconciliação automática concluída:', {
      duration: `${duration}ms`,
      reconciledCount: result.reconciledCount,
      newPaymentsFound: result.newPaymentsFound,
      updatedPayments: result.updatedPayments,
      errorCount: result.errorCount,
      success: result.success
    })

    // Se houve muitos erros, registar para investigação
    if (result.errorCount > 10) {
      console.error('⚠️ Muitos erros na reconciliação automática:', {
        errorCount: result.errorCount,
        errors: result.errors.slice(0, 5) // Primeiros 5 erros
      })
    }

    return NextResponse.json({
      success: result.success,
      message: result.summary,
      duration,
      timestamp: new Date().toISOString(),
      details: {
        reconciledCount: result.reconciledCount,
        newPaymentsFound: result.newPaymentsFound,
        updatedPayments: result.updatedPayments,
        errorCount: result.errorCount
      }
    })

  } catch (error) {
    console.error('❌ Erro na reconciliação automática:', error)
    
    return NextResponse.json(
      { 
        success: false,
        message: 'Erro na reconciliação automática',
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Também permitir POST para testes manuais
export async function POST(request: NextRequest) {
  return GET(request)
}
