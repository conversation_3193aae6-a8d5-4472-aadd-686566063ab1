import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { paymentId, paymentMethod = 'card' } = await request.json()

    if (!paymentId) {
      return NextResponse.json(
        { message: 'ID do pagamento é obrigatório' },
        { status: 400 }
      )
    }

    if (!['card', 'multibanco'].includes(paymentMethod)) {
      return NextResponse.json(
        { message: 'Método de pagamento inválido. Use "card" ou "multibanco"' },
        { status: 400 }
      )
    }

    // Buscar pagamento com relacionamentos
    const payment = await prisma.subscriptionPayment.findFirst({
      where: {
        id: paymentId,
        status: 'PENDING',
        subscription: {
          userId: session.user.id
        }
      },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    if (!payment) {
      return NextResponse.json(
        { message: 'Pagamento não encontrado ou já processado' },
        { status: 404 }
      )
    }

    const amount = Number(payment.amount)
    const description = `Subscrição ${payment.subscription.plan.name} - ${payment.subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`

    // Usar a API unificada de pagamentos V2
    const paymentIntentResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/create-payment-intent-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount,
        currency: 'EUR',
        paymentMethod,
        metadata: {
          type: 'subscription',
          subscriptionId: payment.subscription.id,
          planName: payment.subscription.plan.name,
          billingCycle: payment.subscription.billingCycle,
          paymentId: payment.id,
          description,
          customerEmail: session.user.email,
          customerName: session.user.name
        },
        customerEmail: session.user.email,
        customerName: session.user.name,
        returnUrl: paymentMethod === 'card' ? undefined : `${process.env.NEXTAUTH_URL}/lojista/subscription/success`,
        confirmationMethod: 'automatic'
      })
    })

    const paymentIntentData = await paymentIntentResponse.json()

    if (!paymentIntentResponse.ok) {
      console.error('Erro ao criar PaymentIntent:', paymentIntentData)
      return NextResponse.json(
        { 
          message: paymentIntentData.error || 'Erro ao criar pagamento',
          details: paymentIntentData.details 
        },
        { status: 400 }
      )
    }

    // Se for Multibanco e tiver referência, atualizar o pagamento
    if (paymentMethod === 'multibanco' && paymentIntentData.multibanco) {
      await prisma.subscriptionPayment.update({
        where: { id: payment.id },
        data: {
          multibancoEntity: paymentIntentData.multibanco.entity,
          multibancoReference: paymentIntentData.multibanco.reference
        }
      })

      return NextResponse.json({
        success: true,
        type: 'multibanco_reference',
        multibanco: paymentIntentData.multibanco,
        paymentIntent: paymentIntentData.paymentIntent,
        redirectUrl: `/lojista/subscription/success?multibanco=true&entity=${paymentIntentData.multibanco.entity}&reference=${paymentIntentData.multibanco.reference}&amount=${amount}&subscription=${payment.subscription.id}`
      })
    }

    // Para cartão, retornar dados do PaymentIntent
    return NextResponse.json({
      success: true,
      type: 'payment_intent',
      paymentIntent: paymentIntentData.paymentIntent,
      clientSecret: paymentIntentData.paymentIntent.client_secret,
      payment: {
        id: payment.id,
        amount: amount,
        currency: payment.currency,
        planName: payment.subscription.plan.name
      }
    })

  } catch (error) {
    console.error('Erro ao processar pagamento unificado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}