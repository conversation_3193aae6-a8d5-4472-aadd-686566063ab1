import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { subscriptionId, paymentMethod } = await request.json()

    if (!subscriptionId || !paymentMethod) {
      return NextResponse.json(
        { message: 'Dados obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Buscar subscrição com pagamento pendente
    const subscription = await prisma.subscription.findFirst({
      where: {
        id: subscriptionId,
        userId: session.user.id,
        status: {
          in: ['INCOMPLETE', 'INCOMPLETE_EXPIRED', 'UNPAID']
        }
      },
      include: {
        plan: true,
        payments: {
          where: { status: 'PENDING' },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Subscrição não encontrada ou já ativa' },
        { status: 404 }
      )
    }

    if (subscription.payments.length === 0) {
      return NextResponse.json(
        { message: 'Nenhum pagamento pendente encontrado' },
        { status: 404 }
      )
    }

    const pendingPayment = subscription.payments[0]
    const amount = Number(pendingPayment.amount)

    // Verificar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 500 }
      )
    }

    if (paymentMethod === 'multibanco') {
      try {
        // Criar novo PaymentIntent para Multibanco
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount * 100),
          currency: 'eur',
          payment_method_types: ['multibanco'],
          metadata: {
            subscriptionId: subscription.id,
            paymentId: pendingPayment.id,
            userId: session.user.id,
            type: 'subscription_retry_multibanco'
          }
        })

        // Confirmar PaymentIntent para gerar referência Multibanco
        const confirmedPaymentIntent = await stripe.paymentIntents.confirm(paymentIntent.id, {
          payment_method: {
            type: 'multibanco'
          },
          return_url: `${process.env.NEXTAUTH_URL}/lojista/subscription/success?payment_intent=${paymentIntent.id}`
        })

        // Atualizar o pagamento com o novo PaymentIntent
        await prisma.subscriptionPayment.update({
          where: { id: pendingPayment.id },
          data: {
            stripePaymentIntentId: paymentIntent.id
          }
        })

        // Remover referência antiga se existir
        await prisma.multibancoReference.deleteMany({
          where: {
            orderId: subscription.id,
            status: 'PENDING'
          }
        })

        // Extrair detalhes Multibanco
        let multibancoDetails = null
        if (confirmedPaymentIntent.next_action?.multibanco_display_details) {
          const details = confirmedPaymentIntent.next_action.multibanco_display_details
          multibancoDetails = {
            entity: details.entity,
            reference: details.reference,
            amount: amount,
            expiryDate: details.expires_at ? new Date(details.expires_at * 1000) : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
          }

          // Criar nova referência
          await prisma.multibancoReference.create({
            data: {
              orderId: subscription.id,
              entity: details.entity || '11249',
              reference: details.reference?.replace(/\s/g, '') || '',
              amount: Math.round(amount * 100),
              customerEmail: session.user.email,
              status: 'PENDING',
              expiryDate: multibancoDetails.expiryDate,
              stripePaymentIntentId: paymentIntent.id
            }
          })

          console.log('✅ Nova referência Multibanco gerada:', {
            entity: details.entity,
            reference: details.reference?.substring(0, 6) + '***'
          })
        }

        return NextResponse.json({
          success: true,
          paymentMethod: 'multibanco',
          paymentIntent: {
            id: confirmedPaymentIntent.id,
            status: confirmedPaymentIntent.status
          },
          multibanco: multibancoDetails,
          redirectUrl: `/lojista/subscription/success?payment_intent=${paymentIntent.id}&multibanco=true`
        })

      } catch (error) {
        console.error('Erro ao gerar referência Multibanco:', error)
        return NextResponse.json(
          { message: 'Erro ao gerar referência Multibanco' },
          { status: 500 }
        )
      }

    } else if (paymentMethod === 'card') {
      try {
        // Criar sessão Stripe para cartão
        const stripe = await createStripeInstance(stripeSecretKey)

        const stripeSession = await stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          line_items: [
            {
              price_data: {
                currency: 'eur',
                product_data: {
                  name: `${subscription.plan.name} - ${subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`,
                  description: `Pagamento da subscrição ${subscription.plan.name}`
                },
                unit_amount: Math.round(amount * 100)
              },
              quantity: 1
            }
          ],
          mode: 'payment',
          success_url: `${process.env.NEXTAUTH_URL}/lojista/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${process.env.NEXTAUTH_URL}/lojista/subscricao`,
          customer_email: session.user.email || undefined,
          metadata: {
            subscriptionId: subscription.id,
            paymentId: pendingPayment.id,
            userId: session.user.id,
            type: 'subscription_retry_payment'
          }
        })

        return NextResponse.json({
          success: true,
          paymentMethod: 'card',
          checkoutUrl: stripeSession.url
        })

      } catch (error) {
        console.error('Erro ao criar sessão Stripe:', error)
        return NextResponse.json(
          { message: 'Erro ao processar pagamento com cartão' },
          { status: 500 }
        )
      }

    } else {
      return NextResponse.json(
        { message: 'Método de pagamento não suportado' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Erro ao processar retry de pagamento:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
