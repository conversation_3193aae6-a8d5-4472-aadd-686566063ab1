import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    const addresses = profile?.addresses as any[] || []

    // Ordenar endereços: padrão primeiro, depois por data de criação
    const sortedAddresses = addresses.sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1
      if (!a.isDefault && b.isDefault) return 1
      return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
    })

    return NextResponse.json({
      addresses: sortedAddresses
    })

  } catch (error) {
    console.error('Erro ao buscar endereços:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()
    const { label, street, city, postalCode, country, isDefault, type } = data

    // Validações básicas
    if (!label || !street || !city || !postalCode) {
      return NextResponse.json(
        { message: 'Campos obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Buscar perfil atual
    let profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    let currentAddresses = profile?.addresses as any[] || []

    // Se for endereço padrão, remover padrão dos outros
    if (isDefault) {
      currentAddresses = currentAddresses.map(addr => ({ ...addr, isDefault: false }))
    }

    // Adicionar novo endereço
    const newAddress = {
      id: Date.now().toString(),
      label,
      street,
      city,
      postalCode,
      country: country || 'Portugal',
      isDefault: isDefault || currentAddresses.length === 0, // Primeiro endereço é sempre padrão
      type: type || 'HOME',
      createdAt: new Date().toISOString()
    }

    currentAddresses.push(newAddress)

    // Atualizar ou criar perfil
    if (profile) {
      await prisma.profile.update({
        where: {
          userId: session.user.id
        },
        data: {
          addresses: currentAddresses
        }
      })
    } else {
      await prisma.profile.create({
        data: {
          userId: session.user.id,
          addresses: currentAddresses
        }
      })
    }

    return NextResponse.json({
      message: 'Endereço adicionado com sucesso',
      address: newAddress
    })

  } catch (error) {
    console.error('Erro ao adicionar endereço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
