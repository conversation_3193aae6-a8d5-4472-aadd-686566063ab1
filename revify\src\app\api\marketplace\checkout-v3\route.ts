import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createUnifiedPaymentSystem } from '@/lib/payments/unified-payment-system'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const { addressId, customerInfo, paymentMethod = 'card', coupon, useBalance = false } = await request.json()

    if (!addressId) {
      return NextResponse.json(
        { message: 'Endereço de entrega é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar carrinho do utilizador
    const cartItems = await prisma.cartItem.findMany({
      where: { userId: session.user.id },
      include: {
        product: {
          include: {
            seller: true
          }
        }
      }
    })

    if (cartItems.length === 0) {
      return NextResponse.json(
        { message: 'Carrinho vazio' },
        { status: 400 }
      )
    }

    // Calcular totais
    const subtotal = cartItems.reduce((sum, item) => {
      return sum + (Number(item.product.price) * item.quantity)
    }, 0)

    let discount = 0
    if (coupon) {
      // Aplicar desconto do cupão
      const couponData = await prisma.coupon.findFirst({
        where: {
          code: coupon.code,
          isActive: true,
          expiresAt: { gte: new Date() }
        }
      })

      if (couponData) {
        if (couponData.type === 'PERCENTAGE') {
          discount = subtotal * (Number(couponData.value) / 100)
        } else {
          discount = Number(couponData.value)
        }
      }
    }

    const total = Math.max(0, subtotal - discount)

    if (total <= 0) {
      return NextResponse.json(
        { message: 'Valor total inválido' },
        { status: 400 }
      )
    }

    // Agrupar itens por vendedor para criar encomendas separadas
    const itemsBySeller = cartItems.reduce((acc, item) => {
      const sellerId = item.product.sellerId
      if (!acc[sellerId]) {
        acc[sellerId] = []
      }
      acc[sellerId].push(item)
      return acc
    }, {} as Record<string, typeof cartItems>)

    const orderIds: string[] = []
    let totalAmount = 0

    // Criar encomendas para cada vendedor
    for (const [sellerId, items] of Object.entries(itemsBySeller)) {
      const sellerTotal = items.reduce((sum, item) => {
        return sum + (Number(item.product.price) * item.quantity)
      }, 0)

      const order = await prisma.order.create({
        data: {
          customerId: session.user.id,
          sellerId,
          status: 'PENDING',
          total: sellerTotal,
          shippingAddressId: addressId,
          customerName: customerInfo.name,
          customerEmail: customerInfo.email,
          customerPhone: customerInfo.phone || '',
          items: {
            create: items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              price: Number(item.product.price),
              productName: item.product.name
            }))
          }
        }
      })

      orderIds.push(order.id)
      totalAmount += sellerTotal
    }

    // Criar sistema de pagamento unificado
    const paymentSystem = await createUnifiedPaymentSystem()

    // Criar pagamento pendente para a primeira encomenda (representativa)
    const pendingPayment = await paymentSystem.createPendingPayment(
      'marketplace',
      orderIds[0], // Usar primeira encomenda como referência
      totalAmount,
      'EUR',
      {
        orderIds: orderIds.join(','),
        customerName: customerInfo.name,
        customerEmail: customerInfo.email,
        platformFee: totalAmount * 0.05,
        shopAmount: totalAmount * 0.95
      }
    )

    // Preparar request de pagamento
    const paymentRequest = {
      amount: totalAmount,
      currency: 'EUR',
      description: `Encomenda Marketplace - ${orderIds.length} produto(s)`,
      metadata: {
        type: 'marketplace_order',
        orderIds: orderIds.join(','),
        userId: session.user.id,
        customerName: customerInfo.name,
        customerEmail: customerInfo.email,
        paymentId: pendingPayment.id
      },
      customerEmail: customerInfo.email,
      successUrl: `${process.env.NEXTAUTH_URL}/marketplace/success?payment_intent={PAYMENT_INTENT_ID}&orders=${orderIds.join(',')}`,
      cancelUrl: `${process.env.NEXTAUTH_URL}/marketplace/checkout`
    }

    // Processar pagamento
    const result = await paymentSystem.processPayment(
      paymentMethod as 'card' | 'multibanco',
      paymentRequest,
      pendingPayment
    )

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || 'Erro ao processar pagamento' },
        { status: 500 }
      )
    }

    // Limpar carrinho após criar encomendas
    await prisma.cartItem.deleteMany({
      where: { userId: session.user.id }
    })

    // Retornar resposta baseada no método de pagamento
    if (paymentMethod === 'card') {
      return NextResponse.json({
        success: true,
        paymentMethod: 'card',
        orderIds,
        checkoutUrl: result.checkoutUrl
      })
    } else {
      return NextResponse.json({
        success: true,
        paymentMethod: 'multibanco',
        orderIds,
        multibanco: result.multibanco,
        paymentIntent: result.paymentIntent,
        redirectUrl: result.redirectUrl
      })
    }

  } catch (error) {
    console.error('Erro no checkout marketplace V3:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
