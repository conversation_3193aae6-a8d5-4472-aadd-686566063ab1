const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugAdminPayments() {
  console.log('🔍 DEBUGANDO API ADMIN PAGAMENTOS...\n')

  try {
    // 1. Verificar quantos pagamentos existem
    console.log('1️⃣ VERIFICANDO TOTAL DE PAGAMENTOS')
    const totalPayments = await prisma.paymentHistory.count()
    console.log(`📊 Total de pagamentos na base de dados: ${totalPayments}`)

    if (totalPayments === 0) {
      console.log('❌ PROBLEMA: Nenhum pagamento encontrado na base de dados')
      console.log('   Isso explica por que a API não retorna resultados')
      return
    }

    // 2. Verificar pagamentos por status
    console.log('\n2️⃣ VERIFICANDO PAGAMENTOS POR STATUS')
    const paymentsByStatus = await prisma.paymentHistory.groupBy({
      by: ['status'],
      _count: { id: true }
    })

    paymentsByStatus.forEach(group => {
      console.log(`   ${group.status}: ${group._count.id} pagamentos`)
    })

    // 3. Verificar pagamentos por tipo
    console.log('\n3️⃣ VERIFICANDO PAGAMENTOS POR TIPO')
    const paymentsByType = await prisma.paymentHistory.groupBy({
      by: ['paymentType'],
      _count: { id: true }
    })

    paymentsByType.forEach(group => {
      console.log(`   ${group.paymentType}: ${group._count.id} pagamentos`)
    })

    // 4. Verificar últimos 5 pagamentos
    console.log('\n4️⃣ ÚLTIMOS 5 PAGAMENTOS')
    const recentPayments = await prisma.paymentHistory.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: {
        user: {
          select: { name: true, email: true }
        },
        subscription: {
          select: {
            user: {
              select: { name: true, email: true }
            }
          }
        }
      }
    })

    recentPayments.forEach((payment, index) => {
      console.log(`   ${index + 1}. ${payment.createdAt.toISOString().split('T')[0]} - €${payment.amount} - ${payment.status} - ${payment.paymentType}`)
      console.log(`      Cliente: ${payment.user?.name || payment.customerEmail || 'N/A'}`)
      console.log(`      Stripe ID: ${payment.stripePaymentIntentId || 'N/A'}`)
    })

    // 5. Testar a query da API
    console.log('\n5️⃣ TESTANDO QUERY DA API ADMIN PAGAMENTOS')
    
    const page = 1
    const limit = 10
    const where = {} // Sem filtros

    const [payments, total] = await Promise.all([
      prisma.paymentHistory.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              profile: {
                select: {
                  phone: true
                }
              }
            }
          },
          subscription: {
            select: {
              id: true,
              user: {
                select: {
                  name: true,
                  email: true,
                  repairShop: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            }
          }
        }
      }),
      prisma.paymentHistory.count({ where })
    ])

    console.log(`   📊 Query retornou: ${payments.length} pagamentos de ${total} total`)

    if (payments.length > 0) {
      console.log('   ✅ A query da API está funcionando')
      
      // Processar dados como a API faz
      const processedPayments = payments.map(payment => {
        // Determinar método de pagamento
        let paymentMethodType = 'N/A'
        if (payment.metadata) {
          const metadata = typeof payment.metadata === 'string' 
            ? JSON.parse(payment.metadata) 
            : payment.metadata
          
          if (metadata.payment_method_types) {
            paymentMethodType = Array.isArray(metadata.payment_method_types) 
              ? metadata.payment_method_types[0] 
              : metadata.payment_method_types
          } else if (metadata.paymentMethod) {
            paymentMethodType = metadata.paymentMethod
          }
        }

        // Determinar nome do cliente
        let customerName = 'N/A'
        let customerEmail = payment.customerEmail || 'N/A'
        
        if (payment.user) {
          customerName = payment.user.name || 'N/A'
          customerEmail = payment.user.email || customerEmail
        } else if (payment.subscription?.user) {
          customerName = payment.subscription.user.name || 'N/A'
          customerEmail = payment.subscription.user.email || customerEmail
        }

        // Determinar nome da loja
        let lojistName = 'N/A'
        if (payment.subscription?.user?.repairShop) {
          lojistName = payment.subscription.user.repairShop.name || 'N/A'
        } else if (payment.paymentType === 'SUBSCRIPTION' && payment.subscription?.user) {
          lojistName = payment.subscription.user.name || 'N/A'
        }

        return {
          id: payment.id,
          amount: payment.amount,
          status: payment.status,
          paymentType: payment.paymentType,
          createdAt: payment.createdAt,
          paymentMethodType,
          customerName,
          customerEmail,
          lojistName
        }
      })

      console.log('\n   📋 DADOS PROCESSADOS:')
      processedPayments.forEach((payment, index) => {
        console.log(`      ${index + 1}. €${payment.amount} - ${payment.paymentMethodType} - ${payment.customerName} - ${payment.lojistName}`)
      })

    } else {
      console.log('   ❌ A query não retornou resultados')
    }

    // 6. Verificar se há problemas de relacionamento
    console.log('\n6️⃣ VERIFICANDO RELACIONAMENTOS')
    
    const paymentsWithUser = await prisma.paymentHistory.count({
      where: { userId: { not: null } }
    })
    
    const paymentsWithSubscription = await prisma.paymentHistory.count({
      where: { subscriptionId: { not: null } }
    })

    console.log(`   👤 Pagamentos com userId: ${paymentsWithUser}`)
    console.log(`   📋 Pagamentos com subscriptionId: ${paymentsWithSubscription}`)
    console.log(`   🔗 Pagamentos sem relacionamentos: ${totalPayments - Math.max(paymentsWithUser, paymentsWithSubscription)}`)

    // 7. Verificar reconciliação
    console.log('\n7️⃣ VERIFICANDO STATUS DE RECONCILIAÇÃO')
    
    const reconciledPayments = await prisma.paymentHistory.count({
      where: { reconciledAt: { not: null } }
    })
    
    const pendingReconciliation = await prisma.paymentHistory.count({
      where: {
        OR: [
          { status: 'PENDING' },
          { status: 'PROCESSING' },
          { reconciledAt: null }
        ]
      }
    })

    console.log(`   ✅ Pagamentos reconciliados: ${reconciledPayments}`)
    console.log(`   ⏳ Pagamentos pendentes de reconciliação: ${pendingReconciliation}`)

    if (pendingReconciliation > 0) {
      console.log('   ⚠️ Há pagamentos pendentes de reconciliação')
      console.log('   💡 Execute a reconciliação para atualizar os dados')
    }

    console.log('\n🎯 RESUMO:')
    console.log('=' .repeat(50))
    
    if (totalPayments === 0) {
      console.log('❌ PROBLEMA: Nenhum pagamento na base de dados')
      console.log('   SOLUÇÃO: Verificar se os webhooks do Stripe estão funcionando')
    } else if (payments.length === 0) {
      console.log('❌ PROBLEMA: Query da API não retorna resultados')
      console.log('   SOLUÇÃO: Verificar filtros ou condições da query')
    } else {
      console.log('✅ API deve estar funcionando corretamente')
      console.log('   Se não vê resultados no frontend, verificar:')
      console.log('   1. Autenticação (deve ser ADMIN)')
      console.log('   2. Cache do navegador (Ctrl+F5)')
      console.log('   3. Console do navegador para erros')
    }

  } catch (error) {
    console.error('❌ Erro ao debugar:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugAdminPayments()
