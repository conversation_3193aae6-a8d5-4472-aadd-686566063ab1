import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const problemTypeId = searchParams.get('problemTypeId')
    const deviceId = searchParams.get('deviceId')

    if (!categoryId || !problemTypeId) {
      return NextResponse.json(
        { message: 'Categoria e tipo de problema são obrigatórios' },
        { status: 400 }
      )
    }

    console.log('🔍 Buscando estimativa para:', { categoryId, problemTypeId, deviceId })

    // Buscar preços dos lojistas para esta combinação
    let repairShopPrices = []

    if (deviceId) {
      // Buscar preços específicos para o modelo do dispositivo
      console.log('🎯 Buscando preços por deviceModelId:', deviceId)
      repairShopPrices = await prisma.repairShopPrice.findMany({
        where: {
          deviceModelId: deviceId,
          problemTypeId: problemTypeId,
          isActive: true
        },
        include: {
          repairShop: {
            include: {
              profile: true
            }
          }
        }
      })
      console.log('📊 Preços encontrados por deviceModelId:', repairShopPrices.length)
    }

    // Se não houver preços específicos para o modelo, buscar preços por categoria
    if (repairShopPrices.length === 0) {
      console.log('🔄 Buscando preços por categoryId (sem deviceModelId):', categoryId)
      repairShopPrices = await prisma.repairShopPrice.findMany({
        where: {
          categoryId: categoryId,
          problemTypeId: problemTypeId,
          deviceModelId: null, // Preços gerais por categoria
          isActive: true
        },
        include: {
          repairShop: {
            include: {
              profile: true
            }
          }
        }
      })
      console.log('📊 Preços encontrados por categoryId (sem deviceModelId):', repairShopPrices.length)
    }

    // Se ainda não encontrou, buscar por problemTypeId apenas (qualquer categoria/modelo)
    if (repairShopPrices.length === 0) {
      console.log('🔄 Buscando preços apenas por problemTypeId:', problemTypeId)
      repairShopPrices = await prisma.repairShopPrice.findMany({
        where: {
          problemTypeId: problemTypeId,
          isActive: true
        },
        include: {
          repairShop: {
            include: {
              profile: true
            }
          },
          deviceModel: {
            include: {
              brand: true,
              category: true
            }
          }
        }
      })
      console.log('📊 Preços encontrados por problemTypeId:', repairShopPrices.length)
    }

    // Se ainda não encontrou, buscar por tipos de problema similares (ecrã)
    if (repairShopPrices.length === 0) {
      console.log('🔄 Buscando preços por tipos de problema similares')

      // Buscar tipo de problema atual
      const currentProblemType = await prisma.problemType.findUnique({
        where: { id: problemTypeId }
      })

      if (currentProblemType && currentProblemType.name.toLowerCase().includes('ecrã')) {
        // Buscar outros problemas de ecrã
        const similarProblems = await prisma.problemType.findMany({
          where: {
            OR: [
              { name: { contains: 'ecrã', mode: 'insensitive' } },
              { name: { contains: 'screen', mode: 'insensitive' } }
            ]
          }
        })

        const similarProblemIds = similarProblems.map(p => p.id)
        console.log('🔍 Problemas similares encontrados:', similarProblems.map(p => p.name))

        repairShopPrices = await prisma.repairShopPrice.findMany({
          where: {
            problemTypeId: { in: similarProblemIds },
            isActive: true
          },
          include: {
            repairShop: {
              include: {
                profile: true
              }
            },
            deviceModel: {
              include: {
                brand: true,
                category: true
              }
            }
          }
        })
        console.log('📊 Preços encontrados por problemas similares:', repairShopPrices.length)
      }
    }

    console.log('✅ Total de preços encontrados:', repairShopPrices.length)

    if (repairShopPrices.length === 0) {
      // Se não houver preços de lojistas, usar preços base como fallback
      const basePrice = await prisma.basePrice.findFirst({
        where: {
          categoryId: categoryId,
          problemTypeId: problemTypeId
        }
      })

      if (basePrice) {
        return NextResponse.json({
          averagePrice: basePrice.basePrice,
          averageTime: basePrice.estimatedTime,
          priceRange: {
            min: basePrice.basePrice * 0.8,
            max: basePrice.basePrice * 1.2
          },
          availableShops: 0,
          source: 'base_price'
        })
      } else {
        // Implementar lógica inteligente baseada no tipo de problema e categoria
        const intelligentEstimate = await calculateIntelligentEstimate(categoryId, problemTypeId)

        return NextResponse.json({
          averagePrice: intelligentEstimate.price,
          averageTime: intelligentEstimate.time,
          priceRange: {
            min: intelligentEstimate.price * 0.7,
            max: intelligentEstimate.price * 1.4
          },
          availableShops: 0,
          source: 'intelligent_estimate'
        })
      }
    }

    // Calcular médias dos preços dos lojistas
    const prices = repairShopPrices.map(price => price.price)
    const times = repairShopPrices.map(price => price.estimatedTime)

    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length

    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    return NextResponse.json({
      averagePrice: Math.round(averagePrice * 100) / 100, // Arredondar para 2 casas decimais
      averageTime: Math.round(averageTime),
      priceRange: {
        min: minPrice,
        max: maxPrice
      },
      availableShops: repairShopPrices.length,
      source: 'repair_shops'
    })

  } catch (error) {
    console.error('Erro ao calcular estimativa:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Função para calcular estimativa inteligente baseada em dados históricos e padrões
async function calculateIntelligentEstimate(categoryId: string, problemTypeId: string) {
  try {
    // Buscar informações da categoria e tipo de problema
    const [category, problemType] = await Promise.all([
      prisma.category.findUnique({ where: { id: categoryId } }),
      prisma.problemType.findUnique({ where: { id: problemTypeId } })
    ])

    let basePrice = 50 // Preço base padrão
    let baseTime = 120 // 2 horas padrão

    // Ajustar preço baseado na categoria
    if (category) {
      const categoryName = category.name.toLowerCase()

      if (categoryName.includes('smartphone') || categoryName.includes('telemóvel')) {
        basePrice = 80
        baseTime = 90
      } else if (categoryName.includes('tablet')) {
        basePrice = 100
        baseTime = 120
      } else if (categoryName.includes('laptop') || categoryName.includes('portátil')) {
        basePrice = 150
        baseTime = 180
      } else if (categoryName.includes('desktop') || categoryName.includes('computador')) {
        basePrice = 120
        baseTime = 150
      } else if (categoryName.includes('smartwatch') || categoryName.includes('relógio')) {
        basePrice = 60
        baseTime = 60
      } else if (categoryName.includes('console') || categoryName.includes('gaming')) {
        basePrice = 130
        baseTime = 150
      }
    }

    // Ajustar preço baseado no tipo de problema
    if (problemType) {
      const problemName = problemType.name.toLowerCase()

      if (problemName.includes('ecrã') || problemName.includes('screen') || problemName.includes('display')) {
        basePrice *= 1.5 // Problemas de ecrã são mais caros
        baseTime *= 1.2
      } else if (problemName.includes('bateria') || problemName.includes('battery')) {
        basePrice *= 0.8 // Bateria é mais barata
        baseTime *= 0.7
      } else if (problemName.includes('água') || problemName.includes('water') || problemName.includes('líquido')) {
        basePrice *= 1.8 // Danos por água são complexos
        baseTime *= 1.5
      } else if (problemName.includes('motherboard') || problemName.includes('placa') || problemName.includes('logic')) {
        basePrice *= 2.0 // Problemas de placa são caros
        baseTime *= 1.8
      } else if (problemName.includes('software') || problemName.includes('sistema')) {
        basePrice *= 0.6 // Software é mais barato
        baseTime *= 0.5
      } else if (problemName.includes('touch') || problemName.includes('táctil')) {
        basePrice *= 1.3 // Touch é moderadamente caro
        baseTime *= 1.1
      }
    }

    // Buscar preços similares na base de dados para calibrar
    const similarPrices = await prisma.repairShopPrice.findMany({
      where: {
        OR: [
          { categoryId: categoryId },
          { problemTypeId: problemTypeId }
        ],
        isActive: true
      },
      select: {
        price: true,
        estimatedTime: true
      }
    })

    // Se há preços similares, usar como referência
    if (similarPrices.length > 0) {
      const avgSimilarPrice = similarPrices.reduce((sum, p) => sum + p.price, 0) / similarPrices.length
      const avgSimilarTime = similarPrices.reduce((sum, p) => sum + p.estimatedTime, 0) / similarPrices.length

      // Usar média ponderada entre estimativa inteligente e preços similares
      basePrice = Math.round((basePrice * 0.6 + avgSimilarPrice * 0.4))
      baseTime = Math.round((baseTime * 0.6 + avgSimilarTime * 0.4))
    }

    console.log(`🧠 Estimativa inteligente calculada: €${basePrice}, ${baseTime}min`)
    console.log(`   Categoria: ${category?.name}, Problema: ${problemType?.name}`)
    console.log(`   Preços similares encontrados: ${similarPrices.length}`)

    return {
      price: Math.round(basePrice),
      time: Math.round(baseTime)
    }

  } catch (error) {
    console.error('Erro ao calcular estimativa inteligente:', error)
    return {
      price: 50,
      time: 120
    }
  }
}
