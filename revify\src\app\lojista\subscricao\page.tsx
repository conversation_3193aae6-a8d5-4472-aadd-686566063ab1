'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  CreditCard,
  Calendar,
  Download,
  AlertTriangle,
  CheckCircle,
  Crown,
  ArrowUp,
  Package,
  Plus
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import SubscriptionPaymentForm from '@/components/lojista/SubscriptionPaymentForm'

interface Subscription {
  id: string
  status: string
  billingCycle: string
  currentPeriodStart: string
  currentPeriodEnd: string
  cancelAtPeriodEnd: boolean
  canceledAt: string | null
  plan: {
    id: string
    name: string
    monthlyPrice: number
    yearlyPrice: number
    features: string[]
  }
  payments: {
    id: string
    amount: number
    status: string
    createdAt: string
    periodStart: string
    periodEnd: string
    multibancoEntity?: string
    multibancoReference?: string
  }[]
}

export default function GestaoSubscricaoPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [stripeData, setStripeData] = useState<any>(null)
  const [invoices, setInvoices] = useState<any[]>([])
  const [paymentMethods, setPaymentMethods] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCanceling, setIsCanceling] = useState(false)
  const [showPaymentOptions, setShowPaymentOptions] = useState<string | null>(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)

  useEffect(() => {
    fetchSubscription()
    fetchPaymentMethods()
  }, [])

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/lojista/subscription')
      if (response.ok) {
        const data = await response.json()
        setSubscription(data.subscription)
        setStripeData(data.stripeData)
        setInvoices(data.invoices || [])

        console.log('📊 Dados carregados:', {
          subscription: !!data.subscription,
          stripeData: !!data.stripeData,
          invoicesCount: data.invoices?.length || 0
        })
      }
    } catch (error) {
      console.error('Erro ao buscar subscrição:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const response = await fetch('/api/lojista/subscription/payment-methods')
      if (response.ok) {
        const data = await response.json()
        setPaymentMethods(data.paymentMethods || [])
      }
    } catch (error) {
      console.error('Erro ao buscar métodos de pagamento:', error)
    }
  }

  const cancelSubscription = async () => {
    if (!subscription) return

    if (!confirm('Tem certeza que deseja cancelar a sua subscrição? Esta ação não pode ser desfeita.')) {
      return
    }

    setIsCanceling(true)
    try {
      const response = await fetch(`/api/lojista/subscription/${subscription.id}/cancel`, {
        method: 'POST'
      })

      if (response.ok) {
        alert('Subscrição cancelada com sucesso!')
        fetchSubscription()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao cancelar subscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar subscrição:', error)
      alert('Erro ao cancelar subscrição')
    } finally {
      setIsCanceling(false)
    }
  }

  const handlePayment = async (paymentId: string, paymentMethod: 'card' | 'multibanco' = 'card') => {
    try {
      const response = await fetch('/api/lojista/subscription/pay-unified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          paymentId,
          paymentMethod 
        })
      })

      const data = await response.json()

      if (response.ok) {
        if (data.type === 'multibanco_reference' && data.redirectUrl) {
          // Para Multibanco, redirecionar para página de sucesso com referência
          router.push(data.redirectUrl)
        } else if (data.checkoutUrl) {
          // Para Stripe Checkout
          window.location.href = data.checkoutUrl
        } else if (data.type === 'payment_intent') {
          // Para PaymentIntent, abrir modal de pagamento
          setShowPaymentModal(true)
        } else {
          alert('Tipo de pagamento não reconhecido')
        }
      } else {
        console.error('Erro no pagamento:', data)
        alert(data.message || 'Erro ao processar pagamento')
      }
    } catch (error) {
      console.error('Erro ao processar pagamento:', error)
      alert('Erro ao processar pagamento')
    }
  }

  const handleRetryPayment = () => {
    setShowPaymentModal(true)
  }

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false)
    fetchSubscription() // Recarregar dados
  }

  const handlePaymentCancel = () => {
    setShowPaymentModal(false)
  }



  const handleChangePaymentMethod = async (subscriptionId: string, newMethod: 'card' | 'multibanco') => {
    try {
      const response = await fetch('/api/lojista/subscription/change-payment-method', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscriptionId,
          paymentMethod: newMethod
        })
      })

      const data = await response.json()

      if (response.ok) {
        if (data.checkoutUrl) {
          window.location.href = data.checkoutUrl
        } else if (data.redirectUrl) {
          router.push(data.redirectUrl)
        } else {
          // Recarregar dados
          fetchSubscription()
        }
      } else {
        alert(data.message || 'Erro ao alterar método de pagamento')
      }
    } catch (error) {
      console.error('Erro ao alterar método de pagamento:', error)
      alert('Erro ao alterar método de pagamento')
    }
  }

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'ACTIVE': 'Ativa',
      'PAST_DUE': 'Em Atraso',
      'CANCELED': 'Cancelada',
      'INCOMPLETE': 'Pagamento Pendente',
      'TRIALING': 'Período de Teste',
      'UNPAID': 'Não Paga'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'PAST_DUE': 'bg-yellow-100 text-yellow-800',
      'CANCELED': 'bg-red-100 text-red-800',
      'INCOMPLETE': 'bg-yellow-100 text-yellow-800',
      'TRIALING': 'bg-blue-100 text-blue-800',
      'UNPAID': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getCurrentPrice = () => {
    try {
      if (!subscription || !subscription.plan) {
        console.warn('getCurrentPrice: subscription ou plan não definido', { subscription })
        return 0
      }
      return subscription.billingCycle === 'MONTHLY'
        ? subscription.plan.monthlyPrice
        : subscription.plan.yearlyPrice
    } catch (error) {
      console.error('Erro em getCurrentPrice:', error)
      return 0
    }
  }

  const handleBillingPortal = async () => {
    try {
      const response = await fetch('/api/lojista/subscription/billing-portal', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        window.open(data.url, '_blank')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao abrir portal de faturação')
      }
    } catch (error) {
      console.error('Erro ao abrir billing portal:', error)
      alert('Erro ao abrir portal de faturação')
    }
  }

  const handleDownloadInvoice = async (paymentId: string) => {
    try {
      const response = await fetch(`/api/lojista/subscription/download-invoice?payment_id=${paymentId}`)

      if (response.ok) {
        const data = await response.json()
        if (data.pdfUrl) {
          window.open(data.pdfUrl, '_blank')
        } else {
          alert('PDF da fatura não disponível')
        }
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao descarregar fatura')
      }
    } catch (error) {
      console.error('Erro ao descarregar fatura:', error)
      alert('Erro ao descarregar fatura')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!subscription) {
    console.warn('Componente: subscription não definido', { subscription, isLoading })
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gestão de Subscrição</h1>
            <p className="text-muted-foreground">
              Gerir a sua subscrição e pagamentos
            </p>
          </div>
          <Link href="/lojista">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </Link>
        </div>

        <Card className="text-center">
          <CardHeader>
            <CreditCard className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <CardTitle className="text-2xl">Nenhuma Subscrição Ativa</CardTitle>
            <CardDescription>
              Subscreva um plano para desbloquear funcionalidades avançadas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/lojista/upgrade">
              <Button>
                <Crown className="w-4 h-4 mr-2" />
                Ver Planos
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gestão de Subscrição</h1>
            <p className="text-muted-foreground">
              Gerir a sua subscrição e pagamentos
            </p>
          </div>
          <Link href="/lojista">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </Link>
        </div>

        <div className="grid gap-6">
          {/* Subscription Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Crown className="w-8 h-8 text-yellow-500 mr-3" />
                  <div>
                    <CardTitle className="text-xl">{subscription?.plan?.name || 'Plano não definido'}</CardTitle>
                    <CardDescription>Plano atual</CardDescription>
                  </div>
                </div>
                <Badge className={getStatusColor(stripeData?.status || subscription.status)}>
                  {getStatusLabel(stripeData?.status || subscription.status)}
                  {stripeData && <span className="ml-1 text-xs">(Stripe)</span>}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>

            {subscription.status === 'INCOMPLETE' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
                  <div className="w-full">
                    <h3 className="font-medium text-yellow-800 mb-1">Pagamento Pendente</h3>
                    <p className="text-sm text-yellow-700 mb-3">
                      A sua subscrição será ativada automaticamente após a confirmação do pagamento.
                    </p>
                    {subscription.payments.length > 0 && subscription.payments[0].status === 'PENDING' && (
                      <div className="text-sm text-yellow-700">
                        {subscription.payments[0].multibancoEntity && subscription.payments[0].multibancoReference ? (
                          <div className="mb-4">
                            <div className="bg-white p-3 rounded border mb-3">
                              <strong>Dados para pagamento Multibanco:</strong><br />
                              <div className="mt-2 font-mono">
                                <div>Entidade: <strong>{subscription.payments[0].multibancoEntity}</strong></div>
                                <div>Referência: <strong>{subscription.payments[0].multibancoReference}</strong></div>
                                <div>Valor: <strong>€{Number(subscription.payments[0].amount).toFixed(2)}</strong></div>
                              </div>
                            </div>
                            <div className="flex flex-wrap gap-2">
                              <button
                                onClick={() => handlePayment(subscription.payments[0].id, 'card')}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
                              >
                                <CreditCard className="w-4 h-4 inline mr-1" />
                                Pagar com Cartão
                              </button>
                              <button
                                onClick={() => handlePayment(subscription.payments[0].id, 'multibanco')}
                                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm"
                              >
                                🏧 Nova Referência MB
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-wrap gap-2">
                            <button
                              onClick={() => handlePayment(subscription.payments[0].id, 'card')}
                              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
                            >
                              <CreditCard className="w-4 h-4 inline mr-1" />
                              Pagar com Cartão
                            </button>
                            <button
                              onClick={() => handlePayment(subscription.payments[0].id, 'multibanco')}
                              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm"
                            >
                              🏧 Gerar Referência MB
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  €{Number(getCurrentPrice()).toFixed(2)}
                </div>
                <div className="text-sm text-gray-600">
                  {subscription.billingCycle === 'MONTHLY' ? 'por mês' : 'por ano'}
                </div>
              </div>

              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
                </div>
                <div className="text-sm text-gray-600">Próxima renovação</div>
              </div>

              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}
                </div>
                <div className="text-sm text-gray-600">Ciclo de faturação</div>
              </div>
            </div>

            {/* Ações da Subscrição */}
            <div className="flex flex-wrap gap-2 mt-6">
              <button
                onClick={handleBillingPortal}
                className="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <CreditCard className="w-3 h-3 mr-1" />
                Gerir Subscrição
              </button>
              <Link
                href="/lojista/upgrade"
                className="inline-flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                <ArrowUp className="w-3 h-3 mr-1" />
                Fazer Upgrade
              </Link>
              {subscription.status === 'ACTIVE' && !subscription.cancelAtPeriodEnd && (
                <button
                  onClick={cancelSubscription}
                  disabled={isCanceling}
                  className="inline-flex items-center px-3 py-2 text-sm text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors disabled:opacity-50"
                >
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  {isCanceling ? 'Cancelando...' : 'Cancelar'}
                </button>
              )}
            </div>

            {subscription.cancelAtPeriodEnd && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      Subscrição será cancelada em {new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
                    </p>
                    <p className="text-sm text-yellow-700">
                      Pode continuar a usar o serviço até ao final do período pago.
                    </p>
                  </div>
                </div>
              </div>
            )}
            </CardContent>
          </Card>

          {/* Plan Features */}
          <Card>
            <CardHeader>
              <CardTitle>Funcionalidades do Plano</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {subscription?.plan?.features && subscription.plan.features.length > 0 ?
                  subscription.plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  )) : (
                    <div className="text-muted-foreground">Nenhuma funcionalidade específica listada</div>
                  )
                }
              </div>
            </CardContent>
          </Card>

          {/* Apps Addon */}
          <AppsAddonSection 
            subscription={subscription}
            handlePaymentSuccess={handlePaymentSuccess}
            handlePaymentCancel={handlePaymentCancel}
            showPaymentModal={showPaymentModal}
          />

          {/* Faturas do Stripe */}
          {invoices && invoices.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Faturas</CardTitle>
                <CardDescription>
                  Histórico de faturas da sua subscrição
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">
                          €{invoice.amount_due.toFixed(2)} {invoice.currency.toUpperCase()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(invoice.created * 1000).toLocaleDateString('pt-PT')}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={invoice.status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                          {invoice.status === 'paid' ? 'Paga' : 'Pendente'}
                        </Badge>
                        {invoice.hosted_invoice_url && (
                          <a
                            href={invoice.hosted_invoice_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            Ver Fatura
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle>Métodos de Pagamento</CardTitle>
              <CardDescription>
                Gerir os seus métodos de pagamento salvos
              </CardDescription>
            </CardHeader>
            <CardContent>
              {paymentMethods && paymentMethods.length > 0 ? (
                <div className="space-y-4">
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center">
                        <CreditCard className="w-5 h-5 text-gray-400 mr-3" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {method.card?.brand?.toUpperCase()} •••• {method.card?.last4}
                          </div>
                          <div className="text-sm text-gray-600">
                            Expira em {method.card?.exp_month}/{method.card?.exp_year}
                          </div>
                        </div>
                      </div>
                      <Badge variant="secondary">
                        {method.card?.funding === 'credit' ? 'Crédito' : 'Débito'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Nenhum método de pagamento salvo</p>
                  <p className="text-sm">Os métodos de pagamento serão salvos automaticamente após o primeiro pagamento</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Histórico de Pagamentos</h3>

            {subscription.payments && subscription.payments.length > 0 ? (
              <div className="space-y-4">
                {subscription.payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      <CreditCard className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="font-medium text-gray-900">
                          €{Number(payment.amount).toFixed(2)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {new Date(payment.createdAt).toLocaleDateString('pt-PT')} às {new Date(payment.createdAt).toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' })}
                        </div>
                        <div className="text-xs text-gray-500">
                          Período: {new Date(payment.periodStart).toLocaleDateString('pt-PT')} a {new Date(payment.periodEnd).toLocaleDateString('pt-PT')}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        payment.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                        payment.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {payment.status === 'COMPLETED' ? 'Pago' :
                         payment.status === 'PENDING' ? 'Pendente' : 'Falhado'}
                      </span>
                      {payment.status === 'PENDING' ? (
                        <div className="flex items-center space-x-2">
                          {payment.multibancoEntity && payment.multibancoReference ? (
                            <div className="text-xs text-gray-600">
                              <div>Entidade: {payment.multibancoEntity}</div>
                              <div>Referência: {payment.multibancoReference}</div>
                            </div>
                          ) : (
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handlePayment(payment.id, 'card')}
                                className="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700"
                                title="Pagar com cartão"
                              >
                                💳 Cartão
                              </button>
                              <button
                                onClick={() => handlePayment(payment.id, 'multibanco')}
                                className="px-3 py-1 bg-green-600 text-white text-xs rounded-lg hover:bg-green-700"
                                title="Pagar com Multibanco"
                              >
                                🏧 MB
                              </button>
                            </div>
                          )}
                          <div className="relative">
                            <button
                              onClick={() => setShowPaymentOptions(showPaymentOptions === payment.id ? null : payment.id)}
                              className="px-2 py-1 text-xs text-gray-600 hover:text-gray-800"
                            >
                              Alterar método
                            </button>
                            {showPaymentOptions === payment.id && (
                              <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[150px]">
                                <button
                                  onClick={() => {
                                    handlePayment(payment.id, 'card')
                                    setShowPaymentOptions(null)
                                  }}
                                  className="block w-full text-left px-3 py-2 text-xs hover:bg-gray-50"
                                >
                                  💳 Cartão
                                </button>
                                <button
                                  onClick={() => {
                                    handlePayment(payment.id, 'multibanco')
                                    setShowPaymentOptions(null)
                                  }}
                                  className="block w-full text-left px-3 py-2 text-xs hover:bg-gray-50"
                                >
                                  🏧 Multibanco
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <button
                          onClick={() => handleDownloadInvoice(payment.id)}
                          className="text-gray-600 hover:text-gray-800 transition-colors"
                          title="Descarregar fatura"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">Nenhum pagamento registado</p>
            )}
          </div>


        </div>
      </div>
  )
}

// Apps Addon Section Component
interface AppsAddonSectionProps {
  subscription: Subscription | null
  handlePaymentSuccess: () => void
  handlePaymentCancel: () => void
  showPaymentModal: boolean
}

function AppsAddonSection({
  subscription,
  handlePaymentSuccess,
  handlePaymentCancel,
  showPaymentModal
}: AppsAddonSectionProps) {
  const [installedApps, setInstalledApps] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchInstalledApps()
  }, [])

  const fetchInstalledApps = async () => {
    try {
      const response = await fetch('/api/lojista/apps/installed')
      if (response.ok) {
        const data = await response.json()
        setInstalledApps(data.apps || [])
      }
    } catch (error) {
      console.error('Erro ao carregar apps instaladas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const totalAddonCost = installedApps
    .filter(app => app.isPaid)
    .reduce((sum, app) => sum + (app.monthlyPrice || 0), 0)

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Apps Instaladas</h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Apps Instaladas</h3>
        <Link
          href="/lojista/appstore"
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Ver App Store
        </Link>
      </div>

      {installedApps.length === 0 ? (
        <div className="text-center py-8">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">Nenhuma app instalada</p>
          <Link
            href="/lojista/appstore"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Explorar Apps
          </Link>
        </div>
      ) : (
        <>
          <div className="space-y-3 mb-6">
            {installedApps.map((app) => (
              <div key={app.appId} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <Package className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{app.name}</div>
                    <div className="text-sm text-gray-500">
                      {app.isPaid ? `€${app.monthlyPrice?.toFixed(2)}/mês` : 'Grátis'}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {app.isTrial && (
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      Trial
                    </span>
                  )}
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    app.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {app.isActive ? 'Ativa' : 'Inativa'}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {totalAddonCost > 0 && (
            <div className="border-t border-gray-200 pt-4">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900">Total Apps (addon ao plano):</span>
                <span className="text-lg font-bold text-gray-900">
                  €{totalAddonCost.toFixed(2)}/mês
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Este valor será adicionado à sua próxima fatura
              </p>
            </div>
          )}
        </>
      )}

      {/* Modal de Pagamento */}
      {subscription && (
        <SubscriptionPaymentForm
          subscription={subscription}
          pendingPayment={subscription.payments?.[0]}
          onSuccess={handlePaymentSuccess}
          onCancel={handlePaymentCancel}
          isOpen={showPaymentModal}
        />
      )}
    </div>
  )
}
