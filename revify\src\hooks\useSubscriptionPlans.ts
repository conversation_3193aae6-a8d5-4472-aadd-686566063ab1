import { useState, useEffect } from 'react'

export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  isPopular?: boolean
  color: string
  maxRepairs?: number
  maxProducts?: number
  maxUsers?: number
  hasAnalytics?: boolean
  hasAPI?: boolean
  hasPrioritySupport?: boolean
}

export const DEFAULT_PLANS: SubscriptionPlan[] = [
  {
    id: 'basic',
    name: 'Básico',
    description: 'Perfeito para começar',
    monthlyPrice: 19.99,
    yearlyPrice: 199.99,
    color: 'from-gray-500 to-gray-600',
    features: [
      'Até 50 reparações por mês',
      'Gestão básica de clientes',
      'Relatórios simples',
      'Suporte por email',
      '1 utilizador'
    ],
    maxRepairs: 50,
    maxProducts: 100,
    maxUsers: 1,
    hasAnalytics: false,
    hasAPI: false,
    hasPrioritySupport: false
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'Para negócios em crescimento',
    monthlyPrice: 49.99,
    yearlyPrice: 499.99,
    color: 'from-blue-500 to-blue-600',
    isPopular: true,
    features: [
      'Reparações ilimitadas',
      'Gestão avançada de clientes',
      'Relatórios detalhados',
      'Integração WhatsApp',
      'Até 5 utilizadores',
      'Backup automático',
      'Suporte prioritário'
    ],
    maxRepairs: -1, // Ilimitado
    maxProducts: 1000,
    maxUsers: 5,
    hasAnalytics: true,
    hasAPI: false,
    hasPrioritySupport: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Para grandes empresas',
    monthlyPrice: 99.99,
    yearlyPrice: 999.99,
    color: 'from-purple-500 to-purple-600',
    features: [
      'Tudo do Premium',
      'API completa',
      'Integrações personalizadas',
      'Utilizadores ilimitados',
      'Suporte 24/7',
      'Gestor de conta dedicado',
      'Relatórios personalizados',
      'White-label disponível'
    ],
    maxRepairs: -1,
    maxProducts: -1,
    maxUsers: -1,
    hasAnalytics: true,
    hasAPI: true,
    hasPrioritySupport: true
  }
]

export function useSubscriptionPlans() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>(DEFAULT_PLANS)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPlans = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/subscription/plans')
      
      if (response.ok) {
        const data = await response.json()
        if (data.plans && Array.isArray(data.plans)) {
          setPlans(data.plans)
        }
      } else {
        // Se a API não existir ou falhar, usar planos padrão
        console.warn('API de planos não disponível, usando planos padrão')
        setPlans(DEFAULT_PLANS)
      }
    } catch (error) {
      console.warn('Erro ao carregar planos, usando planos padrão:', error)
      setPlans(DEFAULT_PLANS)
      setError('Erro ao carregar planos')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlans()
  }, [])

  const getPlanById = (id: string): SubscriptionPlan | undefined => {
    return plans.find(plan => plan.id === id)
  }

  const getUpgradeOptions = (currentPlanId?: string): SubscriptionPlan[] => {
    if (!currentPlanId) return plans

    const currentPlanIndex = plans.findIndex(plan => plan.id === currentPlanId)
    if (currentPlanIndex === -1) return plans

    // Retornar apenas planos superiores
    return plans.slice(currentPlanIndex + 1)
  }

  const getDowngradeOptions = (currentPlanId?: string): SubscriptionPlan[] => {
    if (!currentPlanId) return []

    const currentPlanIndex = plans.findIndex(plan => plan.id === currentPlanId)
    if (currentPlanIndex === -1) return []

    // Retornar apenas planos inferiores
    return plans.slice(0, currentPlanIndex)
  }

  const calculateSavings = (plan: SubscriptionPlan): number => {
    const monthlyTotal = plan.monthlyPrice * 12
    return monthlyTotal - plan.yearlyPrice
  }

  const calculateSavingsPercentage = (plan: SubscriptionPlan): number => {
    const monthlyTotal = plan.monthlyPrice * 12
    const savings = monthlyTotal - plan.yearlyPrice
    return Math.round((savings / monthlyTotal) * 100)
  }

  const comparePlans = (planA: SubscriptionPlan, planB: SubscriptionPlan) => {
    return {
      priceDifference: {
        monthly: planB.monthlyPrice - planA.monthlyPrice,
        yearly: planB.yearlyPrice - planA.yearlyPrice
      },
      featureComparison: {
        planA: planA.features,
        planB: planB.features,
        newFeatures: planB.features.filter(feature => !planA.features.includes(feature)),
        commonFeatures: planB.features.filter(feature => planA.features.includes(feature))
      },
      limits: {
        repairs: {
          from: planA.maxRepairs,
          to: planB.maxRepairs
        },
        products: {
          from: planA.maxProducts,
          to: planB.maxProducts
        },
        users: {
          from: planA.maxUsers,
          to: planB.maxUsers
        }
      }
    }
  }

  const isUpgrade = (fromPlanId: string, toPlanId: string): boolean => {
    const fromIndex = plans.findIndex(plan => plan.id === fromPlanId)
    const toIndex = plans.findIndex(plan => plan.id === toPlanId)
    
    if (fromIndex === -1 || toIndex === -1) return false
    
    return toIndex > fromIndex
  }

  const getRecommendedPlan = (currentUsage?: {
    monthlyRepairs?: number
    totalProducts?: number
    totalUsers?: number
  }): SubscriptionPlan | null => {
    if (!currentUsage) return plans.find(plan => plan.isPopular) || plans[1]

    // Lógica de recomendação baseada no uso
    for (const plan of plans) {
      const repairLimit = plan.maxRepairs === -1 ? Infinity : plan.maxRepairs
      const productLimit = plan.maxProducts === -1 ? Infinity : plan.maxProducts
      const userLimit = plan.maxUsers === -1 ? Infinity : plan.maxUsers

      if (
        (currentUsage.monthlyRepairs || 0) <= repairLimit * 0.8 && // 80% do limite
        (currentUsage.totalProducts || 0) <= productLimit * 0.8 &&
        (currentUsage.totalUsers || 0) <= userLimit
      ) {
        return plan
      }
    }

    // Se nenhum plano for suficiente, retornar o maior
    return plans[plans.length - 1]
  }

  return {
    plans,
    loading,
    error,
    fetchPlans,
    getPlanById,
    getUpgradeOptions,
    getDowngradeOptions,
    calculateSavings,
    calculateSavingsPercentage,
    comparePlans,
    isUpgrade,
    getRecommendedPlan
  }
}
