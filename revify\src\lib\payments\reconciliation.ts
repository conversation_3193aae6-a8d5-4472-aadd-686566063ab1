import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'
import { recordPaymentHistory, updatePaymentHistoryStatus } from './payment-history'
import Stripe from 'stripe'

export interface ReconciliationResult {
  success: boolean
  reconciledCount: number
  errorCount: number
  newPaymentsFound: number
  updatedPayments: number
  errors: string[]
  summary: string
}

/**
 * Reconciliar todos os pagamentos com o Stripe
 */
export async function reconcileAllPayments(): Promise<ReconciliationResult> {
  const result: ReconciliationResult = {
    success: false,
    reconciledCount: 0,
    errorCount: 0,
    newPaymentsFound: 0,
    updatedPayments: 0,
    errors: [],
    summary: ''
  }

  try {
    console.log('🔄 Iniciando reconciliação completa de pagamentos...')

    // 1. Reconciliar pagamentos existentes
    const existingResult = await reconcileExistingPayments()
    result.reconciledCount += existingResult.reconciledCount
    result.errorCount += existingResult.errorCount
    result.updatedPayments += existingResult.updatedPayments
    result.errors.push(...existingResult.errors)

    // 2. Buscar novos pagamentos no Stripe
    const newPaymentsResult = await findNewPaymentsFromStripe()
    result.newPaymentsFound += newPaymentsResult.newPaymentsFound
    result.errorCount += newPaymentsResult.errorCount
    result.errors.push(...newPaymentsResult.errors)

    // 3. Reconciliar subscrições
    const subscriptionsResult = await reconcileSubscriptionPayments()
    result.reconciledCount += subscriptionsResult.reconciledCount
    result.errorCount += subscriptionsResult.errorCount
    result.errors.push(...subscriptionsResult.errors)

    result.success = result.errorCount === 0 || result.reconciledCount > 0
    result.summary = `Reconciliação concluída: ${result.reconciledCount} atualizados, ${result.newPaymentsFound} novos, ${result.errorCount} erros`

    console.log('✅ Reconciliação completa:', result.summary)
    return result

  } catch (error) {
    console.error('❌ Erro na reconciliação completa:', error)
    result.errors.push(`Erro geral: ${error.message}`)
    result.summary = 'Falha na reconciliação'
    return result
  }
}

/**
 * Reconciliar pagamentos existentes na base de dados
 */
async function reconcileExistingPayments(): Promise<Partial<ReconciliationResult>> {
  const result = {
    reconciledCount: 0,
    errorCount: 0,
    updatedPayments: 0,
    errors: []
  }

  try {
    const stripe = await createStripeInstance()

    // Buscar pagamentos que precisam de reconciliação
    const paymentsToReconcile = await prisma.paymentHistory.findMany({
      where: {
        OR: [
          { status: 'PENDING' },
          { status: 'PROCESSING' },
          { reconciledAt: null },
          {
            AND: [
              { reconciledAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } }, // Mais de 24h
              { status: { in: ['PENDING', 'PROCESSING'] } }
            ]
          }
        ]
      },
      take: 100, // Limitar para não sobrecarregar
      orderBy: { createdAt: 'desc' }
    })

    console.log(`🔍 Encontrados ${paymentsToReconcile.length} pagamentos para reconciliar`)

    for (const payment of paymentsToReconcile) {
      try {
        let stripeData = null
        let newStatus = payment.status
        let failureReason = payment.failureReason

        // Buscar dados no Stripe
        if (payment.stripePaymentIntentId) {
          try {
            stripeData = await stripe.paymentIntents.retrieve(payment.stripePaymentIntentId)
            
            // Mapear status do Stripe para nosso sistema
            switch (stripeData.status) {
              case 'succeeded':
                newStatus = 'SUCCEEDED'
                break
              case 'processing':
                newStatus = 'PROCESSING'
                break
              case 'requires_payment_method':
              case 'requires_confirmation':
              case 'requires_action':
                newStatus = 'PENDING'
                break
              case 'canceled':
                newStatus = 'CANCELLED'
                break
              case 'requires_capture':
                newStatus = 'PROCESSING'
                break
              default:
                if (stripeData.status.includes('failed')) {
                  newStatus = 'FAILED'
                  failureReason = stripeData.last_payment_error?.message || 'Pagamento falhado'
                }
            }

          } catch (piError) {
            console.warn(`PaymentIntent ${payment.stripePaymentIntentId} não encontrado:`, piError.message)
            if (piError.code === 'resource_missing') {
              newStatus = 'FAILED'
              failureReason = 'PaymentIntent não encontrado no Stripe'
            }
          }
        }

        // Se não temos PaymentIntent, tentar Invoice
        if (!stripeData && payment.stripeInvoiceId) {
          try {
            stripeData = await stripe.invoices.retrieve(payment.stripeInvoiceId)
            
            switch (stripeData.status) {
              case 'paid':
                newStatus = 'SUCCEEDED'
                break
              case 'open':
              case 'draft':
                newStatus = 'PENDING'
                break
              case 'void':
              case 'uncollectible':
                newStatus = 'FAILED'
                failureReason = 'Fatura cancelada ou não cobrável'
                break
            }

          } catch (invError) {
            console.warn(`Invoice ${payment.stripeInvoiceId} não encontrada:`, invError.message)
          }
        }

        // Atualizar se houve mudança
        if (newStatus !== payment.status || failureReason !== payment.failureReason) {
          await prisma.paymentHistory.update({
            where: { id: payment.id },
            data: {
              status: newStatus,
              failureReason,
              reconciledAt: new Date()
            }
          })

          result.updatedPayments++
          console.log(`✅ Pagamento ${payment.id} atualizado: ${payment.status} → ${newStatus}`)
        } else {
          // Marcar como reconciliado mesmo sem mudança
          await prisma.paymentHistory.update({
            where: { id: payment.id },
            data: {
              reconciledAt: new Date()
            }
          })
        }

        result.reconciledCount++

      } catch (error) {
        console.error(`❌ Erro ao reconciliar pagamento ${payment.id}:`, error)
        result.errors.push(`Pagamento ${payment.id}: ${error.message}`)
        result.errorCount++
      }

      // Pequena pausa para não sobrecarregar a API
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    return result

  } catch (error) {
    console.error('❌ Erro na reconciliação de pagamentos existentes:', error)
    result.errors.push(`Erro geral: ${error.message}`)
    result.errorCount++
    return result
  }
}

/**
 * Buscar novos pagamentos no Stripe que não estão na nossa base de dados
 */
async function findNewPaymentsFromStripe(): Promise<Partial<ReconciliationResult>> {
  const result = {
    newPaymentsFound: 0,
    errorCount: 0,
    errors: []
  }

  try {
    const stripe = await createStripeInstance()

    // Buscar PaymentIntents recentes (últimos 7 dias)
    const sevenDaysAgo = Math.floor((Date.now() - 7 * 24 * 60 * 60 * 1000) / 1000)
    
    const paymentIntents = await stripe.paymentIntents.list({
      created: { gte: sevenDaysAgo },
      limit: 100
    })

    console.log(`🔍 Verificando ${paymentIntents.data.length} PaymentIntents do Stripe`)

    for (const pi of paymentIntents.data) {
      try {
        // Verificar se já existe na nossa base de dados
        const existing = await prisma.paymentHistory.findFirst({
          where: { stripePaymentIntentId: pi.id }
        })

        if (!existing && pi.status === 'succeeded') {
          // Criar novo registo
          const metadata = pi.metadata || {}
          
          const detectedType = determinePaymentType(pi)
          let paymentType: 'SUBSCRIPTION' | 'MARKETPLACE' | 'REPAIR' | 'ADDON' | 'UPGRADE' = 'REPAIR'

          if (detectedType === 'subscription') paymentType = 'SUBSCRIPTION'
          else if (detectedType === 'marketplace') paymentType = 'MARKETPLACE'
          else if (detectedType === 'repair') paymentType = 'REPAIR'
          else if (metadata.type?.includes('addon')) paymentType = 'ADDON'
          else if (metadata.type?.includes('upgrade')) paymentType = 'UPGRADE'

          console.log(`🔍 Tipo detectado para ${pi.id}: ${detectedType} → ${paymentType}`)

          const amount = pi.amount / 100
          const platformFee = amount * 0.05
          const netAmount = amount - platformFee

          await recordPaymentHistory({
            stripePaymentIntentId: pi.id,
            stripeCustomerId: pi.customer as string,
            amount,
            currency: pi.currency.toUpperCase(),
            status: 'SUCCEEDED',
            paymentType,
            description: pi.description || `Pagamento ${paymentType.toLowerCase()}`,
            customerEmail: metadata.customerEmail,
            customerName: metadata.customerName,
            lojistId: metadata.lojistId,
            lojistName: metadata.lojistName,
            subscriptionId: metadata.subscriptionId,
            orderId: metadata.orderId,
            repairId: metadata.repairId,
            platformFee,
            netAmount,
            metadata
          })

          result.newPaymentsFound++
          console.log(`✅ Novo pagamento encontrado e registado: ${pi.id}`)
        }

      } catch (error) {
        console.error(`❌ Erro ao processar PaymentIntent ${pi.id}:`, error)
        result.errors.push(`PaymentIntent ${pi.id}: ${error.message}`)
        result.errorCount++
      }
    }

    return result

  } catch (error) {
    console.error('❌ Erro ao buscar novos pagamentos:', error)
    result.errors.push(`Erro geral: ${error.message}`)
    result.errorCount++
    return result
  }
}

/**
 * Reconciliar pagamentos de subscrições
 */
async function reconcileSubscriptionPayments(): Promise<Partial<ReconciliationResult>> {
  const result = {
    reconciledCount: 0,
    errorCount: 0,
    errors: []
  }

  try {
    const stripe = await createStripeInstance()

    // Buscar subscrições ativas
    const activeSubscriptions = await prisma.subscription.findMany({
      where: {
        status: { in: ['ACTIVE', 'TRIALING', 'PAST_DUE'] }
      },
      take: 50
    })

    console.log(`🔍 Verificando ${activeSubscriptions.length} subscrições ativas`)

    for (const subscription of activeSubscriptions) {
      try {
        if (!subscription.stripeSubscriptionId) continue

        // Buscar subscrição no Stripe
        const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripeSubscriptionId)

        // Buscar faturas recentes desta subscrição
        const invoices = await stripe.invoices.list({
          subscription: subscription.stripeSubscriptionId,
          limit: 10
        })

        for (const invoice of invoices.data) {
          // Verificar se já temos este pagamento registado
          const existing = await prisma.paymentHistory.findFirst({
            where: { stripeInvoiceId: invoice.id }
          })

          if (!existing && invoice.status === 'paid') {
            const amount = (invoice.amount_paid || 0) / 100
            const platformFee = amount * 0.05
            const netAmount = amount - platformFee

            await recordPaymentHistory({
              stripeInvoiceId: invoice.id,
              stripePaymentIntentId: invoice.payment_intent as string,
              stripeCustomerId: invoice.customer as string,
              amount,
              currency: invoice.currency?.toUpperCase() || 'EUR',
              status: 'SUCCEEDED',
              paymentType: 'SUBSCRIPTION',
              description: invoice.description || 'Pagamento de subscrição',
              customerEmail: invoice.customer_email,
              customerName: invoice.customer_name,
              subscriptionId: subscription.id,
              platformFee,
              netAmount,
              metadata: invoice.metadata
            })

            result.reconciledCount++
            console.log(`✅ Pagamento de subscrição registado: ${invoice.id}`)
          }
        }

      } catch (error) {
        console.error(`❌ Erro ao reconciliar subscrição ${subscription.id}:`, error)
        result.errors.push(`Subscrição ${subscription.id}: ${error.message}`)
        result.errorCount++
      }
    }

    return result

  } catch (error) {
    console.error('❌ Erro na reconciliação de subscrições:', error)
    result.errors.push(`Erro geral: ${error.message}`)
    result.errorCount++
    return result
  }
}

/**
 * Reconciliar um pagamento específico
 */
export async function reconcileSpecificPayment(paymentId: string): Promise<{
  success: boolean
  message: string
  updatedData?: any
}> {
  try {
    const payment = await prisma.paymentHistory.findUnique({
      where: { id: paymentId }
    })

    if (!payment) {
      return {
        success: false,
        message: 'Pagamento não encontrado'
      }
    }

    const stripe = await createStripeInstance()
    let stripeData = null
    let newStatus = payment.status
    let failureReason = payment.failureReason

    // Buscar no Stripe
    if (payment.stripePaymentIntentId) {
      stripeData = await stripe.paymentIntents.retrieve(payment.stripePaymentIntentId)
      
      switch (stripeData.status) {
        case 'succeeded':
          newStatus = 'SUCCEEDED'
          break
        case 'processing':
          newStatus = 'PROCESSING'
          break
        case 'requires_payment_method':
        case 'requires_confirmation':
        case 'requires_action':
          newStatus = 'PENDING'
          break
        case 'canceled':
          newStatus = 'CANCELLED'
          break
        default:
          if (stripeData.status.includes('failed')) {
            newStatus = 'FAILED'
            failureReason = stripeData.last_payment_error?.message || 'Pagamento falhado'
          }
      }
    } else if (payment.stripeInvoiceId) {
      stripeData = await stripe.invoices.retrieve(payment.stripeInvoiceId)
      
      switch (stripeData.status) {
        case 'paid':
          newStatus = 'SUCCEEDED'
          break
        case 'open':
        case 'draft':
          newStatus = 'PENDING'
          break
        case 'void':
        case 'uncollectible':
          newStatus = 'FAILED'
          failureReason = 'Fatura cancelada ou não cobrável'
          break
      }
    }

    // Atualizar
    const updatedPayment = await prisma.paymentHistory.update({
      where: { id: paymentId },
      data: {
        status: newStatus,
        failureReason,
        reconciledAt: new Date()
      }
    })

    return {
      success: true,
      message: `Pagamento reconciliado: ${payment.status} → ${newStatus}`,
      updatedData: updatedPayment
    }

  } catch (error) {
    console.error('❌ Erro ao reconciliar pagamento específico:', error)
    return {
      success: false,
      message: `Erro na reconciliação: ${error.message}`
    }
  }
}

/**
 * Determinar tipo de pagamento baseado nos metadados e contexto
 */
function determinePaymentType(paymentIntent: Stripe.PaymentIntent): string {
  const metadata = paymentIntent.metadata || {}

  console.log('🔍 Determinando tipo de pagamento:', {
    id: paymentIntent.id,
    metadata,
    description: paymentIntent.description
  })

  // Verificar metadados diretos
  if (metadata.type) {
    console.log('✅ Tipo encontrado nos metadados:', metadata.type)
    return metadata.type
  }

  // Inferir baseado em outros campos
  if (metadata.subscriptionId || metadata.planId || metadata.billingCycle) {
    console.log('✅ Tipo inferido: subscription (subscriptionId/planId/billingCycle)')
    return 'subscription'
  }

  if (metadata.repairId) {
    console.log('✅ Tipo inferido: repair (repairId)')
    return 'repair'
  }

  if (metadata.orderIds || metadata.orderId) {
    console.log('✅ Tipo inferido: marketplace (orderIds/orderId)')
    return 'marketplace'
  }

  // Verificar descrição
  const description = paymentIntent.description?.toLowerCase() || ''
  if (description.includes('subscription') || description.includes('subscrição') || description.includes('plano')) {
    console.log('✅ Tipo inferido: subscription (descrição)')
    return 'subscription'
  }

  if (description.includes('repair') || description.includes('reparação')) {
    console.log('✅ Tipo inferido: repair (descrição)')
    return 'repair'
  }

  if (description.includes('marketplace') || description.includes('compra')) {
    console.log('✅ Tipo inferido: marketplace (descrição)')
    return 'marketplace'
  }

  // Verificar valor para inferir tipo (subscrições geralmente são valores baixos)
  const amount = paymentIntent.amount / 100 // Converter de centavos
  if (amount <= 10) {
    console.log('✅ Tipo inferido: subscription (valor baixo)')
    return 'subscription'
  }

  console.log('⚠️ Tipo não identificado, usando: unknown')
  return 'unknown'
}

/**
 * Corrigir tipos de pagamentos existentes na base de dados
 */
export async function fixExistingPaymentTypes(): Promise<{
  success: boolean
  correctedCount: number
  errors: string[]
}> {
  const result = {
    success: false,
    correctedCount: 0,
    errors: []
  }

  try {
    console.log('🔧 Corrigindo tipos de pagamentos existentes...')

    const stripe = await createStripeInstance()

    // Buscar pagamentos com tipo incorreto (REPAIR mas valor baixo)
    const suspiciousPayments = await prisma.paymentHistory.findMany({
      where: {
        paymentType: 'REPAIR',
        amount: {
          lte: 10 // Valores até €10 são provavelmente subscrições
        }
      }
    })

    console.log(`🔍 Encontrados ${suspiciousPayments.length} pagamentos suspeitos`)

    for (const payment of suspiciousPayments) {
      try {
        if (payment.stripePaymentIntentId) {
          // Buscar PaymentIntent no Stripe
          const paymentIntent = await stripe.paymentIntents.retrieve(payment.stripePaymentIntentId)

          // Determinar tipo correto
          const correctType = determinePaymentType(paymentIntent)

          if (correctType === 'subscription') {
            // Atualizar tipo para SUBSCRIPTION
            await prisma.paymentHistory.update({
              where: { id: payment.id },
              data: { paymentType: 'SUBSCRIPTION' }
            })

            result.correctedCount++
            console.log(`✅ Corrigido ${payment.stripePaymentIntentId}: REPAIR → SUBSCRIPTION`)
          }
        }
      } catch (error) {
        console.error(`❌ Erro ao corrigir pagamento ${payment.id}:`, error)
        result.errors.push(`Erro no pagamento ${payment.id}: ${error.message}`)
      }
    }

    result.success = true
    console.log(`✅ Correção concluída: ${result.correctedCount} pagamentos corrigidos`)

    return result

  } catch (error) {
    console.error('❌ Erro na correção de tipos:', error)
    result.errors.push(`Erro geral: ${error.message}`)
    return result
  }
}
