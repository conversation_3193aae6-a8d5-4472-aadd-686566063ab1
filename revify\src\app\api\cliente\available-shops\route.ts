import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { calculatePostalCodeDistance, estimateDistanceByDistrict, filterShopsByRadius, suggestAlternativeRadius } from '@/lib/postal-codes'
import { geocodePostalCode, calculateRealDistance, formatDistance } from '@/lib/geocoding'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const problemTypeId = searchParams.get('problemTypeId')
    const deviceId = searchParams.get('deviceId')
    const customerLocation = searchParams.get('location') // Para cálculo de distância
    const customerPostalCode = searchParams.get('postalCode') // Código postal do cliente
    const maxRadius = parseInt(searchParams.get('radius') || '10') // Raio máximo em km (padrão 10km)

    if (!categoryId || !problemTypeId) {
      return NextResponse.json(
        { message: 'Categoria e tipo de problema são obrigatórios' },
        { status: 400 }
      )
    }

    console.log('Buscando lojas para:', { categoryId, problemTypeId, deviceId })

    // Buscar tipos de problema similares se for problema de ecrã
    let problemTypeIds = [problemTypeId]

    const currentProblemType = await prisma.problemType.findUnique({
      where: { id: problemTypeId }
    })

    if (currentProblemType && currentProblemType.name.toLowerCase().includes('ecrã')) {
      console.log('🔍 Problema de ecrã detectado, buscando tipos similares...')
      const similarProblems = await prisma.problemType.findMany({
        where: {
          OR: [
            { name: { contains: 'ecrã', mode: 'insensitive' } },
            { name: { contains: 'screen', mode: 'insensitive' } }
          ]
        }
      })

      problemTypeIds = similarProblems.map(p => p.id)
      console.log('📋 Tipos de problema incluídos:', similarProblems.map(p => p.name))
    }

    // Buscar lojistas que têm preços para esta categoria e tipo de problema
    // E que trabalham com a categoria/marca/problema específico (se definido nas especialidades)
    const repairShops = await prisma.user.findMany({
      where: {
        role: 'REPAIR_SHOP',
        AND: [
          // Deve ter preços definidos
          {
            repairShopPrices: {
              some: {
                OR: [
                  {
                    deviceModelId: deviceId,
                    problemTypeId: { in: problemTypeIds },
                    isActive: true
                  },
                  {
                    categoryId: categoryId,
                    problemTypeId: { in: problemTypeIds },
                    deviceModelId: null,
                    isActive: true
                  }
                ]
              }
            }
          },
          // Filtrar por especialidades (se o lojista definiu especialidades)
          {
            OR: [
              // Lojista não definiu especialidades (aceita tudo)
              {
                profile: {
                  OR: [
                    { workingCategories: { equals: null } },
                    { workingCategories: { equals: [] } }
                  ]
                }
              },
              // Lojista trabalha com esta categoria
              {
                profile: {
                  workingCategories: {
                    has: categoryId
                  }
                }
              }
            ]
          }
        ]
      },
      include: {
        profile: true,
        subscription: {
          include: {
            plan: true
          }
        },
        repairShopPrices: {
          where: {
            OR: [
              {
                deviceModelId: deviceId,
                problemTypeId: { in: problemTypeIds },
                isActive: true
              },
              {
                categoryId: categoryId,
                problemTypeId: { in: problemTypeIds },
                deviceModelId: null,
                isActive: true
              }
            ]
          }
        },
        repairShopRepairs: {
          include: {
            review: true
          }
        }
      }
    })

    console.log('Lojas encontradas:', repairShops.length)

    // Se há código postal do cliente, filtrar por raio primeiro
    let shopsInRadius = repairShops
    let radiusUsed = maxRadius
    let expandedSearch = false

    if (customerPostalCode) {
      console.log(`🔍 Filtrando lojas num raio de ${maxRadius}km do código postal ${customerPostalCode}`)

      const shopsWithDistance = filterShopsByRadius(customerPostalCode, repairShops, maxRadius)

      if (shopsWithDistance.length === 0) {
        console.log(`❌ Nenhuma loja encontrada num raio de ${maxRadius}km`)

        // Tentar raios maiores
        const alternativeRadii = suggestAlternativeRadius(maxRadius)

        for (const radius of alternativeRadii) {
          const expandedShops = filterShopsByRadius(customerPostalCode, repairShops, radius)
          if (expandedShops.length > 0) {
            console.log(`✅ Encontradas ${expandedShops.length} lojas num raio de ${radius}km`)
            shopsInRadius = expandedShops.map(item => item.shop)
            radiusUsed = radius
            expandedSearch = true
            break
          }
        }
      } else {
        console.log(`✅ Encontradas ${shopsWithDistance.length} lojas num raio de ${maxRadius}km`)
        shopsInRadius = shopsWithDistance.map(item => item.shop)
      }
    }

    // Processar dados das lojas
    const processedShops = await Promise.all(shopsInRadius.map(async (shop) => {
      // Encontrar preço específico para este serviço
      let price = null
      let estimatedTime = null

      // Primeiro tentar preço específico para o modelo
      let specificPrice = shop.repairShopPrices.find(p =>
        p.deviceModelId === deviceId && problemTypeIds.includes(p.problemTypeId)
      )

      // Se não houver, usar preço geral da categoria
      if (!specificPrice) {
        specificPrice = shop.repairShopPrices.find(p =>
          p.categoryId === categoryId && problemTypeIds.includes(p.problemTypeId) && !p.deviceModelId
        )
      }

      if (specificPrice) {
        price = Number(specificPrice.price)
        estimatedTime = specificPrice.estimatedTime || 60
      } else {
        // Se não há preço específico, pular esta loja
        return null
      }

      // Usar rating do Profile se disponível, senão calcular das reviews
      let rating = 0
      let reviewCount = 0

      if (shop.profile?.averageRating && shop.profile?.totalReviews) {
        // Usar dados já calculados do Profile
        rating = shop.profile.averageRating
        reviewCount = shop.profile.totalReviews
      } else {
        // Calcular das reviews diretamente
        const reviews = shop.repairShopRepairs
          .map(repair => repair.review)
          .filter(review => review !== null)

        reviewCount = reviews.length

        if (reviewCount > 0) {
          const totalRating = reviews.reduce((sum, review) => sum + Number(review.rating), 0)
          rating = totalRating / reviewCount
        } else {
          // Se não há reviews, usar rating padrão baixo para lojas novas
          rating = 3.0
          reviewCount = 0
        }
      }

      // Calcular distância real usando Google APIs
      let distance = 15 // Distância padrão
      let distanceInMeters = null // Distância em metros para formatação
      let travelTime = 0 // Tempo de viagem em minutos

      if (customerPostalCode && shop.profile?.latitude && shop.profile?.longitude) {
        // Método 1: Geocodificar código postal do cliente e usar Distance Matrix API
        const customerCoords = await geocodePostalCode(customerPostalCode)

        if (customerCoords) {
          const realDistance = await calculateRealDistance(
            customerCoords.latitude,
            customerCoords.longitude,
            shop.profile.latitude,
            shop.profile.longitude
          )

          if (realDistance) {
            distance = realDistance.distance
            distanceInMeters = realDistance.distanceInMeters
            travelTime = realDistance.duration
          }
        }
      } else if (customerPostalCode && shop.profile?.postalCode) {
        // Método 2: Fallback para cálculo por códigos postais
        const preciseDistance = calculatePostalCodeDistance(customerPostalCode, shop.profile.postalCode)

        if (preciseDistance !== null) {
          distance = preciseDistance
        } else {
          // Método 3: Fallback para estimativa por distrito
          distance = estimateDistanceByDistrict(customerPostalCode, shop.profile.postalCode)
        }
      } else if (customerLocation && shop.profile?.city) {
        // Método 4: Fallback básico por cidade
        const cityDistance = customerLocation.toLowerCase() === shop.profile.city.toLowerCase() ?
          Math.random() * 10 + 2 : Math.random() * 30 + 10
        distance = Math.round(cityDistance * 10) / 10
      }

      return {
        id: shop.id,
        name: shop.profile?.companyName || shop.name || 'Loja de Reparações',
        description: shop.profile?.description || 'Especialista em reparações',
        rating: Math.round(rating * 10) / 10,
        reviewCount: reviewCount,
        price: price,
        estimatedTime: estimatedTime || 120, // Retornar número em minutos
        estimatedTimeFormatted: estimatedTime && !isNaN(estimatedTime) ?
          (estimatedTime >= 60 ?
            `${Math.floor(estimatedTime / 60)}h ${estimatedTime % 60}min` :
            `${estimatedTime}min`) :
          'A consultar',
        distance: distance,
        distanceFormatted: formatDistance(distance, distanceInMeters), // Distância formatada (metros ou km)
        travelTime: travelTime, // Tempo de viagem real em minutos
        serviceRadius: shop.profile?.serviceRadius || 10,
        phone: shop.profile?.phone || '',
        // Badges de verificação
        isVerified: shop.isVerified || false,
        isCertified: shop.subscription?.plan?.certifiedBadge || false,
        isFeatured: shop.isFeatured || false,
        specialties: {
          categories: shop.profile?.workingCategories || [],
          brands: shop.profile?.workingBrands || [],
          problems: shop.profile?.workingProblems || []
        },
        availability: checkShopAvailability(shop.profile?.businessHours)
      }
    })).then(shops => shops.filter(shop => shop !== null)) // Remover lojas sem preços

    // Ordenar por critérios de matching (distância, rating, preço)
    const sortedShops = processedShops.sort((a, b) => {
      // Primeiro por distância (mais próximo primeiro)
      if (a.distance !== b.distance) {
        return a.distance - b.distance
      }
      
      // Depois por rating (maior primeiro)
      if (a.rating !== b.rating) {
        return b.rating - a.rating
      }
      
      // Por último por preço (menor primeiro)
      return a.price - b.price
    })

    return NextResponse.json({
      shops: sortedShops,
      total: sortedShops.length,
      searchCriteria: {
        categoryId,
        problemTypeId,
        deviceId,
        customerLocation,
        customerPostalCode,
        radiusUsed,
        expandedSearch
      },
      radiusInfo: {
        requested: maxRadius,
        used: radiusUsed,
        expanded: expandedSearch,
        availableExpansions: customerPostalCode ? suggestAlternativeRadius(radiusUsed) : []
      }
    })

  } catch (error) {
    console.error('Erro ao buscar lojas disponíveis:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function checkShopAvailability(businessHours: any) {
  const now = new Date()
  const currentDay = now.getDay() // 0 = domingo, 1 = segunda, etc.
  const currentTime = now.getHours() * 60 + now.getMinutes() // minutos desde meia-noite

  // Mapear dia da semana para chave do businessHours
  const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const todayKey = dayKeys[currentDay]

  // Horários padrão se não estiverem definidos
  const defaultHours = {
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '18:00', closed: false },
    friday: { open: '09:00', close: '18:00', closed: false },
    saturday: { open: '09:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: true }
  }

  const hours = businessHours || defaultHours
  const todayHours = hours[todayKey]

  if (!todayHours || todayHours.closed) {
    // Loja fechada hoje, encontrar próximo dia aberto
    const nextAvailable = findNextAvailableDay(hours, currentDay)
    return {
      isOpen: false,
      nextAvailable: nextAvailable
    }
  }

  // Converter horários para minutos
  const [openHour, openMin] = todayHours.open.split(':').map(Number)
  const [closeHour, closeMin] = todayHours.close.split(':').map(Number)
  const openTime = openHour * 60 + openMin
  const closeTime = closeHour * 60 + closeMin

  // Verificar se há horário de break/almoço
  let isInBreak = false
  if (todayHours.breakStart && todayHours.breakEnd) {
    const [breakStartHour, breakStartMin] = todayHours.breakStart.split(':').map(Number)
    const [breakEndHour, breakEndMin] = todayHours.breakEnd.split(':').map(Number)
    const breakStartTime = breakStartHour * 60 + breakStartMin
    const breakEndTime = breakEndHour * 60 + breakEndMin

    isInBreak = currentTime >= breakStartTime && currentTime <= breakEndTime
  }

  const isOpen = currentTime >= openTime && currentTime <= closeTime && !isInBreak

  if (isOpen) {
    return {
      isOpen: true,
      nextAvailable: null
    }
  } else {
    // Loja fechada agora, calcular próxima abertura
    let nextAvailable
    if (currentTime < openTime) {
      // Ainda não abriu hoje
      const nextOpen = new Date()
      nextOpen.setHours(openHour, openMin, 0, 0)
      nextAvailable = nextOpen.toISOString()
    } else {
      // Já fechou hoje, próximo dia útil
      nextAvailable = findNextAvailableDay(hours, currentDay)
    }

    return {
      isOpen: false,
      nextAvailable: nextAvailable
    }
  }
}

function findNextAvailableDay(businessHours: any, currentDay: number) {
  const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']

  for (let i = 1; i <= 7; i++) {
    const nextDay = (currentDay + i) % 7
    const dayKey = dayKeys[nextDay]
    const dayHours = businessHours[dayKey]

    if (dayHours && !dayHours.closed) {
      const nextDate = new Date()
      nextDate.setDate(nextDate.getDate() + i)
      const [openHour, openMin] = dayHours.open.split(':').map(Number)
      nextDate.setHours(openHour, openMin, 0, 0)
      return nextDate.toISOString()
    }
  }

  // Se não encontrar nenhum dia aberto nos próximos 7 dias, retornar amanhã
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(9, 0, 0, 0)
  return tomorrow.toISOString()
}
