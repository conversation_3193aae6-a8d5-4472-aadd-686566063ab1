import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { recordPaymentHistory } from '@/lib/payments/payment-history'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { planId, billingCycle, paymentMethod = 'card' } = await request.json()

    if (!planId || !billingCycle) {
      return NextResponse.json(
        { message: 'Plano e ciclo de faturação são obrigatórios' },
        { status: 400 }
      )
    }

    if (!['card', 'multibanco'].includes(paymentMethod)) {
      return NextResponse.json(
        { message: 'Método de pagamento inválido. Use "card" ou "multibanco"' },
        { status: 400 }
      )
    }

    // Verificar se o plano existe
    const plan = await prisma.subscriptionPlan.findFirst({
      where: { 
        id: planId, 
        isActive: true 
      }
    })

    if (!plan) {
      return NextResponse.json(
        { message: 'Plano não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se já tem subscrição ativa
    const existingSubscription = await prisma.subscription.findFirst({
      where: { 
        userId: session.user.id, 
        status: 'ACTIVE' 
      },
      include: {
        plan: true
      }
    })

    // Se já tem subscrição ativa e é o mesmo plano, bloquear
    if (existingSubscription && existingSubscription.planId === planId) {
      return NextResponse.json(
        { message: 'Você já possui este plano ativo' },
        { status: 400 }
      )
    }

    const amount = billingCycle === 'MONTHLY' ? Number(plan.monthlyPrice) : Number(plan.yearlyPrice)
    const description = `Upgrade para ${plan.name} - ${billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}`

    // Usar a API unificada de pagamentos V2
    const paymentIntentResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/payments/create-payment-intent-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount,
        currency: 'EUR',
        paymentMethod,
        metadata: {
          type: 'upgrade',
          planId: plan.id,
          planName: plan.name,
          billingCycle,
          currentPlanId: existingSubscription?.planId || null,
          description,
          customerEmail: session.user.email,
          customerName: session.user.name,
          lojistId: session.user.id,
          lojistName: session.user.name
        },
        customerEmail: session.user.email,
        customerName: session.user.name,
        returnUrl: paymentMethod === 'card' ? undefined : `${process.env.NEXTAUTH_URL}/lojista/upgrade/success`,
        confirmationMethod: 'automatic'
      })
    })

    const paymentIntentData = await paymentIntentResponse.json()

    if (!paymentIntentResponse.ok) {
      console.error('Erro ao criar PaymentIntent para upgrade:', paymentIntentData)
      return NextResponse.json(
        { 
          message: paymentIntentData.error || 'Erro ao criar pagamento',
          details: paymentIntentData.details 
        },
        { status: 400 }
      )
    }

    // Se for Multibanco e tiver referência
    if (paymentMethod === 'multibanco' && paymentIntentData.multibanco) {
      return NextResponse.json({
        success: true,
        type: 'multibanco_reference',
        multibanco: paymentIntentData.multibanco,
        paymentIntent: paymentIntentData.paymentIntent,
        plan: {
          id: plan.id,
          name: plan.name,
          amount: amount
        },
        redirectUrl: `/lojista/upgrade/success?multibanco=true&entity=${paymentIntentData.multibanco.entity}&reference=${paymentIntentData.multibanco.reference}&amount=${amount}&plan=${plan.id}`
      })
    }

    // Para cartão, retornar dados do PaymentIntent
    return NextResponse.json({
      success: true,
      type: 'payment_intent',
      paymentIntent: paymentIntentData.paymentIntent,
      clientSecret: paymentIntentData.paymentIntent.client_secret,
      plan: {
        id: plan.id,
        name: plan.name,
        amount: amount,
        billingCycle
      }
    })

  } catch (error) {
    console.error('Erro ao fazer upgrade unificado:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}