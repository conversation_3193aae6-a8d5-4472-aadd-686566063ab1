'use client'

import { useState } from 'react'
import UnifiedPaymentForm from './UnifiedPaymentForm'
import StripePaymentElement from './StripePaymentElement'
import MultibancoReference from './MultibancoReference'
import { CheckCircle, ArrowLeft } from 'lucide-react'

interface PaymentExampleProps {
  amount: number
  currency?: string
  description: string
  metadata?: Record<string, string>
  customerEmail?: string
  customerName?: string
  onComplete?: (result: any) => void
  onCancel?: () => void
}

export default function PaymentExample({
  amount,
  currency = 'EUR',
  description,
  metadata = {},
  customerEmail,
  customerName,
  onComplete,
  onCancel
}: PaymentExampleProps) {
  const [paymentMode, setPaymentMode] = useState<'unified' | 'specific'>('unified')
  const [specificMethod, setSpecificMethod] = useState<'card' | 'multibanco' | 'klarna'>('card')
  const [paymentResult, setPaymentResult] = useState<any>(null)
  const [showMultibancoRef, setShowMultibancoRef] = useState(false)

  const handlePaymentSuccess = (result: any) => {
    console.log('Pagamento bem-sucedido:', result)
    setPaymentResult(result)

    // Se for referência Multibanco, mostrar os dados
    if (result.type === 'multibanco_reference' && result.multibanco) {
      setShowMultibancoRef(true)
    } else {
      // Para outros tipos, chamar callback
      onComplete?.(result)
    }
  }

  const handlePaymentError = (error: string) => {
    console.error('Erro no pagamento:', error)
    alert(`Erro no pagamento: ${error}`)
  }

  const resetPayment = () => {
    setPaymentResult(null)
    setShowMultibancoRef(false)
  }

  // Se mostrar referência Multibanco
  if (showMultibancoRef && paymentResult?.multibanco) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="mb-6">
          <button
            onClick={resetPayment}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </button>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Referência Multibanco Gerada
          </h2>
          <p className="text-gray-600">
            Use os dados abaixo para efetuar o pagamento
          </p>
        </div>

        <MultibancoReference
          entity={paymentResult.multibanco.entity}
          reference={paymentResult.multibanco.reference}
          amount={amount}
          currency={currency}
          expiresAt={paymentResult.multibanco.expires_at}
          description={description}
          onCopy={() => console.log('Dados copiados')}
        />

        <div className="mt-6 text-center">
          <button
            onClick={() => onComplete?.(paymentResult)}
            className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700"
          >
            Continuar
          </button>
        </div>
      </div>
    )
  }

  // Se pagamento foi bem-sucedido (não Multibanco)
  if (paymentResult && paymentResult.type === 'payment_succeeded') {
    return (
      <div className="max-w-md mx-auto p-6 text-center">
        <div className="mb-6">
          <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Pagamento Concluído
          </h2>
          <p className="text-gray-600">
            O seu pagamento foi processado com sucesso
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="text-sm text-gray-600 mb-1">Valor pago:</div>
          <div className="text-2xl font-bold text-gray-900">
            {new Intl.NumberFormat('pt-PT', {
              style: 'currency',
              currency: currency
            }).format(amount)}
          </div>
          <div className="text-sm text-gray-500 mt-1">{description}</div>
        </div>

        <button
          onClick={() => onComplete?.(paymentResult)}
          className="w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700"
        >
          Continuar
        </button>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Exemplo de Pagamento Stripe
        </h1>
        <p className="text-gray-600">
          Demonstração dos componentes de pagamento com suporte a cartão, Multibanco e Klarna
        </p>
      </div>

      {/* Mode Selection */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Escolha o modo de pagamento:
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setPaymentMode('unified')}
            className={`p-4 border-2 rounded-lg text-left transition-colors ${
              paymentMode === 'unified'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <h3 className="font-semibold text-gray-900 mb-2">
              Formulário Unificado
            </h3>
            <p className="text-sm text-gray-600">
              Permite ao utilizador escolher entre cartão, Multibanco e Klarna
            </p>
          </button>

          <button
            onClick={() => setPaymentMode('specific')}
            className={`p-4 border-2 rounded-lg text-left transition-colors ${
              paymentMode === 'specific'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <h3 className="font-semibold text-gray-900 mb-2">
              Método Específico
            </h3>
            <p className="text-sm text-gray-600">
              Formulário para um método de pagamento específico
            </p>
          </button>
        </div>
      </div>

      {/* Method Selection for Specific Mode */}
      {paymentMode === 'specific' && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Escolha o método específico:
          </h3>
          
          <div className="flex space-x-4">
            <button
              onClick={() => setSpecificMethod('card')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                specificMethod === 'card'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Cartão
            </button>
            <button
              onClick={() => setSpecificMethod('multibanco')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                specificMethod === 'multibanco'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Multibanco
            </button>
            <button
              onClick={() => setSpecificMethod('klarna')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                specificMethod === 'klarna'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Klarna
            </button>
          </div>
        </div>
      )}

      {/* Payment Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Form */}
        <div>
          {paymentMode === 'unified' ? (
            <UnifiedPaymentForm
              amount={amount}
              currency={currency}
              description={description}
              metadata={metadata}
              customerEmail={customerEmail}
              customerName={customerName}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              onCancel={onCancel}
              returnUrl={`${window.location.origin}/payment/success`}
            />
          ) : (
            <StripePaymentElement
              amount={amount}
              currency={currency}
              paymentMethod={specificMethod}
              description={description}
              metadata={metadata}
              customerEmail={customerEmail}
              customerName={customerName}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              returnUrl={`${window.location.origin}/payment/success`}
            />
          )}
        </div>

        {/* Info Panel */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Informações do Pagamento
          </h3>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Valor:</span>
              <span className="font-semibold">
                {new Intl.NumberFormat('pt-PT', {
                  style: 'currency',
                  currency: currency
                }).format(amount)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Moeda:</span>
              <span className="font-semibold">{currency}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Descrição:</span>
              <span className="font-semibold">{description}</span>
            </div>
            
            {customerEmail && (
              <div className="flex justify-between">
                <span className="text-gray-600">Email:</span>
                <span className="font-semibold">{customerEmail}</span>
              </div>
            )}
            
            {customerName && (
              <div className="flex justify-between">
                <span className="text-gray-600">Nome:</span>
                <span className="font-semibold">{customerName}</span>
              </div>
            )}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">
              Métodos Suportados:
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Cartão de crédito/débito (Visa, Mastercard, Amex)</li>
              <li>• Multibanco (referência para pagamento)</li>
              <li>• Klarna (pague depois ou em prestações)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Cancel Button */}
      {onCancel && (
        <div className="mt-8 text-center">
          <button
            onClick={onCancel}
            className="px-6 py-2 text-gray-600 hover:text-gray-900 font-medium"
          >
            Cancelar
          </button>
        </div>
      )}
    </div>
  )
}
