import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar subscrição do usuário
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: session.user.id,
        status: {
          in: ['ACTIVE', 'INCOMPLETE', 'PAST_DUE', 'TRIALING', 'UNPAID']
        }
      }
    })

    if (!subscription || !subscription.stripeCustomerId) {
      return NextResponse.json({
        paymentMethods: [],
        message: 'Nenhum método de pagamento encontrado'
      })
    }

    // Buscar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    if (!stripeSecretSetting?.value) {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretSetting.value)

    try {
      // Buscar métodos de pagamento do customer
      const paymentMethods = await stripe.paymentMethods.list({
        customer: subscription.stripeCustomerId,
        type: 'card'
      })

      // Buscar customer para informações adicionais
      const customer = await stripe.customers.retrieve(subscription.stripeCustomerId)

      const formattedPaymentMethods = paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        card: pm.card ? {
          brand: pm.card.brand,
          last4: pm.card.last4,
          exp_month: pm.card.exp_month,
          exp_year: pm.card.exp_year,
          funding: pm.card.funding
        } : null,
        created: new Date(pm.created * 1000).toISOString()
      }))

      return NextResponse.json({
        paymentMethods: formattedPaymentMethods,
        customer: {
          id: customer.id,
          email: customer.email,
          name: customer.name,
          default_source: customer.default_source
        }
      })

    } catch (stripeError: any) {
      console.error('Erro do Stripe:', stripeError)
      
      if (stripeError.code === 'resource_missing') {
        return NextResponse.json({
          paymentMethods: [],
          message: 'Customer não encontrado no Stripe'
        })
      }

      return NextResponse.json(
        { message: 'Erro ao buscar métodos de pagamento' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Erro ao buscar métodos de pagamento:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
