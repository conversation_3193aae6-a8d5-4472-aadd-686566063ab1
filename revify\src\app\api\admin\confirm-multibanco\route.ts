import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { referenceId, subscriptionId } = await request.json()

    if (!referenceId && !subscriptionId) {
      return NextResponse.json(
        { message: 'ID da referência ou subscrição é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar referência Multibanco
    let multibancoRef
    if (referenceId) {
      multibancoRef = await prisma.multibancoReference.findUnique({
        where: { id: referenceId }
      })
    } else {
      multibancoRef = await prisma.multibancoReference.findFirst({
        where: {
          orderId: subscriptionId,
          status: 'PENDING'
        },
        orderBy: { createdAt: 'desc' }
      })
    }

    if (!multibancoRef) {
      return NextResponse.json(
        { message: 'Referência Multibanco não encontrada' },
        { status: 404 }
      )
    }

    // Buscar subscrição
    const subscription = await prisma.subscription.findUnique({
      where: { id: multibancoRef.orderId },
      include: { plan: true }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Subscrição não encontrada' },
        { status: 404 }
      )
    }

    // Atualizar referência como paga
    await prisma.multibancoReference.update({
      where: { id: multibancoRef.id },
      data: {
        status: 'PAID',
        paidAt: new Date()
      }
    })

    // Atualizar pagamento pendente
    await prisma.subscriptionPayment.updateMany({
      where: {
        subscriptionId: subscription.id,
        status: 'PENDING'
      },
      data: {
        status: 'COMPLETED',
        paidAt: new Date()
      }
    })

    // Ativar subscrição
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: 'ACTIVE'
      }
    })

    console.log(`✅ Pagamento Multibanco confirmado manualmente:`, {
      subscriptionId: subscription.id,
      referenceId: multibancoRef.id,
      entity: multibancoRef.entity,
      reference: multibancoRef.reference,
      amount: multibancoRef.amount / 100,
      confirmedBy: session.user.email
    })

    return NextResponse.json({
      success: true,
      message: 'Pagamento Multibanco confirmado com sucesso',
      subscription: {
        id: subscription.id,
        status: 'ACTIVE',
        planName: subscription.plan.name
      },
      payment: {
        entity: multibancoRef.entity,
        reference: multibancoRef.reference,
        amount: multibancoRef.amount / 100,
        paidAt: new Date()
      }
    })

  } catch (error) {
    console.error('Erro ao confirmar pagamento Multibanco:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
