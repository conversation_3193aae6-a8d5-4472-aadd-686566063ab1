import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    if (!stripeSecretSetting?.value) {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretSetting.value)

    // Buscar subscrição do usuário (qualquer status)
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: session.user.id
      },
      orderBy: {
        createdAt: 'desc' // Pegar a mais recente
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Nenhuma subscrição encontrada.' },
        { status: 404 }
      )
    }

    // Se não tem stripeCustomerId, criar um customer no Stripe
    let customerId = subscription.stripeCustomerId

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: session.user.email!,
        name: session.user.name || undefined,
        metadata: {
          userId: session.user.id
        }
      })

      customerId = customer.id

      // Atualizar subscrição com o customer ID
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: { stripeCustomerId: customerId }
      })
    }

    // Criar sessão do billing portal
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/lojista/subscricao`,
    })

    return NextResponse.json({
      url: portalSession.url
    })

  } catch (error) {
    console.error('Erro ao criar billing portal:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
